# Trainer Trainees API Documentation

## Overview
This API provides endpoints to retrieve trainer-trainee relationships with customer and sport details from the ERP system.

## Files Structure
- `trainer_trainees_final.php` - Main API endpoint (recommended)
- `trainer_trainees_improved.php` - Alternative version with inline configuration
- `config.php` - Configuration file (used by final version)
- `API_DOCUMENTATION.md` - This documentation file

## Endpoints

### GET /trainer_trainees_final.php

Retrieves trainer-trainee relationships with optional filtering and pagination.

#### Query Parameters

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `trainer_name` | string | Filter by trainer name (partial match) | `?trainer_name=<PERSON>` |
| `sport_name` | string | Filter by sport name (partial match) | `?sport_name=Tennis` |
| `customer_id` | integer | Filter by specific customer ID | `?customer_id=123` |
| `customer_name` | string | Filter by customer name (partial match) | `?customer_name=Smith` |
| `subscription_type` | string | Filter by subscription type | `?subscription_type=Premium` |
| `limit` | integer | Limit number of results (max 1000) | `?limit=50` |
| `offset` | integer | Offset for pagination | `?offset=100` |
| `action` | string | Special actions (see below) | `?action=stats` |

#### Special Actions

##### Statistics Endpoint
- **URL**: `?action=stats`
- **Description**: Returns statistics about trainers, trainees, and sports
- **Example**: `/trainer_trainees_final.php?action=stats`

#### Response Format

##### Success Response
```json
{
    "status": "success",
    "message": "Trainer-trainee data retrieved successfully",
    "data": [
        {
            "trainer_name": "John Doe",
            "customer_id": 123,
            "customer_name": "Jane Smith",
            "customer_email": "<EMAIL>",
            "customer_phone": "+1234567890",
            "sport_name": "Tennis",
            "training_days": "Monday, Wednesday, Friday",
            "subscription_type": "Premium",
            "subscription_duration": "3 months",
            "number_of_sessions": 12,
            "created_at": "2024-01-15 10:30:00",
            "updated_at": "2024-01-20 14:45:00"
        }
    ],
    "timestamp": "2024-01-25 12:00:00",
    "count": 1,
    "meta": {
        "filters_applied": {
            "trainer_name": "John"
        },
        "total_results": 1,
        "pagination": {
            "limit": 50,
            "offset": 0
        }
    }
}
```

##### Error Response
```json
{
    "status": "error",
    "message": "Invalid customer_id: must be a positive integer",
    "timestamp": "2024-01-25 12:00:00",
    "error_code": 400
}
```

## Usage Examples

### Basic Usage
```bash
# Get all trainer-trainee relationships
curl "https://yourdomain.com/trainer_trainees_final.php"

# Filter by trainer name
curl "https://yourdomain.com/trainer_trainees_final.php?trainer_name=John"

# Filter by sport
curl "https://yourdomain.com/trainer_trainees_final.php?sport_name=Tennis"

# Multiple filters
curl "https://yourdomain.com/trainer_trainees_final.php?trainer_name=John&sport_name=Tennis"
```

### Pagination
```bash
# Get first 20 results
curl "https://yourdomain.com/trainer_trainees_final.php?limit=20"

# Get next 20 results
curl "https://yourdomain.com/trainer_trainees_final.php?limit=20&offset=20"
```

### Statistics
```bash
# Get system statistics
curl "https://yourdomain.com/trainer_trainees_final.php?action=stats"
```

## Configuration

### Database Configuration
Edit `config.php` to update database credentials:

```php
define('DB_HOST', 'your_host');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
define('DB_NAME', 'your_database');
```

### Environment Configuration
The API automatically detects the environment:
- **Development**: `localhost` or `127.0.0.1` - Shows detailed errors
- **Production**: Other domains - Hides error details for security

### Security Settings
- `MAX_QUERY_RESULTS`: Maximum results per query (default: 1000)
- `ENABLE_API_KEY`: Set to true to require API key authentication
- `CORS_ALLOWED_ORIGINS`: Configure allowed origins for CORS

## Error Handling

The API includes comprehensive error handling:
- Input validation errors (400)
- Database connection errors (500)
- Method not allowed errors (405)
- General server errors (500)

All errors are logged to `api_errors.log` when `LOG_ERRORS` is enabled.

## Security Features

1. **Input Sanitization**: All inputs are sanitized to prevent XSS
2. **SQL Injection Prevention**: Uses prepared statements
3. **Rate Limiting**: Configurable rate limiting (not implemented in basic version)
4. **CORS Configuration**: Configurable CORS headers
5. **Error Information**: Sensitive error details hidden in production

## Performance Considerations

1. **Database Connection**: Uses singleton pattern for connection management
2. **Query Optimization**: Indexed queries with proper JOIN operations
3. **Result Limiting**: Maximum result limits to prevent memory issues
4. **Prepared Statements**: Reusable prepared statements for better performance

## Migration from Original Code

To migrate from the original `trainer_trainees.php`:

1. Replace the original file with `trainer_trainees_final.php`
2. Create the `config.php` file with your database credentials
3. Update any client code to handle the new response format
4. Test all existing functionality

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check database credentials in `config.php`
   - Verify database server is running
   - Check network connectivity

2. **Empty Results**
   - Verify table names and column names match your database schema
   - Check if data exists in the database
   - Review filter parameters

3. **CORS Issues**
   - Update `CORS_ALLOWED_ORIGINS` in `config.php`
   - Ensure proper headers are being sent

### Debug Mode
Set `APP_DEBUG` to `true` in `config.php` to enable detailed error messages during development.
