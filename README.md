# Hemm ERP Application

## Features

### Core Features
- **User Management**: Manage user accounts, roles, and permissions.
- **Customer Management**: Handle customer registrations, profiles, and interactions.
- **Employee Management**: Manage employee records, schedules, and payroll.
- **Sport Management**: Organize sports activities, schedules, and bookings.
- **Stadium Booking**: Facilitate stadium reservations and scheduling.
- **Financial Management**: Track transactions, generate reports, and manage the treasury.

### Advanced Features
- **Multi-Language Support**: Supports both Arabic and English languages.
- **Responsive Design**: Optimized for various screen sizes and devices.
- **Animations**: Enhanced user experience with smooth animations.
- **API Integration**: Seamless integration with external services and APIs.
- **Local Storage**: Efficient data handling with local storage services.

### Technical Features
- **Flutter Framework**: Built using Flutter for cross-platform compatibility.
- **State Management**: Utilizes provider for efficient state management.
- **Custom Widgets**: Reusable and customizable widgets for consistent UI.
- **Theming**: Flexible theming options for personalized user experience.

## Getting Started

1. **Clone the repository**:
   ```bash
   git clone  https://github.com/NaderAlyEltabey/Hemma_ERP.git
   ```
2. **Install dependencies**:
   ```bash
   flutter pub get
   ```
3. **Run the application**:
   ```bash
   flutter run
   ```

## Contributing

We welcome contributions! Please follow our [contribution guidelines](CONTRIBUTING.md) to get started.

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
