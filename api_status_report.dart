// ignore_for_file: avoid_print

import 'package:http/http.dart' as http;

/// Comprehensive API Status Checker for Hemma Sports Academy ERP System
class ApiStatusChecker {
  static const String baseUrl = 'https://backend2.hemmasportacademy.com';
  static const Duration timeout = Duration(seconds: 10);

  /// Check the status of a single API endpoint
  static Future<ApiEndpointStatus> checkEndpoint(
    String url,
    String method,
  ) async {
    final startTime = DateTime.now();

    try {
      http.Response response;

      switch (method.toUpperCase()) {
        case 'GET':
          response = await http.get(Uri.parse(url)).timeout(timeout);
          break;
        case 'POST':
          response = await http.post(Uri.parse(url), body: {}).timeout(timeout);
          break;
        default:
          response = await http.get(Uri.parse(url)).timeout(timeout);
      }

      final endTime = DateTime.now();
      final responseTime = endTime.difference(startTime).inMilliseconds;

      return ApiEndpointStatus(
        url: url,
        method: method,
        statusCode: response.statusCode,
        responseTime: responseTime,
        isWorking: response.statusCode >= 200 && response.statusCode < 400,
        hasData:
            response.body.isNotEmpty &&
            response.body != '[]' &&
            response.body != '{}',
        responseSize: response.body.length,
        contentType: response.headers['content-type'] ?? 'unknown',
        error: null,
      );
    } catch (e) {
      final endTime = DateTime.now();
      final responseTime = endTime.difference(startTime).inMilliseconds;

      return ApiEndpointStatus(
        url: url,
        method: method,
        statusCode: 0,
        responseTime: responseTime,
        isWorking: false,
        hasData: false,
        responseSize: 0,
        contentType: 'error',
        error: e.toString(),
      );
    }
  }

  /// Generate comprehensive API status report
  static Future<void> generateStatusReport() async {
    print('🔍 HEMMA SPORTS ACADEMY ERP - API STATUS REPORT');
    print('=' * 80);
    print('Generated: ${DateTime.now()}');
    print('Base URL: $baseUrl');
    print('=' * 80);

    final endpoints = getAllApiEndpoints();
    final results = <ApiEndpointStatus>[];

    for (final endpoint in endpoints) {
      print('Checking: ${endpoint.name}...');
      final status = await checkEndpoint(endpoint.url, endpoint.method);
      results.add(status);
    }

    // Generate summary
    final workingCount = results.where((r) => r.isWorking).length;
    final withDataCount = results.where((r) => r.hasData).length;
    final totalCount = results.length;

    print('\n📊 SUMMARY');
    print('=' * 40);
    print('Total Endpoints: $totalCount');
    print(
      'Working: $workingCount (${(workingCount / totalCount * 100).toStringAsFixed(1)}%)',
    );
    print(
      'With Data: $withDataCount (${(withDataCount / totalCount * 100).toStringAsFixed(1)}%)',
    );
    print('Failed: ${totalCount - workingCount}');

    // Detailed results
    print('\n📋 DETAILED RESULTS');
    print('=' * 80);

    for (int i = 0; i < endpoints.length; i++) {
      final endpoint = endpoints[i];
      final result = results[i];

      final status = result.isWorking ? '✅' : '❌';
      final dataStatus = result.hasData ? '📊' : '📭';

      print('$status $dataStatus ${endpoint.category}');
      print('   Name: ${endpoint.name}');
      print('   URL: ${result.url}');
      print('   Method: ${result.method}');
      print('   Status: ${result.statusCode}');
      print('   Response Time: ${result.responseTime}ms');
      print('   Content Type: ${result.contentType}');
      print('   Response Size: ${result.responseSize} bytes');
      if (result.error != null) {
        print('   Error: ${result.error}');
      }
      print('   Working: ${result.isWorking}');
      print('   Has Data: ${result.hasData}');
      print('');
    }

    // Category breakdown
    print('📂 CATEGORY BREAKDOWN');
    print('=' * 40);
    final categories = endpoints.map((e) => e.category).toSet();

    for (final category in categories) {
      final categoryEndpoints =
          endpoints.where((e) => e.category == category).toList();
      final categoryResults =
          results
              .where((r) => categoryEndpoints.any((e) => e.url == r.url))
              .toList();
      final categoryWorking = categoryResults.where((r) => r.isWorking).length;

      print('$category: $categoryWorking/${categoryEndpoints.length} working');
    }
  }

  /// Get all API endpoints in the system
  static List<ApiEndpoint> getAllApiEndpoints() {
    return [
      // Authentication Endpoints (from AuthService)
      ApiEndpoint('Authentication', 'Login', '$baseUrl/auth/login', 'POST'),
      ApiEndpoint('Authentication', 'Logout', '$baseUrl/auth/logout', 'POST'),
      ApiEndpoint(
        'Authentication',
        'Refresh Token',
        '$baseUrl/auth/refresh',
        'POST',
      ),
      ApiEndpoint(
        'Authentication',
        'Change Password',
        '$baseUrl/auth/change-password',
        'POST',
      ),

      // Customer Management
      ApiEndpoint(
        'Customer',
        'Select All Customers',
        '$baseUrl/customers/select_all.php',
        'GET',
      ),

      ApiEndpoint(
        'Customer',
        'Create Customer',
        '$baseUrl/crud.php?table=customers&action=insert',
        'POST',
      ),
      ApiEndpoint(
        'Customer',
        'Update Customer',
        '$baseUrl/crud.php?table=customers&action=update',
        'POST',
      ),

      // Sports Management
      ApiEndpoint('Sports', 'Fetch Sports', '$baseUrl/fetch_sports.php', 'GET'),
      ApiEndpoint(
        'Sports',
        'Sports CRUD Select',
        '$baseUrl/crud.php?table=sports&action=select',
        'GET',
      ),

      // Stadium Management
      ApiEndpoint(
        'Stadium',
        'Select Stadiums',
        '$baseUrl/stadiums/select_stadiums.php',
        'GET',
      ),
      ApiEndpoint(
        'Stadium',
        'Insert Stadium',
        '$baseUrl/stadiums/insert_stadium.php',
        'POST',
      ),
      ApiEndpoint(
        'Stadium',
        'Update Stadium',
        '$baseUrl/stadiums/update_stadium.php',
        'POST',
      ),
      ApiEndpoint(
        'Stadium',
        'Delete Stadium',
        '$baseUrl/stadiums/delete_stadium.php',
        'POST',
      ),

      // Stadium Booking Management
      ApiEndpoint(
        'Stadium Booking',
        'Select Stadium Bookings',
        '$baseUrl/stadium_bookings/select_stadium_bookings.php',
        'GET',
      ),
      ApiEndpoint(
        'Stadium Booking',
        'Insert Stadium Booking',
        '$baseUrl/stadium_bookings/insert_stadium_booking.php',
        'POST',
      ),
      ApiEndpoint(
        'Stadium Booking',
        'Update Stadium Booking',
        '$baseUrl/stadium_bookings/update_stadium_booking.php',
        'POST',
      ),
      ApiEndpoint(
        'Stadium Booking',
        'Delete Stadium Booking',
        '$baseUrl/stadium_bookings/delete_stadium_booking.php',
        'POST',
      ),

      // Visit Booking Management
      ApiEndpoint(
        'Visit Booking',
        'Select Visit Bookings',
        '$baseUrl/booking/select_visit_bookings.php',
        'GET',
      ),
      ApiEndpoint(
        'Visit Booking',
        'Insert Visit Booking',
        '$baseUrl/booking/insert_visit_booking.php',
        'POST',
      ),

      // Employee Management
      ApiEndpoint(
        'Employee',
        'Select Employees',
        '$baseUrl/employees/select_employee.php',
        'GET',
      ),

      // Payroll Management
      ApiEndpoint(
        'Payroll',
        'Select Payroll',
        '$baseUrl/payroll/select_payroll.php',
        'GET',
      ),
      ApiEndpoint(
        'Payroll',
        'Insert Payroll',
        '$baseUrl/payroll/insert_payroll.php',
        'POST',
      ),
      ApiEndpoint(
        'Payroll',
        'Update Payroll',
        '$baseUrl/payroll/update_payroll.php',
        'POST',
      ),
      ApiEndpoint(
        'Payroll',
        'Delete Payroll',
        '$baseUrl/payroll/delete_payroll.php',
        'POST',
      ),

      // Trainer Management
      ApiEndpoint(
        'Trainer',
        'Get Trainers',
        '$baseUrl/trainers/get_trainers.php',
        'GET',
      ),

      // Time Planning
      ApiEndpoint(
        'Time Planning',
        'Sport Time Plan',
        '$baseUrl/timeplan/sport_time_plan.php',
        'GET',
      ),
      ApiEndpoint(
        'Time Planning',
        'Trainer Trainees',
        '$baseUrl/timeplan/trainer_trainees.php',
        'GET',
      ),

      // General Ledger
      ApiEndpoint(
        'General Ledger',
        'Select Customer',
        '$baseUrl/gledger/select_customer.php',
        'GET',
      ),

      // Promo Code Management
      ApiEndpoint(
        'Promo Code',
        'Select Promo Codes',
        '$baseUrl/promocodes/get/promocode_select.php',
        'GET',
      ),
      ApiEndpoint(
        'Promo Code',
        'Insert Promo Code',
        '$baseUrl/promocodes/insert/promocode_insert.php',
        'POST',
      ),
      ApiEndpoint(
        'Promo Code',
        'Update Promo Code',
        '$baseUrl/promocodes/update/promocode_update.php',
        'POST',
      ),
      ApiEndpoint(
        'Promo Code',
        'Delete Promo Code',
        '$baseUrl/promocodes/delete/promocodes_delete.php',
        'POST',
      ),
      ApiEndpoint(
        'Promo Code',
        'Select Eligibility',
        '$baseUrl/promocodes/get/select_promo_code_eligibility.php',
        'GET',
      ),
      ApiEndpoint(
        'Promo Code',
        'Insert Eligibility',
        '$baseUrl/promocodes/insert/insert_promo_code_eligibility.php',
        'POST',
      ),
      ApiEndpoint(
        'Promo Code',
        'Delete Eligibility',
        '$baseUrl/promocodes/delete/delete_promo_code_eligibility.php',
        'POST',
      ),
      ApiEndpoint(
        'Promo Code',
        'Select Usages',
        '$baseUrl/promocodes/get/select_promo_code_usages.php',
        'GET',
      ),
      ApiEndpoint(
        'Promo Code',
        'Insert Usage',
        '$baseUrl/promocodes/insert/insert_promo_code_usage.php',
        'POST',
      ),
      ApiEndpoint(
        'Promo Code',
        'Delete Usage',
        '$baseUrl/promocodes/delete/delete_promo_code_usage.php',
        'POST',
      ),

      // Reports (from ReportService)
      ApiEndpoint('Reports', 'Get Reports', '$baseUrl/reports', 'GET'),
      ApiEndpoint(
        'Reports',
        'Generate Financial Report',
        '$baseUrl/reports/financial',
        'POST',
      ),
      ApiEndpoint(
        'Reports',
        'Generate Attendance Report',
        '$baseUrl/reports/attendance',
        'POST',
      ),
      ApiEndpoint(
        'Reports',
        'Download Report',
        '$baseUrl/reports/{id}/download',
        'GET',
      ),

      // Status/Health Check
      ApiEndpoint(
        'System',
        'All Cards and Charts',
        '$baseUrl/api/status/all_cards_and_charts.php',
        'GET',
      ),
    ];
  }
}

/// Represents an API endpoint
class ApiEndpoint {
  final String category;
  final String name;
  final String url;
  final String method;

  ApiEndpoint(this.category, this.name, this.url, this.method);
}

/// Represents the status of an API endpoint
class ApiEndpointStatus {
  final String url;
  final String method;
  final int statusCode;
  final int responseTime;
  final bool isWorking;
  final bool hasData;
  final int responseSize;
  final String contentType;
  final String? error;

  ApiEndpointStatus({
    required this.url,
    required this.method,
    required this.statusCode,
    required this.responseTime,
    required this.isWorking,
    required this.hasData,
    required this.responseSize,
    required this.contentType,
    this.error,
  });
}

/// Main function to run the API status check
void main() async {
  await ApiStatusChecker.generateStatusReport();
}
