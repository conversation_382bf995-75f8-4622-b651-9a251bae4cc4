<svg xmlns="http://www.w3.org/2000/svg" width="1920" height="1080" viewBox="0 0 1920 1080">
  <!-- Background gradient -->
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#304FFE;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00BCD4;stop-opacity:1" />
    </linearGradient>
    
    <!-- Pattern for background texture -->
    <pattern id="pattern1" x="0" y="0" width="60" height="60" patternUnits="userSpaceOnUse">
      <circle cx="30" cy="30" r="20" fill="rgba(255, 255, 255, 0.05)" />
    </pattern>
  </defs>
  
  <!-- Main background -->
  <rect width="1920" height="1080" fill="url(#grad1)" />
  
  <!-- Pattern overlay -->
  <rect width="1920" height="1080" fill="url(#pattern1)" />
  
  <!-- Abstract shapes -->
  <circle cx="200" cy="200" r="400" fill="rgba(255, 255, 255, 0.03)" />
  <circle cx="1720" cy="880" r="300" fill="rgba(255, 255, 255, 0.03)" />
  
  <!-- Diagonal lines -->
  <g stroke="rgba(255, 255, 255, 0.07)" stroke-width="2">
    <line x1="0" y1="0" x2="1920" y2="1080" />
    <line x1="1920" y1="0" x2="0" y2="1080" />
  </g>
  
  <!-- Decorative elements -->
  <g fill="rgba(255, 255, 255, 0.1)">
    <circle cx="960" cy="540" r="100" />
    <circle cx="960" cy="540" r="200" fill="none" stroke="rgba(255, 255, 255, 0.1)" stroke-width="2" />
    <circle cx="960" cy="540" r="300" fill="none" stroke="rgba(255, 255, 255, 0.05)" stroke-width="1" />
  </g>
</svg>