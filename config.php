<?php
/**
 * Configuration File
 * 
 * Contains database and application configuration settings
 * 
 * <AUTHOR> Name
 * @version 1.0
 */

// Prevent direct access
if (!defined('API_ACCESS')) {
    die('Direct access not permitted');
}

/**
 * Database Configuration
 */
define('DB_HOST', 'localhost');
define('DB_USER', 'u709340751_erp');
define('DB_PASS', '1234567890CodePoint');
define('DB_NAME', 'u709340751_erp_final');
define('DB_CHARSET', 'utf8mb4');

/**
 * Application Configuration
 */
define('APP_NAME', 'Trainer Trainees API');
define('APP_VERSION', '1.1');
define('APP_DEBUG', false); // Set to false in production

/**
 * API Configuration
 */
define('API_RATE_LIMIT', 100); // Requests per minute
define('API_TIMEOUT', 30); // Seconds

/**
 * CORS Configuration
 */
define('CORS_ALLOWED_ORIGINS', '*'); // In production, specify exact domains
define('CORS_ALLOWED_METHODS', 'GET, POST, OPTIONS');
define('CORS_ALLOWED_HEADERS', 'Content-Type, Authorization, X-Requested-With');

/**
 * Error Handling Configuration
 */
define('LOG_ERRORS', true);
define('LOG_FILE', 'api_errors.log');
define('DISPLAY_ERRORS', APP_DEBUG);

/**
 * Security Configuration
 */
define('MAX_QUERY_RESULTS', 1000); // Maximum number of results per query
define('ENABLE_API_KEY', false); // Set to true to require API key authentication

/**
 * Environment-specific configuration
 */
$environment = $_SERVER['SERVER_NAME'] ?? 'localhost';

switch ($environment) {
    case 'localhost':
    case '127.0.0.1':
        // Development environment
        define('ENV', 'development');
        ini_set('display_errors', 1);
        ini_set('display_startup_errors', 1);
        error_reporting(E_ALL);
        break;
        
    default:
        // Production environment
        define('ENV', 'production');
        ini_set('display_errors', 0);
        ini_set('display_startup_errors', 0);
        error_reporting(E_ALL & ~E_NOTICE & ~E_DEPRECATED);
        break;
}

/**
 * Timezone Configuration
 */
date_default_timezone_set('UTC'); // Change to your timezone

/**
 * Custom Error Handler
 */
function customErrorHandler($errno, $errstr, $errfile, $errline) {
    if (LOG_ERRORS) {
        $error_message = date('Y-m-d H:i:s') . " - Error [$errno]: $errstr in $errfile on line $errline" . PHP_EOL;
        error_log($error_message, 3, LOG_FILE);
    }
    
    if (DISPLAY_ERRORS) {
        echo "Error: $errstr";
    }
    
    return true;
}

// Set custom error handler
set_error_handler('customErrorHandler');

/**
 * Custom Exception Handler
 */
function customExceptionHandler($exception) {
    if (LOG_ERRORS) {
        $error_message = date('Y-m-d H:i:s') . " - Uncaught Exception: " . $exception->getMessage() . 
                        " in " . $exception->getFile() . " on line " . $exception->getLine() . PHP_EOL;
        error_log($error_message, 3, LOG_FILE);
    }
    
    // Return JSON error response
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Internal server error',
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}

// Set custom exception handler
set_exception_handler('customExceptionHandler');
?>
