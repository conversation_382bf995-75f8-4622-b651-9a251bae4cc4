// ignore_for_file: avoid_print

import 'package:hemmaerp/services/auth_service.dart';
import 'package:hemmaerp/services/secure_api_service.dart';

/// Example demonstrating how to use the AuthService
void main() async {
  // Initialize the API service
  final apiService = SecureApiService(
    baseUrl: 'https://api.example.com',
    timeout: const Duration(seconds: 30),
  );

  // Initialize the authentication service
  final authService = AuthService(apiService);

  // Example 1: Initialize authentication from stored data
  print('=== Initializing Authentication ===');
  final isInitialized = await authService.initializeAuth();
  if (isInitialized) {
    print('✅ Authentication initialized successfully');
    print('Current user: ${authService.currentUser?.name}');
  } else {
    print('❌ No stored authentication found');
  }

  // Example 2: Login with email and password
  print('\n=== Login Example ===');
  final loginResult = await authService.login(
    '<EMAIL>',
    'SecurePassword123!',
  );

  if (loginResult.success) {
    print('✅ Login successful');
    print('User: ${loginResult.user?.name}');
    print('Email: ${loginResult.user?.email}');
    print('Role: ${loginResult.user?.role}');
    print('Permissions: ${loginResult.user?.permissions}');
  } else {
    print('❌ Login failed: ${loginResult.error}');
  }

  // Example 3: Check authentication status
  print('\n=== Authentication Status ===');
  if (authService.isAuthenticated) {
    print('✅ User is authenticated');
    
    // Example 4: Change password
    print('\n=== Change Password Example ===');
    final changePasswordResult = await authService.changePassword(
      'SecurePassword123!',
      'NewSecurePassword456!',
    );
    
    if (changePasswordResult.success) {
      print('✅ Password changed successfully');
    } else {
      print('❌ Password change failed: ${changePasswordResult.error}');
    }
    
    // Example 5: Refresh authentication token
    print('\n=== Token Refresh Example ===');
    final refreshResult = await authService.refreshAuthToken();
    if (refreshResult) {
      print('✅ Token refreshed successfully');
    } else {
      print('❌ Token refresh failed');
    }
    
    // Example 6: Check user permissions
    print('\n=== Permission Check Example ===');
    final user = authService.currentUser;
    if (user != null) {
      print('Can read: ${user.hasPermission('read')}');
      print('Can write: ${user.hasPermission('write')}');
      print('Can delete: ${user.hasPermission('delete')}');
    }
    
    // Example 7: Logout
    print('\n=== Logout Example ===');
    await authService.logout();
    print('✅ Logged out successfully');
    
  } else {
    print('❌ User is not authenticated');
  }

  // Example 8: Demonstrate validation errors
  print('\n=== Validation Examples ===');
  
  // Invalid email
  final invalidEmailResult = await authService.login('invalid-email', 'password');
  print('Invalid email result: ${invalidEmailResult.error}');
  
  // Short password
  final shortPasswordResult = await authService.login('<EMAIL>', '123');
  print('Short password result: ${shortPasswordResult.error}');
  
  // Empty password
  final emptyPasswordResult = await authService.login('<EMAIL>', '');
  print('Empty password result: ${emptyPasswordResult.error}');
}

/// Example of handling authentication in a Flutter app
class AuthenticationExample {
  final AuthService _authService;
  
  AuthenticationExample(this._authService);
  
  /// Initialize authentication when app starts
  Future<bool> initializeApp() async {
    try {
      return await _authService.initializeAuth();
    } catch (e) {
      print('Failed to initialize authentication: $e');
      return false;
    }
  }
  
  /// Handle user login
  Future<String?> loginUser(String email, String password) async {
    try {
      final result = await _authService.login(email, password);
      
      if (result.success) {
        // Navigate to main app screen
        return null; // No error
      } else {
        return result.error; // Return error message
      }
    } catch (e) {
      return 'Login failed: ${e.toString()}';
    }
  }
  
  /// Handle user logout
  Future<void> logoutUser() async {
    try {
      await _authService.logout();
      // Navigate to login screen
    } catch (e) {
      print('Logout error: $e');
      // Still navigate to login screen even if server call fails
    }
  }
  
  /// Check if user is authenticated
  bool get isUserLoggedIn => _authService.isAuthenticated;
  
  /// Get current user information
  UserData? get currentUser => _authService.currentUser;
  
  /// Handle password change
  Future<String?> changeUserPassword(String currentPassword, String newPassword) async {
    try {
      final result = await _authService.changePassword(currentPassword, newPassword);
      
      if (result.success) {
        return null; // Success
      } else {
        return result.error;
      }
    } catch (e) {
      return 'Password change failed: ${e.toString()}';
    }
  }
  
  /// Refresh authentication token periodically
  Future<bool> refreshAuthentication() async {
    try {
      return await _authService.refreshAuthToken();
    } catch (e) {
      print('Token refresh failed: $e');
      return false;
    }
  }
}

/// Example of using AuthService with error handling
class AuthServiceWrapper {
  final AuthService _authService;
  
  AuthServiceWrapper(this._authService);
  
  /// Safe login with comprehensive error handling
  Future<AuthResult> safeLogin(String email, String password) async {
    try {
      // Validate inputs before making API call
      if (email.isEmpty) {
        return AuthResult.failure('Email is required');
      }
      
      if (password.isEmpty) {
        return AuthResult.failure('Password is required');
      }
      
      // Attempt login
      final result = await _authService.login(email, password);
      
      // Log the result (without sensitive data)
      if (result.success) {
        print('Login successful for user: ${result.user?.name}');
      } else {
        print('Login failed: ${result.error}');
      }
      
      return result;
      
    } catch (e) {
      print('Login exception: $e');
      return AuthResult.failure('An unexpected error occurred during login');
    }
  }
  
  /// Safe logout with error handling
  Future<void> safeLogout() async {
    try {
      await _authService.logout();
      print('Logout successful');
    } catch (e) {
      print('Logout error: $e');
      // Continue with logout process even if server call fails
    }
  }
  
  /// Periodic token refresh with retry logic
  Future<bool> refreshTokenWithRetry({int maxRetries = 3}) async {
    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        final success = await _authService.refreshAuthToken();
        if (success) {
          print('Token refresh successful on attempt $attempt');
          return true;
        }
      } catch (e) {
        print('Token refresh attempt $attempt failed: $e');
      }
      
      if (attempt < maxRetries) {
        // Wait before retrying
        await Future.delayed(Duration(seconds: attempt * 2));
      }
    }
    
    print('Token refresh failed after $maxRetries attempts');
    return false;
  }
}
