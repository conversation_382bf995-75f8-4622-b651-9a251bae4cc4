import 'package:flutter/material.dart';
import '../constants/animation_constants.dart';

/// A widget that fades in when it's first built
class FadeInWidget extends StatefulWidget {
  final Widget child;
  final Duration? duration;
  final Curve? curve;
  final double? delay;
  
  const FadeInWidget({
    super.key,
    required this.child,
    this.duration,
    this.curve,
    this.delay,
  });
  
  @override
  State<FadeInWidget> createState() => _FadeInWidgetState();
}

class _FadeInWidgetState extends State<FadeInWidget> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.duration ?? AnimationConstants.mediumDuration,
    );
    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: widget.curve ?? AnimationConstants.standardCurve,
      ),
    );
    
    if (widget.delay != null) {
      Future.delayed(Duration(milliseconds: (widget.delay! * 1000).toInt()), () {
        if (mounted) _controller.forward();
      });
    } else {
      _controller.forward();
    }
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _animation,
      child: widget.child,
    );
  }
}

/// A widget that slides in from a direction
class SlideInWidget extends StatefulWidget {
  final Widget child;
  final Duration? duration;
  final Curve? curve;
  final Offset? beginOffset;
  final double? delay;
  
  const SlideInWidget({
    super.key,
    required this.child,
    this.duration,
    this.curve,
    this.beginOffset,
    this.delay,
  });
  
  @override
  State<SlideInWidget> createState() => _SlideInWidgetState();
}

class _SlideInWidgetState extends State<SlideInWidget> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<Offset> _animation;
  
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.duration ?? AnimationConstants.mediumDuration,
    );
    _animation = Tween<Offset>(
      begin: widget.beginOffset ?? const Offset(0.0, 0.2),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: widget.curve ?? AnimationConstants.standardCurve,
      ),
    );
    
    if (widget.delay != null) {
      Future.delayed(Duration(milliseconds: (widget.delay! * 1000).toInt()), () {
        if (mounted) _controller.forward();
      });
    } else {
      _controller.forward();
    }
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: _animation,
      child: widget.child,
    );
  }
}

/// A widget that scales in
class ScaleInWidget extends StatefulWidget {
  final Widget child;
  final Duration? duration;
  final Curve? curve;
  final double? beginScale;
  final double? delay;
  
  const ScaleInWidget({
    super.key,
    required this.child,
    this.duration,
    this.curve,
    this.beginScale,
    this.delay,
  });
  
  @override
  State<ScaleInWidget> createState() => _ScaleInWidgetState();
}

class _ScaleInWidgetState extends State<ScaleInWidget> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.duration ?? AnimationConstants.mediumDuration,
    );
    _animation = Tween<double>(
      begin: widget.beginScale ?? 0.8,
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: widget.curve ?? AnimationConstants.standardCurve,
      ),
    );
    
    if (widget.delay != null) {
      Future.delayed(Duration(milliseconds: (widget.delay! * 1000).toInt()), () {
        if (mounted) _controller.forward();
      });
    } else {
      _controller.forward();
    }
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return ScaleTransition(
      scale: _animation,
      child: widget.child,
    );
  }
}

/// A widget that combines fade, slide and scale animations
class AnimatedEntryWidget extends StatefulWidget {
  final Widget child;
  final Duration? duration;
  final Curve? curve;
  final Offset? beginOffset;
  final double? beginScale;
  final double? delay;
  final bool fade;
  final bool slide;
  final bool scale;
  
  const AnimatedEntryWidget({
    super.key,
    required this.child,
    this.duration,
    this.curve,
    this.beginOffset,
    this.beginScale,
    this.delay,
    this.fade = true,
    this.slide = true,
    this.scale = false,
  });
  
  @override
  State<AnimatedEntryWidget> createState() => _AnimatedEntryWidgetState();
}

class _AnimatedEntryWidgetState extends State<AnimatedEntryWidget> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;
  
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.duration ?? AnimationConstants.mediumDuration,
    );
    
    _fadeAnimation = Tween<double>(
      begin: widget.fade ? 0.0 : 1.0,
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: widget.curve ?? AnimationConstants.standardCurve,
      ),
    );
    
    _slideAnimation = Tween<Offset>(
      begin: widget.slide ? (widget.beginOffset ?? const Offset(0.0, 0.2)) : Offset.zero,
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: widget.curve ?? AnimationConstants.standardCurve,
      ),
    );
    
    _scaleAnimation = Tween<double>(
      begin: widget.scale ? (widget.beginScale ?? 0.9) : 1.0,
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: widget.curve ?? AnimationConstants.standardCurve,
      ),
    );
    
    if (widget.delay != null) {
      Future.delayed(Duration(milliseconds: (widget.delay! * 1000).toInt()), () {
        if (mounted) _controller.forward();
      });
    } else {
      _controller.forward();
    }
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: ScaleTransition(
          scale: _scaleAnimation,
          child: widget.child,
        ),
      ),
    );
  }
}

/// A widget that pulses to draw attention
class PulseAnimationWidget extends StatefulWidget {
  final Widget child;
  final Duration? duration;
  final double minScale;
  final double maxScale;
  final bool autoStart;
  final bool repeat;
  
  const PulseAnimationWidget({
    super.key,
    required this.child,
    this.duration,
    this.minScale = 0.97,
    this.maxScale = 1.03,
    this.autoStart = true,
    this.repeat = true,
  });
  
  @override
  State<PulseAnimationWidget> createState() => _PulseAnimationWidgetState();
}

class _PulseAnimationWidgetState extends State<PulseAnimationWidget> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.duration ?? AnimationConstants.longDuration,
    );
    
    _animation = TweenSequence<double>([
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.0, end: widget.maxScale)
            .chain(CurveTween(curve: Curves.easeInOut)),
        weight: 1,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: widget.maxScale, end: widget.minScale)
            .chain(CurveTween(curve: Curves.easeInOut)),
        weight: 1,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: widget.minScale, end: 1.0)
            .chain(CurveTween(curve: Curves.easeInOut)),
        weight: 1,
      ),
    ]).animate(_controller);
    
    if (widget.autoStart) {
      if (widget.repeat) {
        _controller.repeat();
      } else {
        _controller.forward();
      }
    }
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Transform.scale(
          scale: _animation.value,
          child: child,
        );
      },
      child: widget.child,
    );
  }
}

/// A widget that shimmers to indicate loading state
class ShimmerLoadingWidget extends StatefulWidget {
  final double width;
  final double height;
  final double borderRadius;
  final Color baseColor;
  final Color highlightColor;
  
  const ShimmerLoadingWidget({
    super.key,
    required this.width,
    required this.height,
    this.borderRadius = 8.0,
    this.baseColor = const Color(0xFF2C2C2C),
    this.highlightColor = const Color(0xFF3E3E3E),
  });
  
  @override
  State<ShimmerLoadingWidget> createState() => _ShimmerLoadingWidgetState();
}

class _ShimmerLoadingWidgetState extends State<ShimmerLoadingWidget> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: AnimationConstants.loadingIndicatorDuration,
    );
    
    _animation = Tween<double>(begin: -1.0, end: 2.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOutSine,
      ),
    );
    
    _controller.repeat();
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          width: widget.width,
          height: widget.height,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(widget.borderRadius),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                widget.baseColor,
                widget.highlightColor,
                widget.baseColor,
              ],
              stops: [
                0.0,
                _animation.value,
                1.0,
              ],
            ),
          ),
        );
      },
    );
  }
}