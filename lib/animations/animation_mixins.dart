import 'package:flutter/material.dart';
import '../constants/animation_constants.dart';

/// Mixin for single ticker provider state
mixin AnimationControllerMixin<T extends StatefulWidget> on State<T>, TickerProviderStateMixin<T> {
  late AnimationController controller;
  
  @override
  void initState() {
    super.initState();
    controller = AnimationController(
      vsync: this,
      duration: AnimationConstants.mediumDuration,
    );
  }
  
  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }
}

/// Mixin for fade in animation
mixin FadeInAnimationMixin<T extends StatefulWidget> on State<T>, TickerProviderStateMixin<T> {
  late AnimationController fadeController;
  late Animation<double> fadeAnimation;
  
  @override
  void initState() {
    super.initState();
    fadeController = AnimationController(
      vsync: this,
      duration: AnimationConstants.mediumDuration,
    );
    fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: fadeController,
        curve: AnimationConstants.standardCurve,
      ),
    );
    fadeController.forward();
  }
  
  @override
  void dispose() {
    fadeController.dispose();
    super.dispose();
  }
}

/// Mixin for slide animation
mixin SlideAnimationMixin<T extends StatefulWidget> on State<T>, TickerProviderStateMixin<T> {
  late AnimationController slideController;
  late Animation<Offset> slideAnimation;
  
  void initSlideAnimation({
    Offset begin = const Offset(0.0, 0.2),
    Offset end = Offset.zero,
    Duration? duration,
  }) {
    slideController = AnimationController(
      vsync: this,
      duration: duration ?? AnimationConstants.mediumDuration,
    );
    slideAnimation = Tween<Offset>(begin: begin, end: end).animate(
      CurvedAnimation(
        parent: slideController,
        curve: AnimationConstants.standardCurve,
      ),
    );
    slideController.forward();
  }
  
  @override
  void dispose() {
    slideController.dispose();
    super.dispose();
  }
}

/// Mixin for scale animation
mixin ScaleAnimationMixin<T extends StatefulWidget> on State<T>, TickerProviderStateMixin<T> {
  late AnimationController scaleController;
  late Animation<double> scaleAnimation;
  
  void initScaleAnimation({
    double begin = 0.8,
    double end = 1.0,
    Duration? duration,
    Curve? curve,
  }) {
    scaleController = AnimationController(
      vsync: this,
      duration: duration ?? AnimationConstants.mediumDuration,
    );
    scaleAnimation = Tween<double>(begin: begin, end: end).animate(
      CurvedAnimation(
        parent: scaleController,
        curve: curve ?? AnimationConstants.standardCurve,
      ),
    );
    scaleController.forward();
  }
  
  @override
  void dispose() {
    scaleController.dispose();
    super.dispose();
  }
}

/// Mixin for staggered list animation
mixin StaggeredListAnimationMixin<T extends StatefulWidget> on State<T>, TickerProviderStateMixin<T> {
  final Map<int, AnimationController> _controllers = {};
  final Map<int, Animation<Offset>> _slideAnimations = {};
  final Map<int, Animation<double>> _fadeAnimations = {};
  
  AnimationController getControllerForIndex(int index) {
    if (!_controllers.containsKey(index)) {
      final controller = AnimationController(
        vsync: this,
        duration: AnimationConstants.mediumDuration,
      );
      _controllers[index] = controller;
      
      Future.delayed(AnimationConstants.staggeredDelay * index, () {
        if (mounted) controller.forward();
      });
    }
    return _controllers[index]!;
  }
  
  Animation<Offset> getSlideAnimationForIndex(int index) {
    if (!_slideAnimations.containsKey(index)) {
      final controller = getControllerForIndex(index);
      _slideAnimations[index] = Tween<Offset>(
        begin: const Offset(0.0, 0.2),
        end: Offset.zero,
      ).animate(
        CurvedAnimation(
          parent: controller,
          curve: AnimationConstants.standardCurve,
        ),
      );
    }
    return _slideAnimations[index]!;
  }
  
  Animation<double> getFadeAnimationForIndex(int index) {
    if (!_fadeAnimations.containsKey(index)) {
      final controller = getControllerForIndex(index);
      _fadeAnimations[index] = Tween<double>(
        begin: 0.0,
        end: 1.0,
      ).animate(
        CurvedAnimation(
          parent: controller,
          curve: AnimationConstants.standardCurve,
        ),
      );
    }
    return _fadeAnimations[index]!;
  }
  
  @override
  void dispose() {
    for (final controller in _controllers.values) {
      controller.dispose();
    }
    super.dispose();
  }
}