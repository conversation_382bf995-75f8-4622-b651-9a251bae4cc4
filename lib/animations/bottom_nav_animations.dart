// ignore_for_file: unused_import, deprecated_member_use

import 'package:flutter/material.dart';
import '../constants/animation_constants.dart';

/// Animated bottom navigation bar for ERP system
class AnimatedBottomNavBar extends StatefulWidget {
  final List<BottomNavItem> items;
  final int currentIndex;
  final ValueChanged<int> onTap;
  final Color backgroundColor;
  final Color selectedItemColor;
  final Color unselectedItemColor;
  final double height;
  final double iconSize;
  final TextStyle? selectedLabelStyle;
  final TextStyle? unselectedLabelStyle;
  final Duration animationDuration;
  final Curve animationCurve;
  
  const AnimatedBottomNavBar({
    super.key,
    required this.items,
    required this.currentIndex,
    required this.onTap,
    this.backgroundColor = Colors.white,
    this.selectedItemColor = Colors.blue,
    this.unselectedItemColor = Colors.grey,
    this.height = 60.0,
    this.iconSize = 24.0,
    this.selectedLabelStyle,
    this.unselectedLabelStyle,
    this.animationDuration = const Duration(milliseconds: 200),
    this.animationCurve = Curves.easeInOut,
  });
  
  @override
  State<AnimatedBottomNavBar> createState() => _AnimatedBottomNavBarState();
}

class _AnimatedBottomNavBarState extends State<AnimatedBottomNavBar> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late List<Animation<double>> _animations;
  
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.animationDuration,
    );
    
    _createAnimations();
    
    // Initialize with current index
    _controller.value = 1.0;
  }
  
  void _createAnimations() {
    _animations = List.generate(
      widget.items.length,
      (index) {
        return Tween<double>(
          begin: 0.0,
          end: index == widget.currentIndex ? 1.0 : 0.0,
        ).animate(
          CurvedAnimation(
            parent: _controller,
            curve: widget.animationCurve,
          ),
        );
      },
    );
  }
  
  @override
  void didUpdateWidget(AnimatedBottomNavBar oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (oldWidget.currentIndex != widget.currentIndex) {
      _createAnimations();
      _controller.forward(from: 0.0);
    }
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Container(
      height: widget.height,
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8.0,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: List.generate(
          widget.items.length,
          (index) => _buildNavItem(index),
        ),
      ),
    );
  }
  
  Widget _buildNavItem(int index) {
    final isSelected = index == widget.currentIndex;
    
    return InkWell(
      onTap: () => widget.onTap(index),
      child: AnimatedBuilder(
        animation: _animations[index],
        builder: (context, child) {
          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Icon with scale animation
                Transform.scale(
                  scale: 1.0 + (_animations[index].value * 0.2),
                  child: Icon(
                    widget.items[index].icon,
                    color: Color.lerp(
                      widget.unselectedItemColor,
                      widget.selectedItemColor,
                      _animations[index].value,
                    ),
                    size: widget.iconSize,
                  ),
                ),
                
                const SizedBox(height: 4.0),
                
                // Label with fade animation
                Opacity(
                  opacity: 0.5 + (_animations[index].value * 0.5),
                  child: Text(
                    widget.items[index].label,
                    style: isSelected
                        ? widget.selectedLabelStyle ?? TextStyle(
                            color: widget.selectedItemColor,
                            fontSize: 12.0,
                            fontWeight: FontWeight.bold,
                          )
                        : widget.unselectedLabelStyle ?? TextStyle(
                            color: widget.unselectedItemColor,
                            fontSize: 12.0,
                          ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}

/// Data class for bottom navigation items
class BottomNavItem {
  final IconData icon;
  final String label;
  
  const BottomNavItem({
    required this.icon,
    required this.label,
  });
}

/// Animated floating bottom navigation bar
class FloatingBottomNavBar extends StatefulWidget {
  final List<BottomNavItem> items;
  final int currentIndex;
  final ValueChanged<int> onTap;
  final Color backgroundColor;
  final Color selectedItemColor;
  final Color unselectedItemColor;
  final double height;
  final double elevation;
  final double borderRadius;
  final EdgeInsetsGeometry margin;
  final Duration animationDuration;
  
  const FloatingBottomNavBar({
    super.key,
    required this.items,
    required this.currentIndex,
    required this.onTap,
    this.backgroundColor = Colors.white,
    this.selectedItemColor = Colors.blue,
    this.unselectedItemColor = Colors.grey,
    this.height = 60.0,
    this.elevation = 8.0,
    this.borderRadius = 30.0,
    this.margin = const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
    this.animationDuration = const Duration(milliseconds: 300),
  });
  
  @override
  State<FloatingBottomNavBar> createState() => _FloatingBottomNavBarState();
}

class _FloatingBottomNavBarState extends State<FloatingBottomNavBar> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _indicatorAnimation;
  
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.animationDuration,
    );
    
    _updateIndicatorAnimation();
  }
  
  void _updateIndicatorAnimation() {
    _indicatorAnimation = Tween<double>(
      begin: _getIndicatorPosition(widget.currentIndex),
      end: _getIndicatorPosition(widget.currentIndex),
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.elasticOut,
      ),
    );
  }
  
  double _getIndicatorPosition(int index) {
    // Calculate the position based on the index
    final itemWidth = 1.0 / widget.items.length;
    return itemWidth * (index + 0.5);
  }
  
  @override
  void didUpdateWidget(FloatingBottomNavBar oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (oldWidget.currentIndex != widget.currentIndex) {
      _indicatorAnimation = Tween<double>(
        begin: _getIndicatorPosition(oldWidget.currentIndex),
        end: _getIndicatorPosition(widget.currentIndex),
      ).animate(
        CurvedAnimation(
          parent: _controller,
          curve: Curves.elasticOut,
        ),
      );
      
      _controller.forward(from: 0.0);
    }
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Container(
      height: widget.height,
      margin: widget.margin,
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: widget.elevation,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Stack(
        children: [
          // Animated indicator
          AnimatedBuilder(
            animation: _indicatorAnimation,
            builder: (context, child) {
              return Positioned(
                top: 0,
                left: _indicatorAnimation.value * MediaQuery.of(context).size.width - 25,
                child: Container(
                  width: 50,
                  height: 3,
                  decoration: BoxDecoration(
                    color: widget.selectedItemColor,
                    borderRadius: BorderRadius.circular(1.5),
                  ),
                ),
              );
            },
          ),
          
          // Nav items
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: List.generate(
              widget.items.length,
              (index) => _buildNavItem(index),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildNavItem(int index) {
    final isSelected = index == widget.currentIndex;
    
    return Expanded(
      child: InkWell(
        onTap: () => widget.onTap(index),
        borderRadius: BorderRadius.circular(widget.borderRadius),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AnimatedContainer(
              duration: widget.animationDuration,
              child: Icon(
                widget.items[index].icon,
                color: isSelected ? widget.selectedItemColor : widget.unselectedItemColor,
                size: isSelected ? 28.0 : 24.0,
              ),
            ),
            
            const SizedBox(height: 4.0),
            
            AnimatedDefaultTextStyle(
              duration: widget.animationDuration,
              style: TextStyle(
                color: isSelected ? widget.selectedItemColor : widget.unselectedItemColor,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                fontSize: 12.0,
              ),
              child: Text(widget.items[index].label),
            ),
          ],
        ),
      ),
    );
  }
}