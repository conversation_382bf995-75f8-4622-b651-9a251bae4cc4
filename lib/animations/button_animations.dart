// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';

/// A button that animates when pressed
class AnimatedPressableButton extends StatefulWidget {
  final Widget child;
  final VoidCallback onPressed;
  final Duration duration;
  final double pressedScale;
  final BorderRadius? borderRadius;
  final Color? color;
  final EdgeInsetsGeometry padding;

  const AnimatedPressableButton({
    super.key,
    required this.child,
    required this.onPressed,
    this.duration = const Duration(milliseconds: 150),
    this.pressedScale = 0.95,
    this.borderRadius,
    this.color,
    this.padding = const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
  });

  @override
  State<AnimatedPressableButton> createState() => _AnimatedPressableButtonState();
}

class _AnimatedPressableButtonState extends State<AnimatedPressableButton> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.duration,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: widget.pressedScale,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut,
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    if (!_isPressed) {
      _isPressed = true;
      _controller.forward();
    }
  }

  void _onTapUp(TapUpDetails details) {
    if (_isPressed) {
      _isPressed = false;
      _controller.reverse().then((_) {
        widget.onPressed();
      });
    }
  }

  void _onTapCancel() {
    if (_isPressed) {
      _isPressed = false;
      _controller.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: _onTapDown,
      onTapUp: _onTapUp,
      onTapCancel: _onTapCancel,
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              decoration: BoxDecoration(
                color: widget.color,
                borderRadius: widget.borderRadius,
              ),
              padding: widget.padding,
              child: widget.child,
            ),
          );
        },
      ),
    );
  }
}

/// A button that ripples when pressed
class RippleButton extends StatefulWidget {
  final Widget child;
  final VoidCallback onPressed;
  final Color rippleColor;
  final Duration duration;
  final BorderRadius? borderRadius;
  final Color? color;
  final EdgeInsetsGeometry padding;

  const RippleButton({
    super.key,
    required this.child,
    required this.onPressed,
    required this.rippleColor,
    this.duration = const Duration(milliseconds: 600),
    this.borderRadius,
    this.color,
    this.padding = const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
  });

  @override
  State<RippleButton> createState() => _RippleButtonState();
}

class _RippleButtonState extends State<RippleButton> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _rippleAnimation;
  Offset _tapPosition = Offset.zero;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.duration,
    );
    
    _rippleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeOut,
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    _tapPosition = details.localPosition;
    _controller.forward(from: 0.0);
  }

  void _onTap() {
    widget.onPressed();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: _onTapDown,
      onTap: _onTap,
      child: ClipRRect(
        borderRadius: widget.borderRadius ?? BorderRadius.zero,
        child: Stack(
          children: [
            Container(
              color: widget.color,
              padding: widget.padding,
              child: widget.child,
            ),
            AnimatedBuilder(
              animation: _rippleAnimation,
              builder: (context, child) {
                return Positioned.fill(
                  child: Opacity(
                    opacity: 1.0 - _rippleAnimation.value,
                    child: CustomPaint(
                      painter: RipplePainter(
                        center: _tapPosition,
                        radius: _rippleAnimation.value * 300,
                        color: widget.rippleColor,
                      ),
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}

class RipplePainter extends CustomPainter {
  final Offset center;
  final double radius;
  final Color color;

  RipplePainter({
    required this.center,
    required this.radius,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    canvas.drawCircle(center, radius, paint);
  }

  @override
  bool shouldRepaint(RipplePainter oldDelegate) {
    return oldDelegate.center != center ||
        oldDelegate.radius != radius ||
        oldDelegate.color != color;
  }
}

/// A button with a loading animation
class LoadingButton extends StatefulWidget {
  final Widget child;
  final Widget loadingWidget;
  final VoidCallback onPressed;
  final bool isLoading;
  final Duration switchDuration;
  final Color? color;
  final BorderRadius? borderRadius;
  final EdgeInsetsGeometry padding;

  const LoadingButton({
    super.key,
    required this.child,
    required this.loadingWidget,
    required this.onPressed,
    this.isLoading = false,
    this.switchDuration = const Duration(milliseconds: 300),
    this.color,
    this.borderRadius,
    this.padding = const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
  });

  @override
  State<LoadingButton> createState() => _LoadingButtonState();
}

class _LoadingButtonState extends State<LoadingButton> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.isLoading ? null : widget.onPressed,
      child: Container(
        decoration: BoxDecoration(
          color: widget.color,
          borderRadius: widget.borderRadius,
        ),
        padding: widget.padding,
        child: AnimatedSwitcher(
          duration: widget.switchDuration,
          child: widget.isLoading ? widget.loadingWidget : widget.child,
          transitionBuilder: (child, animation) {
            return FadeTransition(
              opacity: animation,
              child: ScaleTransition(
                scale: animation,
                child: child,
              ),
            );
          },
        ),
      ),
    );
  }
}

/// A button with hover effects
class HoverButton extends StatefulWidget {
  final Widget child;
  final VoidCallback onPressed;
  final Color? color;
  final Color? hoverColor;
  final BorderRadius? borderRadius;
  final EdgeInsetsGeometry padding;
  final Duration hoverDuration;
  final double hoverScale;
  final double hoverElevation;

  const HoverButton({
    super.key,
    required this.child,
    required this.onPressed,
    this.color,
    this.hoverColor,
    this.borderRadius,
    this.padding = const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
    this.hoverDuration = const Duration(milliseconds: 200),
    this.hoverScale = 1.05,
    this.hoverElevation = 4.0,
  });

  @override
  State<HoverButton> createState() => _HoverButtonState();
}

class _HoverButtonState extends State<HoverButton> {
  bool _isHovering = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => _isHovering = true),
      onExit: (_) => setState(() => _isHovering = false),
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: widget.onPressed,
        child: AnimatedContainer(
          duration: widget.hoverDuration,
          curve: Curves.easeInOut,
          transform: Matrix4.identity()..scale(_isHovering ? widget.hoverScale : 1.0),
          decoration: BoxDecoration(
            color: _isHovering ? widget.hoverColor : widget.color,
            borderRadius: widget.borderRadius,
            boxShadow: _isHovering
                ? [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: widget.hoverElevation,
                      spreadRadius: 1,
                      offset: const Offset(0, 2),
                    )
                  ]
                : null,
          ),
          padding: widget.padding,
          child: widget.child,
        ),
      ),
    );
  }
}