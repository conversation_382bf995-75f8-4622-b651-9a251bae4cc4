// ignore_for_file: unused_import

import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../constants/animation_constants.dart';

/// A widget that animates a circular progress indicator
class AnimatedCircularProgress extends StatefulWidget {
  final double value;
  final double size;
  final double strokeWidth;
  final Color backgroundColor;
  final Color foregroundColor;
  final Widget? child;
  final Duration duration;
  final Curve curve;

  const AnimatedCircularProgress({
    super.key,
    required this.value,
    this.size = 100.0,
    this.strokeWidth = 10.0,
    required this.backgroundColor,
    required this.foregroundColor,
    this.child,
    this.duration = const Duration(milliseconds: 500),
    this.curve = Curves.easeInOut,
  });

  @override
  State<AnimatedCircularProgress> createState() => _AnimatedCircularProgressState();
}

class _AnimatedCircularProgressState extends State<AnimatedCircularProgress> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  double _oldValue = 0.0;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.duration,
    );
    
    _animation = Tween<double>(
      begin: 0.0,
      end: widget.value,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: widget.curve,
      ),
    );
    
    _oldValue = widget.value;
    _controller.forward();
  }

  @override
  void didUpdateWidget(AnimatedCircularProgress oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.value != oldWidget.value) {
      _animation = Tween<double>(
        begin: _oldValue,
        end: widget.value,
      ).animate(
        CurvedAnimation(
          parent: _controller,
          curve: widget.curve,
        ),
      );
      
      _oldValue = widget.value;
      _controller.forward(from: 0.0);
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return SizedBox(
          width: widget.size,
          height: widget.size,
          child: Stack(
            alignment: Alignment.center,
            children: [
              SizedBox(
                width: widget.size,
                height: widget.size,
                child: CircularProgressIndicator(
                  value: 1.0,
                  strokeWidth: widget.strokeWidth,
                  backgroundColor: widget.backgroundColor,
                  valueColor: AlwaysStoppedAnimation<Color>(widget.backgroundColor),
                ),
              ),
              SizedBox(
                width: widget.size,
                height: widget.size,
                child: CircularProgressIndicator(
                  value: _animation.value,
                  strokeWidth: widget.strokeWidth,
                  backgroundColor: Colors.transparent,
                  valueColor: AlwaysStoppedAnimation<Color>(widget.foregroundColor),
                ),
              ),
              if (widget.child != null) widget.child!,
            ],
          ),
        );
      },
    );
  }
}

/// A widget that animates a linear progress indicator
class AnimatedLinearProgress extends StatefulWidget {
  final double value;
  final double height;
  final Color backgroundColor;
  final Color foregroundColor;
  final Duration duration;
  final Curve curve;
  final BorderRadius? borderRadius;

  const AnimatedLinearProgress({
    super.key,
    required this.value,
    this.height = 10.0,
    required this.backgroundColor,
    required this.foregroundColor,
    this.duration = const Duration(milliseconds: 500),
    this.curve = Curves.easeInOut,
    this.borderRadius,
  });

  @override
  State<AnimatedLinearProgress> createState() => _AnimatedLinearProgressState();
}

class _AnimatedLinearProgressState extends State<AnimatedLinearProgress> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  double _oldValue = 0.0;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.duration,
    );
    
    _animation = Tween<double>(
      begin: 0.0,
      end: widget.value,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: widget.curve,
      ),
    );
    
    _oldValue = widget.value;
    _controller.forward();
  }

  @override
  void didUpdateWidget(AnimatedLinearProgress oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.value != oldWidget.value) {
      _animation = Tween<double>(
        begin: _oldValue,
        end: widget.value,
      ).animate(
        CurvedAnimation(
          parent: _controller,
          curve: widget.curve,
        ),
      );
      
      _oldValue = widget.value;
      _controller.forward(from: 0.0);
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          height: widget.height,
          decoration: BoxDecoration(
            color: widget.backgroundColor,
            borderRadius: widget.borderRadius,
          ),
          child: FractionallySizedBox(
            widthFactor: _animation.value,
            alignment: Alignment.centerLeft,
            child: Container(
              decoration: BoxDecoration(
                color: widget.foregroundColor,
                borderRadius: widget.borderRadius,
              ),
            ),
          ),
        );
      },
    );
  }
}

/// A widget that animates a bar chart
class AnimatedBarChart extends StatefulWidget {
  final List<double> values;
  final List<String> labels;
  final double maxValue;
  final List<Color> colors;
  final double barWidth;
  final double spacing;
  final Duration duration;
  final Curve curve;
  final double height;
  final bool showLabels;
  final bool showValues;
  final TextStyle? labelStyle;
  final TextStyle? valueStyle;

  const AnimatedBarChart({
    super.key,
    required this.values,
    required this.labels,
    required this.maxValue,
    required this.colors,
    this.barWidth = 30.0,
    this.spacing = 20.0,
    this.duration = const Duration(milliseconds: 800),
    this.curve = Curves.easeOutCubic,
    this.height = 200.0,
    this.showLabels = true,
    this.showValues = true,
    this.labelStyle,
    this.valueStyle,
  }) : assert(values.length == labels.length, 'Values and labels must have the same length');

  @override
  State<AnimatedBarChart> createState() => _AnimatedBarChartState();
}

class _AnimatedBarChartState extends State<AnimatedBarChart> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late List<Animation<double>> _animations;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.duration,
    );
    
    _createAnimations();
    _controller.forward();
  }

  void _createAnimations() {
    _animations = List.generate(
      widget.values.length,
      (index) {
        return Tween<double>(
          begin: 0.0,
          end: widget.values[index] / widget.maxValue,
        ).animate(
          CurvedAnimation(
            parent: _controller,
            curve: Interval(
              index * 0.1,
              0.1 + index * 0.1,
              curve: widget.curve,
            ),
          ),
        );
      },
    );
  }

  @override
  void didUpdateWidget(AnimatedBarChart oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.values != oldWidget.values ||
        widget.maxValue != oldWidget.maxValue) {
      _createAnimations();
      _controller.forward(from: 0.0);
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: widget.height + (widget.showLabels ? 40.0 : 0.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisAlignment: MainAxisAlignment.center,
        children: List.generate(
          widget.values.length,
          (index) {
            final color = index < widget.colors.length
                ? widget.colors[index]
                : widget.colors[index % widget.colors.length];
            
            return Padding(
              padding: EdgeInsets.symmetric(horizontal: widget.spacing / 2),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  if (widget.showValues)
                    AnimatedBuilder(
                      animation: _animations[index],
                      builder: (context, child) {
                        return Opacity(
                          opacity: _animations[index].value,
                          child: Text(
                            widget.values[index].toStringAsFixed(1),
                            style: widget.valueStyle,
                          ),
                        );
                      },
                    ),
                  AnimatedBuilder(
                    animation: _animations[index],
                    builder: (context, child) {
                      return Container(
                        width: widget.barWidth,
                        height: widget.height * _animations[index].value,
                        decoration: BoxDecoration(
                          color: color,
                          borderRadius: const BorderRadius.vertical(
                            top: Radius.circular(4.0),
                          ),
                        ),
                      );
                    },
                  ),
                  if (widget.showLabels)
                    Padding(
                      padding: const EdgeInsets.only(top: 8.0),
                      child: SizedBox(
                        width: widget.barWidth + 10,
                        child: Text(
                          widget.labels[index],
                          style: widget.labelStyle,
                          textAlign: TextAlign.center,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}

/// A widget that animates a pie chart
class AnimatedPieChart extends StatefulWidget {
  final List<double> values;
  final List<Color> colors;
  final double size;
  final Duration duration;
  final Curve curve;
  final double strokeWidth;
  final Widget? centerWidget;

  const AnimatedPieChart({
    super.key,
    required this.values,
    required this.colors,
    this.size = 200.0,
    this.duration = const Duration(milliseconds: 800),
    this.curve = Curves.easeInOut,
    this.strokeWidth = 40.0,
    this.centerWidget,
  });

  @override
  State<AnimatedPieChart> createState() => _AnimatedPieChartState();
}

class _AnimatedPieChartState extends State<AnimatedPieChart> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.duration,
    );
    
    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: widget.curve,
      ),
    );
    
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: AnimatedBuilder(
        animation: _animation,
        builder: (context, child) {
          return Stack(
            alignment: Alignment.center,
            children: [
              CustomPaint(
                size: Size(widget.size, widget.size),
                painter: _PieChartPainter(
                  values: widget.values,
                  colors: widget.colors,
                  progress: _animation.value,
                  strokeWidth: widget.strokeWidth,
                ),
              ),
              if (widget.centerWidget != null) widget.centerWidget!,
            ],
          );
        },
      ),
    );
  }
}

class _PieChartPainter extends CustomPainter {
  final List<double> values;
  final List<Color> colors;
  final double progress;
  final double strokeWidth;

  _PieChartPainter({
    required this.values,
    required this.colors,
    required this.progress,
    required this.strokeWidth,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = (size.width - strokeWidth) / 2;
    
    final total = values.fold<double>(0, (sum, value) => sum + value);
    
    double startAngle = -math.pi / 2;
    
    for (int i = 0; i < values.length; i++) {
      final sweepAngle = (values[i] / total) * 2 * math.pi * progress;
      final color = i < colors.length ? colors[i] : colors[i % colors.length];
      
      final paint = Paint()
        ..style = PaintingStyle.stroke
        ..strokeWidth = strokeWidth
        ..color = color;
      
      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        startAngle,
        sweepAngle,
        false,
        paint,
      );
      
      startAngle += sweepAngle;
    }
  }

  @override
  bool shouldRepaint(_PieChartPainter oldDelegate) {
    return oldDelegate.progress != progress ||
           oldDelegate.values != values ||
           oldDelegate.colors != colors ||
           oldDelegate.strokeWidth != strokeWidth;
  }
}