import 'package:flutter/material.dart';

/// Timeline animation for ERP system
class AnimatedTimeline extends StatefulWidget {
  final List<TimelineItem> items;
  final Color lineColor;
  final Color activeLineColor;
  final Color inactiveLineColor;
  final Color activeNodeColor;
  final Color inactiveNodeColor;
  final double nodeSize;
  final double lineWidth;
  final Duration animationDuration;
  final Curve animationCurve;
  
  const AnimatedTimeline({
    super.key,
    required this.items,
    this.lineColor = Colors.grey,
    this.activeLineColor = Colors.blue,
    this.inactiveLineColor = Colors.grey,
    this.activeNodeColor = Colors.blue,
    this.inactiveNodeColor = Colors.grey,
    this.nodeSize = 16.0,
    this.lineWidth = 2.0,
    this.animationDuration = const Duration(milliseconds: 500),
    this.animationCurve = Curves.easeInOut,
  });
  
  @override
  State<AnimatedTimeline> createState() => _AnimatedTimelineState();
}

class _AnimatedTimelineState extends State<AnimatedTimeline> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late List<Animation<double>> _animations;
  
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.animationDuration,
    );
    
    // Create animations for each timeline item
    _animations = List.generate(
      widget.items.length,
      (index) {
        final start = index / widget.items.length;
        final end = (index + 1) / widget.items.length;
        
        return Tween<double>(begin: 0.0, end: 1.0).animate(
          CurvedAnimation(
            parent: _controller,
            curve: Interval(start, end, curve: widget.animationCurve),
          ),
        );
      },
    );
    
    _controller.forward();
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: widget.items.length,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemBuilder: (context, index) {
        final isLast = index == widget.items.length - 1;
        final isActive = widget.items[index].isActive;
        
        return AnimatedBuilder(
          animation: _animations[index],
          builder: (context, child) {
            return Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Timeline node and line
                Column(
                  children: [
                    // Node
                    Container(
                      width: widget.nodeSize,
                      height: widget.nodeSize,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: isActive 
                            ? widget.activeNodeColor 
                            : widget.inactiveNodeColor,
                        border: Border.all(
                          color: isActive 
                              ? widget.activeNodeColor 
                              : widget.inactiveNodeColor,
                          width: 2.0,
                        ),
                      ),
                      child: Center(
                        child: Icon(
                          widget.items[index].icon,
                          color: Colors.white,
                          size: widget.nodeSize * 0.6,
                        ),
                      ),
                    ),
                    
                    // Line (except for the last item)
                    if (!isLast)
                      Container(
                        width: widget.lineWidth,
                        height: 50.0 * _animations[index].value,
                        color: isActive 
                            ? widget.activeLineColor 
                            : widget.inactiveLineColor,
                      ),
                  ],
                ),
                
                const SizedBox(width: 16.0),
                
                // Content
                Expanded(
                  child: Opacity(
                    opacity: _animations[index].value,
                    child: Transform.translate(
                      offset: Offset(20 * (1 - _animations[index].value), 0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.items[index].title,
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16.0,
                              color: isActive 
                                  ? widget.activeNodeColor 
                                  : widget.inactiveNodeColor,
                            ),
                          ),
                          const SizedBox(height: 4.0),
                          Text(
                            widget.items[index].description,
                            style: TextStyle(
                              fontSize: 14.0,
                              color: Colors.grey[400],
                            ),
                          ),
                          const SizedBox(height: 16.0),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }
}

/// Data class for timeline items
class TimelineItem {
  final String title;
  final String description;
  final IconData icon;
  final bool isActive;
  
  const TimelineItem({
    required this.title,
    required this.description,
    required this.icon,
    this.isActive = false,
  });
}

/// Progress tracking animation for ERP system
class AnimatedProgressTracker extends StatefulWidget {
  final double progress;
  final double targetProgress;
  final Color backgroundColor;
  final Color progressColor;
  final double height;
  final double borderRadius;
  final Duration animationDuration;
  final Curve animationCurve;
  final String? label;
  final TextStyle? labelStyle;
  
  const AnimatedProgressTracker({
    super.key,
    required this.progress,
    required this.targetProgress,
    this.backgroundColor = Colors.grey,
    this.progressColor = Colors.blue,
    this.height = 16.0,
    this.borderRadius = 8.0,
    this.animationDuration = const Duration(milliseconds: 500),
    this.animationCurve = Curves.easeInOut,
    this.label,
    this.labelStyle,
  });
  
  @override
  State<AnimatedProgressTracker> createState() => _AnimatedProgressTrackerState();
}

class _AnimatedProgressTrackerState extends State<AnimatedProgressTracker> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _progressAnimation;
  
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.animationDuration,
    );
    
    _progressAnimation = Tween<double>(
      begin: widget.progress,
      end: widget.targetProgress,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: widget.animationCurve,
      ),
    );
    
    _controller.forward();
  }
  
  @override
  void didUpdateWidget(AnimatedProgressTracker oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (oldWidget.targetProgress != widget.targetProgress) {
      _progressAnimation = Tween<double>(
        begin: oldWidget.targetProgress,
        end: widget.targetProgress,
      ).animate(
        CurvedAnimation(
          parent: _controller,
          curve: widget.animationCurve,
        ),
      );
      
      _controller.reset();
      _controller.forward();
    }
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label (if provided)
        if (widget.label != null) ...[
          Text(
            widget.label!,
            style: widget.labelStyle ?? TextStyle(color: Colors.grey[300]),
          ),
          const SizedBox(height: 8.0),
        ],
        
        // Progress bar
        AnimatedBuilder(
          animation: _progressAnimation,
          builder: (context, child) {
            return Stack(
              children: [
                // Background
                Container(
                  height: widget.height,
                  decoration: BoxDecoration(
                    color: widget.backgroundColor,
                    borderRadius: BorderRadius.circular(widget.borderRadius),
                  ),
                ),
                
                // Progress
                Container(
                  height: widget.height,
                  width: MediaQuery.of(context).size.width * _progressAnimation.value,
                  decoration: BoxDecoration(
                    color: widget.progressColor,
                    borderRadius: BorderRadius.circular(widget.borderRadius),
                  ),
                ),
                
                // Percentage text
                Positioned.fill(
                  child: Center(
                    child: Text(
                      '${(_progressAnimation.value * 100).toInt()}%',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: widget.height * 0.7,
                      ),
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ],
    );
  }
}

/// Data comparison animation (before/after) for ERP system
class AnimatedDataComparison extends StatefulWidget {
  final String title;
  final double beforeValue;
  final double afterValue;
  final String unit;
  final Color positiveColor;
  final Color negativeColor;
  final Color neutralColor;
  final Duration animationDuration;
  final Curve animationCurve;
  
  const AnimatedDataComparison({
    super.key,
    required this.title,
    required this.beforeValue,
    required this.afterValue,
    this.unit = '',
    this.positiveColor = Colors.green,
    this.negativeColor = Colors.red,
    this.neutralColor = Colors.grey,
    this.animationDuration = const Duration(milliseconds: 800),
    this.animationCurve = Curves.easeInOut,
  });
  
  @override
  State<AnimatedDataComparison> createState() => _AnimatedDataComparisonState();
}

class _AnimatedDataComparisonState extends State<AnimatedDataComparison> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _beforeAnimation;
  late Animation<double> _afterAnimation;
  
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.animationDuration,
    );
    
    _beforeAnimation = Tween<double>(
      begin: 0.0,
      end: widget.beforeValue,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Interval(0.0, 0.5, curve: widget.animationCurve),
      ),
    );
    
    _afterAnimation = Tween<double>(
      begin: 0.0,
      end: widget.afterValue,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Interval(0.5, 1.0, curve: widget.animationCurve),
      ),
    );
    
    _controller.forward();
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    final difference = widget.afterValue - widget.beforeValue;
    final percentChange = widget.beforeValue != 0 
        ? (difference / widget.beforeValue) * 100 
        : 0.0;
    
    final comparisonColor = difference > 0 
        ? widget.positiveColor 
        : difference < 0 
            ? widget.negativeColor 
            : widget.neutralColor;
    
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Card(
          elevation: 4.0,
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Title
                Text(
                  widget.title,
                  style: TextStyle(
                    fontSize: 18.0,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                
                const SizedBox(height: 16.0),
                
                // Before value
                Row(
                  children: [
                    Text(
                      'Before: ',
                      style: TextStyle(
                        fontSize: 14.0,
                        color: Colors.grey[400],
                      ),
                    ),
                    Text(
                      '${_beforeAnimation.value.toStringAsFixed(1)}${widget.unit}',
                      style: TextStyle(
                        fontSize: 16.0,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 8.0),
                
                // After value
                Row(
                  children: [
                    Text(
                      'After: ',
                      style: TextStyle(
                        fontSize: 14.0,
                        color: Colors.grey[400],
                      ),
                    ),
                    Text(
                      '${_afterAnimation.value.toStringAsFixed(1)}${widget.unit}',
                      style: TextStyle(
                        fontSize: 16.0,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 16.0),
                
                // Comparison visualization
                Row(
                  children: [
                    // Before bar
                    Expanded(
                      flex: _beforeAnimation.value > 0 ? (_beforeAnimation.value * 10).toInt() : 1,
                      child: Container(
                        height: 24.0,
                        decoration: BoxDecoration(
                          color: Colors.grey[700],
                          borderRadius: BorderRadius.circular(4.0),
                        ),
                      ),
                    ),
                    
                    const SizedBox(width: 16.0),
                    
                    // After bar
                    Expanded(
                      flex: _afterAnimation.value > 0 ? (_afterAnimation.value * 10).toInt() : 1,
                      child: Container(
                        height: 24.0,
                        decoration: BoxDecoration(
                          color: comparisonColor,
                          borderRadius: BorderRadius.circular(4.0),
                        ),
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 16.0),
                
                // Percentage change
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      difference > 0 
                          ? Icons.arrow_upward 
                          : difference < 0 
                              ? Icons.arrow_downward 
                              : Icons.remove,
                      color: comparisonColor,
                      size: 16.0,
                    ),
                    
                    const SizedBox(width: 4.0),
                    
                    Text(
                      '${difference > 0 ? '+' : ''}${percentChange.toStringAsFixed(1)}%',
                      style: TextStyle(
                        fontSize: 16.0,
                        fontWeight: FontWeight.bold,
                        color: comparisonColor,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}