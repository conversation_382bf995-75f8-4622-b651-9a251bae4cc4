// ignore_for_file: unused_import, deprecated_member_use, use_super_parameters

import 'package:flutter/material.dart';
import '../constants/animation_constants.dart';

/// A widget that animates form field validation errors
class AnimatedErrorContainer extends StatefulWidget {
  final String? errorText;
  final TextStyle? errorStyle;
  final Duration duration;
  final Curve curve;

  const AnimatedErrorContainer({
    super.key,
    this.errorText,
    this.errorStyle,
    this.duration = const Duration(milliseconds: 200),
    this.curve = Curves.easeInOut,
  });

  @override
  State<AnimatedErrorContainer> createState() => _AnimatedErrorContainerState();
}

class _AnimatedErrorContainerState extends State<AnimatedErrorContainer> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _heightAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.duration,
    );
    
    _heightAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: widget.curve,
      ),
    );
    
    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: widget.curve,
      ),
    );
    
    if (widget.errorText != null && widget.errorText!.isNotEmpty) {
      _controller.forward();
    }
  }

  @override
  void didUpdateWidget(AnimatedErrorContainer oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.errorText != oldWidget.errorText) {
      if (widget.errorText != null && widget.errorText!.isNotEmpty) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final defaultErrorStyle = Theme.of(context).inputDecorationTheme.errorStyle ??
        const TextStyle(color: Colors.red, fontSize: 12.0);
    
    final errorStyle = widget.errorStyle ?? defaultErrorStyle;
    
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return ClipRect(
          child: Align(
            alignment: Alignment.topLeft,
            heightFactor: _heightAnimation.value,
            child: Opacity(
              opacity: _opacityAnimation.value,
              child: widget.errorText != null && widget.errorText!.isNotEmpty
                  ? Padding(
                      padding: const EdgeInsets.only(top: 4.0),
                      child: Text(
                        widget.errorText!,
                        style: errorStyle,
                      ),
                    )
                  : const SizedBox.shrink(),
            ),
          ),
        );
      },
    );
  }
}

/// A widget that animates form field focus
class AnimatedFormField extends StatefulWidget {
  final Widget child;
  final bool isFocused;
  final Duration duration;
  final Curve curve;
  final Color focusedColor;
  final Color unfocusedColor;
  final double focusedElevation;
  final double unfocusedElevation;
  final BorderRadius borderRadius;

  const AnimatedFormField({
    super.key,
    required this.child,
    required this.isFocused,
    this.duration = const Duration(milliseconds: 200),
    this.curve = Curves.easeInOut,
    required this.focusedColor,
    required this.unfocusedColor,
    this.focusedElevation = 4.0,
    this.unfocusedElevation = 1.0,
    this.borderRadius = const BorderRadius.all(Radius.circular(8.0)),
  });

  @override
  State<AnimatedFormField> createState() => _AnimatedFormFieldState();
}

class _AnimatedFormFieldState extends State<AnimatedFormField> {
  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: widget.duration,
      curve: widget.curve,
      decoration: BoxDecoration(
        color: widget.isFocused ? widget.focusedColor : widget.unfocusedColor,
        borderRadius: widget.borderRadius,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: widget.isFocused ? widget.focusedElevation : widget.unfocusedElevation,
            spreadRadius: widget.isFocused ? 1.0 : 0.0,
          ),
        ],
      ),
      child: widget.child,
    );
  }
}

/// A widget that animates form submission
class FormSubmitButton extends StatefulWidget {
  final VoidCallback onPressed;
  final String text;
  final String loadingText;
  final String successText;
  final bool isLoading;
  final bool isSuccess;
  final Duration loadingDuration;
  final Duration successDuration;
  final Color color;
  final Color loadingColor;
  final Color successColor;
  final TextStyle? textStyle;
  final double height;
  final double borderRadius;

  const FormSubmitButton({
    Key? key,
    required this.onPressed,
    required this.text,
    this.loadingText = 'Loading...',
    this.successText = 'Success!',
    this.isLoading = false,
    this.isSuccess = false,
    this.loadingDuration = const Duration(milliseconds: 300),
    this.successDuration = const Duration(milliseconds: 300),
    required this.color,
    required this.loadingColor,
    required this.successColor,
    this.textStyle,
    this.height = 50.0,
    this.borderRadius = 8.0,
  }) : super(key: key);

  @override
  State<FormSubmitButton> createState() => _FormSubmitButtonState();
}

class _FormSubmitButtonState extends State<FormSubmitButton> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<Color?> _colorAnimation;
  late Animation<double> _widthAnimation;
  
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.loadingDuration,
    );
    
    _updateAnimations();
    
    if (widget.isLoading) {
      _controller.forward();
    } else if (widget.isSuccess) {
      _controller.value = 1.0;
    }
  }
  
  void _updateAnimations() {
    _colorAnimation = ColorTween(
      begin: widget.color,
      end: widget.isSuccess ? widget.successColor : widget.loadingColor,
    ).animate(_controller);
    
    _widthAnimation = Tween<double>(
      begin: 1.0,
      end: widget.isSuccess ? 1.0 : 0.5,
    ).animate(_controller);
  }
  
  @override
  void didUpdateWidget(FormSubmitButton oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (oldWidget.isLoading != widget.isLoading || 
        oldWidget.isSuccess != widget.isSuccess) {
      _updateAnimations();
      
      if (widget.isLoading) {
        _controller.forward();
      } else if (widget.isSuccess) {
        _controller.value = 1.0;
      } else {
        _controller.reverse();
      }
    }
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Container(
          width: MediaQuery.of(context).size.width * _widthAnimation.value,
          height: widget.height,
          decoration: BoxDecoration(
            color: _colorAnimation.value,
            borderRadius: BorderRadius.circular(widget.borderRadius),
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: widget.isLoading || widget.isSuccess ? null : widget.onPressed,
              borderRadius: BorderRadius.circular(widget.borderRadius),
              child: Center(
                child: widget.isLoading
                    ? SizedBox(
                        width: 24.0,
                        height: 24.0,
                        child: CircularProgressIndicator(
                          strokeWidth: 2.0,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Text(
                        widget.isSuccess ? widget.successText : widget.text,
                        style: widget.textStyle ?? TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                      ),
              ),
            ),
          ),
        );
      },
    );
  }
}