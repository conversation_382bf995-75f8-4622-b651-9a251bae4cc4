import 'package:flutter/material.dart';
import 'package:visibility_detector/visibility_detector.dart';

/// A widget that animates items in a list with a staggered effect
class StaggeredListAnimation extends StatefulWidget {
  final List<Widget> children;
  final Duration itemDuration;
  final Duration staggerDuration;
  final Curve curve;
  final Axis direction;
  final MainAxisAlignment mainAxisAlignment;
  final CrossAxisAlignment crossAxisAlignment;
  final MainAxisSize mainAxisSize;

  const StaggeredListAnimation({
    super.key,
    required this.children,
    this.itemDuration = const Duration(milliseconds: 400),
    this.staggerDuration = const Duration(milliseconds: 50),
    this.curve = Curves.easeOut,
    this.direction = Axis.vertical,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    this.mainAxisSize = MainAxisSize.max,
  });

  @override
  State<StaggeredListAnimation> createState() => _StaggeredListAnimationState();
}

class _StaggeredListAnimationState extends State<StaggeredListAnimation> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late List<Animation<double>> _animations;

  @override
  void initState() {
    super.initState();
    
    // Calculate the total duration based on the number of items
    final totalDuration = Duration(
      milliseconds: widget.itemDuration.inMilliseconds +
          (widget.children.length - 1) * widget.staggerDuration.inMilliseconds,
    );
    
    _controller = AnimationController(
      vsync: this,
      duration: totalDuration,
    );
    
    // Create animations for each child with staggered delays
    _animations = List.generate(
      widget.children.length,
      (index) {
        final start = index * widget.staggerDuration.inMilliseconds / totalDuration.inMilliseconds;
        final end = start + widget.itemDuration.inMilliseconds / totalDuration.inMilliseconds;
        
        return Tween<double>(begin: 0.0, end: 1.0).animate(
          CurvedAnimation(
            parent: _controller,
            curve: Interval(start, end, curve: widget.curve),
          ),
        );
      },
    );
    
    // Start the animation
    _controller.forward();
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.direction == Axis.vertical
        ? Column(
            mainAxisAlignment: widget.mainAxisAlignment,
            crossAxisAlignment: widget.crossAxisAlignment,
            mainAxisSize: widget.mainAxisSize,
            children: _buildAnimatedChildren(),
          )
        : Row(
            mainAxisAlignment: widget.mainAxisAlignment,
            crossAxisAlignment: widget.crossAxisAlignment,
            mainAxisSize: widget.mainAxisSize,
            children: _buildAnimatedChildren(),
          );
  }
  
  List<Widget> _buildAnimatedChildren() {
    return List.generate(
      widget.children.length,
      (index) {
        return AnimatedBuilder(
          animation: _animations[index],
          builder: (context, child) {
            return Opacity(
              opacity: _animations[index].value,
              child: Transform.translate(
                offset: widget.direction == Axis.vertical
                    ? Offset(0, 20 * (1 - _animations[index].value))
                    : Offset(20 * (1 - _animations[index].value), 0),
                child: child,
              ),
            );
          },
          child: widget.children[index],
        );
      },
    );
  }
  
  // Method to manually trigger animations
  void animateItems() {
    _controller.reset();
    _controller.forward();
  }
}

/// A widget that animates items in a grid with a staggered effect
class StaggeredGridAnimation extends StatefulWidget {
  final List<Widget> children;
  final Duration itemDuration;
  final Duration staggerDuration;
  final Curve curve;
  final int crossAxisCount;
  final double childAspectRatio;
  final double crossAxisSpacing;
  final double mainAxisSpacing;

  const StaggeredGridAnimation({
    super.key,
    required this.children,
    this.itemDuration = const Duration(milliseconds: 400),
    this.staggerDuration = const Duration(milliseconds: 50),
    this.curve = Curves.easeOut,
    required this.crossAxisCount,
    this.childAspectRatio = 1.0,
    this.crossAxisSpacing = 10.0,
    this.mainAxisSpacing = 10.0,
  });

  @override
  State<StaggeredGridAnimation> createState() => _StaggeredGridAnimationState();
}

class _StaggeredGridAnimationState extends State<StaggeredGridAnimation> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late List<Animation<double>> _animations;

  @override
  void initState() {
    super.initState();
    
    // Calculate the total duration based on the number of items
    final totalDuration = Duration(
      milliseconds: widget.itemDuration.inMilliseconds +
          (widget.children.length - 1) * widget.staggerDuration.inMilliseconds,
    );
    
    _controller = AnimationController(
      vsync: this,
      duration: totalDuration,
    );
    
    // Create animations for each child with staggered delays
    _animations = List.generate(
      widget.children.length,
      (index) {
        final start = index * widget.staggerDuration.inMilliseconds / totalDuration.inMilliseconds;
        final end = start + widget.itemDuration.inMilliseconds / totalDuration.inMilliseconds;
        
        return Tween<double>(begin: 0.0, end: 1.0).animate(
          CurvedAnimation(
            parent: _controller,
            curve: Interval(start, end, curve: widget.curve),
          ),
        );
      },
    );
    
    // Start the animation
    _controller.forward();
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: widget.crossAxisCount,
        childAspectRatio: widget.childAspectRatio,
        crossAxisSpacing: widget.crossAxisSpacing,
        mainAxisSpacing: widget.mainAxisSpacing,
      ),
      itemCount: widget.children.length,
      shrinkWrap: true,
      physics: NeverScrollableScrollPhysics(),
      itemBuilder: (context, index) {
        return AnimatedBuilder(
          animation: _animations[index],
          builder: (context, child) {
            return Opacity(
              opacity: _animations[index].value,
              child: Transform.scale(
                scale: 0.8 + (0.2 * _animations[index].value),
                child: child,
              ),
            );
          },
          child: widget.children[index],
        );
      },
    );
  }
  
  // Method to manually trigger animations
  void animateItems() {
    _controller.reset();
    _controller.forward();
  }
}

/// A widget that animates list items when they appear in the viewport
class AnimatedListView extends StatefulWidget {
  final List<Widget> children;
  final Duration itemDuration;
  final Duration staggerDuration;
  final Curve curve;
  final ScrollPhysics? physics;
  final EdgeInsetsGeometry? padding;
  final bool shrinkWrap;
  final ScrollController? controller;

  const AnimatedListView({
    super.key,
    required this.children,
    this.itemDuration = const Duration(milliseconds: 400),
    this.staggerDuration = const Duration(milliseconds: 50),
    this.curve = Curves.easeOut,
    this.physics,
    this.padding,
    this.shrinkWrap = false,
    this.controller,
  });

  @override
  State<AnimatedListView> createState() => _AnimatedListViewState();
}

class _AnimatedListViewState extends State<AnimatedListView> {
  final List<bool> _visibleItems = [];

  @override
  void initState() {
    super.initState();
    _visibleItems.addAll(List.generate(widget.children.length, (_) => false));
  }

  @override
  void didUpdateWidget(AnimatedListView oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.children.length > _visibleItems.length) {
      _visibleItems.addAll(
        List.generate(
          widget.children.length - _visibleItems.length,
          (_) => false,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: widget.children.length,
      physics: widget.physics,
      padding: widget.padding,
      shrinkWrap: widget.shrinkWrap,
      controller: widget.controller,
      itemBuilder: (context, index) {
        return _buildAnimatedItem(index);
      },
    );
  }

  Widget _buildAnimatedItem(int index) {
    return VisibilityDetector(
      key: Key('item_$index'),
      onVisibilityChanged: (visibilityInfo) {
        final visiblePercentage = visibilityInfo.visibleFraction * 100;
        if (visiblePercentage > 10 && !_visibleItems[index]) {
          setState(() {
            _visibleItems[index] = true;
          });
        }
      },
      child: AnimatedSwitcher(
        duration: widget.itemDuration,
        child: _visibleItems[index]
            ? TweenAnimationBuilder<double>(
                tween: Tween<double>(begin: 0.0, end: 1.0),
                duration: widget.itemDuration,
                curve: widget.curve,
                builder: (context, value, child) {
                  return Opacity(
                    opacity: value,
                    child: Transform.translate(
                      offset: Offset(0, 20 * (1 - value)),
                      child: child,
                    ),
                  );
                },
                child: widget.children[index],
              )
            : SizedBox(
                height: 0,
                width: double.infinity,
              ),
      ),
    );
  }
}