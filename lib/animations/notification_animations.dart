// ignore_for_file: unused_import, deprecated_member_use

import 'package:flutter/material.dart';
import '../constants/animation_constants.dart';

/// Toast notification animation
class AnimatedToast extends StatefulWidget {
  final String message;
  final IconData? icon;
  final Color backgroundColor;
  final Color textColor;
  final Duration duration;
  final VoidCallback? onDismiss;
  final ToastPosition position;
  
  const AnimatedToast({
    super.key,
    required this.message,
    this.icon,
    this.backgroundColor = Colors.black87,
    this.textColor = Colors.white,
    this.duration = const Duration(seconds: 3),
    this.onDismiss,
    this.position = ToastPosition.bottom,
  });
  
  @override
  State<AnimatedToast> createState() => _AnimatedToastState();
}

enum ToastPosition { top, bottom, center }

class _AnimatedToastState extends State<AnimatedToast> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeOut,
        reverseCurve: Curves.easeIn,
      ),
    );
    
    _slideAnimation = Tween<Offset>(
      begin: _getStartOffset(),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeOut,
        reverseCurve: Curves.easeIn,
      ),
    );
    
    _controller.forward();
    
    // Auto-dismiss after duration
    Future.delayed(widget.duration, () {
      if (mounted) {
        _controller.reverse().then((_) {
          if (widget.onDismiss != null) {
            widget.onDismiss!();
          }
        });
      }
    });
  }
  
  Offset _getStartOffset() {
    switch (widget.position) {
      case ToastPosition.top:
        return const Offset(0.0, -1.0);
      case ToastPosition.bottom:
        return const Offset(0.0, 1.0);
      case ToastPosition.center:
        return const Offset(0.0, 0.3);
    }
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 16.0),
              padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
              decoration: BoxDecoration(
                color: widget.backgroundColor,
                borderRadius: BorderRadius.circular(8.0),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.2),
                    blurRadius: 8.0,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (widget.icon != null) ...[
                    Icon(
                      widget.icon,
                      color: widget.textColor,
                      size: 20.0,
                    ),
                    const SizedBox(width: 8.0),
                  ],
                  Flexible(
                    child: Text(
                      widget.message,
                      style: TextStyle(
                        color: widget.textColor,
                        fontSize: 14.0,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Alert/dialog entrance and exit animations
class AnimatedDialog extends StatefulWidget {
  final Widget child;
  final DialogAnimationType animationType;
  final Duration animationDuration;
  final Curve animationCurve;
  final VoidCallback? onDismiss;
  
  const AnimatedDialog({
    super.key,
    required this.child,
    this.animationType = DialogAnimationType.scale,
    this.animationDuration = const Duration(milliseconds: 300),
    this.animationCurve = Curves.easeInOut,
    this.onDismiss,
  });
  
  @override
  State<AnimatedDialog> createState() => _AnimatedDialogState();
}

enum DialogAnimationType { scale, fade, slideTop, slideBottom }

class _AnimatedDialogState extends State<AnimatedDialog> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.animationDuration,
    );
    
    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: widget.animationCurve,
      ),
    );
    
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: widget.animationCurve,
      ),
    );
    
    _slideAnimation = Tween<Offset>(
      begin: widget.animationType == DialogAnimationType.slideTop
          ? const Offset(0.0, -0.2)
          : const Offset(0.0, 0.2),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: widget.animationCurve,
      ),
    );
    
    _controller.forward();
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        Widget animatedChild = widget.child;
        
        // Apply animations based on type
        switch (widget.animationType) {
          case DialogAnimationType.scale:
            animatedChild = ScaleTransition(
              scale: _scaleAnimation,
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: widget.child,
              ),
            );
            break;
            
          case DialogAnimationType.fade:
            animatedChild = FadeTransition(
              opacity: _fadeAnimation,
              child: widget.child,
            );
            break;
            
          case DialogAnimationType.slideTop:
          case DialogAnimationType.slideBottom:
            animatedChild = SlideTransition(
              position: _slideAnimation,
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: widget.child,
              ),
            );
            break;
        }
        
        return GestureDetector(
          onTap: () {
            if (widget.onDismiss != null) {
              _controller.reverse().then((_) {
                widget.onDismiss!();
              });
            }
          },
          child: Container(
            color: Colors.transparent,
            child: Center(
              child: animatedChild,
            ),
          ),
        );
      },
    );
  }
}

/// Badge count animation
class AnimatedBadge extends StatefulWidget {
  final int count;
  final Color backgroundColor;
  final Color textColor;
  final double size;
  final Widget child;
  final Alignment alignment;
  final Duration animationDuration;
  
  const AnimatedBadge({
    super.key,
    required this.count,
    required this.child,
    this.backgroundColor = Colors.red,
    this.textColor = Colors.white,
    this.size = 20.0,
    this.alignment = Alignment.topRight,
    this.animationDuration = const Duration(milliseconds: 300),
  });
  
  @override
  State<AnimatedBadge> createState() => _AnimatedBadgeState();
}

class _AnimatedBadgeState extends State<AnimatedBadge> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.animationDuration,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.elasticOut,
      ),
    );
    
    _controller.forward();
  }
  
  @override
  void didUpdateWidget(AnimatedBadge oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.count != widget.count) {
      _controller.reset();
      _controller.forward();
    }
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: widget.alignment,
      children: [
        widget.child,
        if (widget.count > 0)
          ScaleTransition(
            scale: _scaleAnimation,
            child: Container(
              width: widget.size,
              height: widget.size,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: widget.backgroundColor,
                shape: BoxShape.circle,
              ),
              child: Text(
                widget.count > 99 ? '99+' : widget.count.toString(),
                style: TextStyle(
                  color: widget.textColor,
                  fontSize: widget.size * 0.6,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
      ],
    );
  }
}