// ignore_for_file: unused_import, annotate_overrides

import 'package:flutter/material.dart';
import '../constants/animation_constants.dart';

enum TransitionType {
  fade,
  scale,
  rightToLeft,
  leftToRight,
  bottomToTop,
  topToBottom,
  slideAndFade,
}

/// Slide transition from right to left (for forward navigation)
class SlideRightToLeftPageTransition extends PageRouteBuilder {
  final Widget page;
  
  SlideRightToLeftPageTransition({required this.page})
      : super(
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionDuration: const Duration(milliseconds: 300),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            var begin = const Offset(1.0, 0.0);
            var end = Offset.zero;
            var curve = Curves.easeInOut;
            
            var tween = Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
            var offsetAnimation = animation.drive(tween);
            
            return SlideTransition(position: offsetAnimation, child: child);
          },
        );
}

/// Slide transition from left to right (for backward navigation)
class SlideLeftToRightPageTransition extends PageRouteBuilder {
  final Widget page;
  
  SlideLeftToRightPageTransition({required this.page})
      : super(
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionDuration: const Duration(milliseconds: 300),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            var begin = const Offset(-1.0, 0.0);
            var end = Offset.zero;
            var curve = Curves.easeInOut;
            
            var tween = Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
            var offsetAnimation = animation.drive(tween);
            
            return SlideTransition(position: offsetAnimation, child: child);
          },
        );
}

/// Slide transition from bottom to top (for modal screens)
class SlideUpPageTransition extends PageRouteBuilder {
  final Widget page;
  
  SlideUpPageTransition({required this.page})
      : super(
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionDuration: const Duration(milliseconds: 300),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            var begin = const Offset(0.0, 1.0);
            var end = Offset.zero;
            var curve = Curves.easeInOut;
            
            var tween = Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
            var offsetAnimation = animation.drive(tween);
            
            return SlideTransition(position: offsetAnimation, child: child);
          },
        );
}

/// Fade transition (for dialogs and non-directional transitions)
class FadePageTransition extends PageRouteBuilder {
  final Widget page;
  
  FadePageTransition({required this.page})
      : super(
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionDuration: const Duration(milliseconds: 300),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            var curve = Curves.easeInOut;
            
            var tween = Tween(begin: 0.0, end: 1.0).chain(CurveTween(curve: curve));
            var opacityAnimation = animation.drive(tween);
            
            return FadeTransition(opacity: opacityAnimation, child: child);
          },
        );
}

/// Scale transition (for popping dialogs or focusing on elements)
class ScalePageTransition extends PageRouteBuilder {
  final Widget page;
  
  ScalePageTransition({required this.page})
      : super(
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionDuration: const Duration(milliseconds: 300),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            var curve = Curves.easeInOut;
            
            var scaleTween = Tween(begin: 0.8, end: 1.0).chain(CurveTween(curve: curve));
            var opacityTween = Tween(begin: 0.0, end: 1.0).chain(CurveTween(curve: curve));
            
            var scaleAnimation = animation.drive(scaleTween);
            var opacityAnimation = animation.drive(opacityTween);
            
            return FadeTransition(
              opacity: opacityAnimation,
              child: ScaleTransition(
                scale: scaleAnimation,
                child: child,
              ),
            );
          },
        );
}

/// Combined slide and fade transition
class SlideAndFadePageTransition extends PageRouteBuilder {
  final Widget page;
  final Offset beginOffset;
  
  SlideAndFadePageTransition({
    required this.page,
    this.beginOffset = const Offset(0.0, 0.2),
  }) : super(
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionDuration: const Duration(milliseconds: 300),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            var curve = Curves.easeInOut;
            
            var slideTween = Tween(begin: beginOffset, end: Offset.zero)
                .chain(CurveTween(curve: curve));
            var opacityTween = Tween(begin: 0.0, end: 1.0)
                .chain(CurveTween(curve: curve));
            
            var slideAnimation = animation.drive(slideTween);
            var opacityAnimation = animation.drive(opacityTween);
            
            return FadeTransition(
              opacity: opacityAnimation,
              child: SlideTransition(
                position: slideAnimation,
                child: child,
              ),
            );
          },
        );
}

/// Custom route transition for ERP system
class ERPPageTransition extends PageRouteBuilder {
  final Widget page;
  final RouteSettings settings;
  final TransitionType transitionType;
  
  ERPPageTransition({
    required this.page,
    required this.settings,
    this.transitionType = TransitionType.rightToLeft,
  }) : super(
          settings: settings,
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionDuration: const Duration(milliseconds: 300),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            switch (transitionType) {
              case TransitionType.fade:
                return FadeTransition(
                  opacity: Tween<double>(begin: 0.0, end: 1.0).animate(
                    CurvedAnimation(
                      parent: animation,
                      curve: Curves.easeInOut,
                    ),
                  ),
                  child: child,
                );
              
              case TransitionType.scale:
                return ScaleTransition(
                  scale: Tween<double>(begin: 0.8, end: 1.0).animate(
                    CurvedAnimation(
                      parent: animation,
                      curve: Curves.easeInOut,
                    ),
                  ),
                  child: FadeTransition(
                    opacity: Tween<double>(begin: 0.0, end: 1.0).animate(
                      CurvedAnimation(
                        parent: animation,
                        curve: Curves.easeInOut,
                      ),
                    ),
                    child: child,
                  ),
                );
              
              case TransitionType.rightToLeft:
                return SlideTransition(
                  position: Tween<Offset>(
                    begin: const Offset(1.0, 0.0),
                    end: Offset.zero,
                  ).animate(
                    CurvedAnimation(
                      parent: animation,
                      curve: Curves.easeInOut,
                    ),
                  ),
                  child: child,
                );
              
              case TransitionType.leftToRight:
                return SlideTransition(
                  position: Tween<Offset>(
                    begin: const Offset(-1.0, 0.0),
                    end: Offset.zero,
                  ).animate(
                    CurvedAnimation(
                      parent: animation,
                      curve: Curves.easeInOut,
                    ),
                  ),
                  child: child,
                );
              
              case TransitionType.bottomToTop:
                return SlideTransition(
                  position: Tween<Offset>(
                    begin: const Offset(0.0, 1.0),
                    end: Offset.zero,
                  ).animate(
                    CurvedAnimation(
                      parent: animation,
                      curve: Curves.easeInOut,
                    ),
                  ),
                  child: child,
                );
              
              case TransitionType.topToBottom:
                return SlideTransition(
                  position: Tween<Offset>(
                    begin: const Offset(0.0, -1.0),
                    end: Offset.zero,
                  ).animate(
                    CurvedAnimation(
                      parent: animation,
                      curve: Curves.easeInOut,
                    ),
                  ),
                  child: child,
                );
              
              case TransitionType.slideAndFade:
                return FadeTransition(
                  opacity: Tween<double>(begin: 0.0, end: 1.0).animate(
                    CurvedAnimation(
                      parent: animation,
                      curve: Curves.easeInOut,
                    ),
                  ),
                  child: SlideTransition(
                    position: Tween<Offset>(
                      begin: const Offset(0.0, 0.2),
                      end: Offset.zero,
                    ).animate(
                      CurvedAnimation(
                        parent: animation,
                        curve: Curves.easeInOut,
                      ),
                    ),
                    child: child,
                  ),
                );
            }
          },
        );
}

/// Helper class to navigate with custom transitions
class NavigationHelper {
  static Future<T?> push<T>(
    BuildContext context, 
    Widget page, {
    TransitionType transitionType = TransitionType.rightToLeft,
    String? routeName,
  }) {
    return Navigator.push<T>(
      context,
      ERPPageTransition(
        page: page,
        settings: RouteSettings(name: routeName),
        transitionType: transitionType,
      ) as Route<T>,
    );
  }
  
  static Future<T?> pushReplacement<T, TO>(
    BuildContext context, 
    Widget page, {
    TransitionType transitionType = TransitionType.rightToLeft,
    String? routeName,
  }) {
    return Navigator.pushReplacement<T, TO>(
      context,
      ERPPageTransition(
        page: page,
        settings: RouteSettings(name: routeName),
        transitionType: transitionType,
      ) as Route<T>,
    );
  }
  
  static Future<T?> pushAndRemoveUntil<T>(
    BuildContext context, 
    Widget page, 
    bool Function(Route<dynamic>) predicate, {
    TransitionType transitionType = TransitionType.rightToLeft,
    String? routeName,
  }) {
    return Navigator.pushAndRemoveUntil<T>(
      context,
      ERPPageTransition(
        page: page,
        settings: RouteSettings(name: routeName),
        transitionType: transitionType,
      ) as Route<T>,
      predicate,
    );
  }
}