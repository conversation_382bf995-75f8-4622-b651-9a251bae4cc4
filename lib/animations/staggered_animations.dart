// ignore_for_file: unused_import

import 'package:flutter/material.dart';
import '../constants/animation_constants.dart';

/// A widget that animates its children with a staggered delay
class StaggeredAnimationList extends StatefulWidget {
  final List<Widget> children;
  final Duration itemDuration;
  final Duration staggerDuration;
  final Curve curve;
  final Axis direction;
  final double startOffset;
  final bool animateOnInit;

  const StaggeredAnimationList({
    super.key,
    required this.children,
    this.itemDuration = const Duration(milliseconds: 300),
    this.staggerDuration = const Duration(milliseconds: 50),
    this.curve = Curves.easeInOut,
    this.direction = Axis.vertical,
    this.startOffset = 0.2,
    this.animateOnInit = true,
  });

  @override
  State<StaggeredAnimationList> createState() => _StaggeredAnimationListState();
}

class _StaggeredAnimationListState extends State<StaggeredAnimationList> with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _animations;

  @override
  void initState() {
    super.initState();
    
    _controllers = List.generate(
      widget.children.length,
      (index) => AnimationController(
        vsync: this,
        duration: widget.itemDuration,
      ),
    );
    
    _animations = _controllers.map((controller) {
      return Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(
          parent: controller,
          curve: widget.curve,
        ),
      );
    }).toList();
    
    if (widget.animateOnInit) {
      _playAnimations();
    }
  }
  
  void _playAnimations() {
    for (int i = 0; i < _controllers.length; i++) {
      Future.delayed(
        widget.staggerDuration * i,
        () {
          if (mounted && i < _controllers.length) {
            _controllers[i].forward();
          }
        },
      );
    }
  }

  @override
  void dispose() {
    for (final controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.direction == Axis.vertical
        ? Column(
            children: _buildAnimatedChildren(),
          )
        : Row(
            children: _buildAnimatedChildren(),
          );
  }

  List<Widget> _buildAnimatedChildren() {
    return List.generate(
      widget.children.length,
      (index) {
        return AnimatedBuilder(
          animation: _animations[index],
          builder: (context, child) {
            final offset = widget.direction == Axis.vertical
                ? Offset(0, (1 - _animations[index].value) * widget.startOffset)
                : Offset((1 - _animations[index].value) * widget.startOffset, 0);
                
            return Opacity(
              opacity: _animations[index].value,
              child: Transform.translate(
                offset: offset,
                child: child,
              ),
            );
          },
          child: widget.children[index],
        );
      },
    );
  }
  
  void animateItems() {
    _playAnimations();
  }
}

/// A widget that animates its children with a staggered delay in a grid
class StaggeredAnimationGrid extends StatefulWidget {
  final List<Widget> children;
  final Duration itemDuration;
  final Duration staggerDuration;
  final Curve curve;
  final int crossAxisCount;
  final double childAspectRatio;
  final double crossAxisSpacing;
  final double mainAxisSpacing;
  final double startOffset;

  const StaggeredAnimationGrid({
    super.key,
    required this.children,
    this.itemDuration = const Duration(milliseconds: 300),
    this.staggerDuration = const Duration(milliseconds: 50),
    this.curve = Curves.easeInOut,
    required this.crossAxisCount,
    this.childAspectRatio = 1.0,
    this.crossAxisSpacing = 10.0,
    this.mainAxisSpacing = 10.0,
    this.startOffset = 0.2,
  });

  @override
  State<StaggeredAnimationGrid> createState() => _StaggeredAnimationGridState();
}

class _StaggeredAnimationGridState extends State<StaggeredAnimationGrid> with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _animations;

  @override
  void initState() {
    super.initState();
    
    _controllers = List.generate(
      widget.children.length,
      (index) => AnimationController(
        vsync: this,
        duration: widget.itemDuration,
      ),
    );
    
    _animations = _controllers.map((controller) {
      return Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(
          parent: controller,
          curve: widget.curve,
        ),
      );
    }).toList();
    
    _playAnimations();
  }
  
  void _playAnimations() {
    for (int i = 0; i < _controllers.length; i++) {
      Future.delayed(
        widget.staggerDuration * i,
        () {
          if (mounted && i < _controllers.length) {
            _controllers[i].forward();
          }
        },
      );
    }
  }

  @override
  void dispose() {
    for (final controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: widget.crossAxisCount,
        childAspectRatio: widget.childAspectRatio,
        crossAxisSpacing: widget.crossAxisSpacing,
        mainAxisSpacing: widget.mainAxisSpacing,
      ),
      itemCount: widget.children.length,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemBuilder: (context, index) {
        return AnimatedBuilder(
          animation: _animations[index],
          builder: (context, child) {
            return Opacity(
              opacity: _animations[index].value,
              child: Transform.translate(
                offset: Offset(
                  0,
                  (1 - _animations[index].value) * 50,
                ),
                child: Transform.scale(
                  scale: 0.8 + (0.2 * _animations[index].value),
                  child: child,
                ),
              ),
            );
          },
          child: widget.children[index],
        );
      },
    );
  }
}