// ignore_for_file: unused_import, unused_local_variable, annotate_overrides

import 'package:flutter/material.dart';
import '../constants/animation_constants.dart';

/// Custom tab transition for ERP system
class ERPTabTransition extends StatelessWidget {
  final Animation<double> animation;
  final Animation<double> secondaryAnimation;
  final Widget child;
  
  const ERPTabTransition({
    super.key,
    required this.animation,
    required this.secondaryAnimation,
    required this.child,
  });
  
  @override
  Widget build(BuildContext context) {
    // For the entering tab
    var enteringAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: animation,
        curve: Curves.easeInOut,
      ),
    );
    
    // For the exiting tab
    var exitingAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(
      CurvedAnimation(
        parent: secondaryAnimation,
        curve: Curves.easeInOut,
      ),
    );
    
    return Stack(
      children: [
        // Exiting tab fades out
        FadeTransition(
          opacity: exitingAnimation,
          child: ScaleTransition(
            scale: Tween<double>(
              begin: 1.0,
              end: 0.95,
            ).animate(secondaryAnimation),
            child: Container(),
          ),
        ),
        
        // Entering tab fades in and slides up slightly
        FadeTransition(
          opacity: enteringAnimation,
          child: SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(0.0, 0.05),
              end: Offset.zero,
            ).animate(animation),
            child: child,
          ),
        ),
      ],
    );
  }
}

/// Custom tab controller for ERP system
class ERPTabController extends StatefulWidget {
  final List<Widget> tabs;
  final List<Widget> tabViews;
  final Color activeColor;
  final Color inactiveColor;
  final Color backgroundColor;
  final double height;
  final double indicatorHeight;
  final double indicatorWidth;
  final double borderRadius;
  
  const ERPTabController({
    super.key,
    required this.tabs,
    required this.tabViews,
    this.activeColor = Colors.blue,
    this.inactiveColor = Colors.grey,
    this.backgroundColor = Colors.black,
    this.height = 50.0,
    this.indicatorHeight = 3.0,
    this.indicatorWidth = 20.0,
    this.borderRadius = 8.0,
  });
  
  @override
  State<ERPTabController> createState() => _ERPTabControllerState();
}

class _ERPTabControllerState extends State<ERPTabController> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: widget.tabs.length, vsync: this);
  }
  
  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Custom tab bar
        Container(
          height: widget.height,
          decoration: BoxDecoration(
            color: widget.backgroundColor,
            borderRadius: BorderRadius.circular(widget.borderRadius),
          ),
          child: TabBar(
            controller: _tabController,
            indicator: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: widget.activeColor,
                  width: widget.indicatorHeight,
                ),
              ),
            ),
            labelColor: widget.activeColor,
            unselectedLabelColor: widget.inactiveColor,
            tabs: widget.tabs,
          ),
        ),
        
        // Tab content with custom transitions
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: widget.tabViews.map((view) {
              return ERPTabTransition(
                animation: _tabController.animation!,
                secondaryAnimation: _tabController.animation!.drive(
                  Tween<double>(begin: 1.0, end: 0.0)
                ),
                child: view,
              );
            }).toList(),
          ),
        ),
      ],
    );
  }
}

/// Enhanced tab transition with custom controller
class CustomTabPageTransition extends StatefulWidget {
  final TabController controller;
  final List<Widget> tabs;
  final List<Widget> tabViews;
  final Color indicatorColor;
  final Color labelColor;
  final Color unselectedLabelColor;
  final TextStyle? labelStyle;
  final TextStyle? unselectedLabelStyle;
  final double indicatorWeight;
  final EdgeInsetsGeometry indicatorPadding;
  final Duration animationDuration;
  final Curve animationCurve;
  
  const CustomTabPageTransition({
    super.key,
    required this.controller,
    required this.tabs,
    required this.tabViews,
    this.indicatorColor = Colors.blue,
    this.labelColor = Colors.blue,
    this.unselectedLabelColor = Colors.grey,
    this.labelStyle,
    this.unselectedLabelStyle,
    this.indicatorWeight = 3.0,
    this.indicatorPadding = EdgeInsets.zero,
    this.animationDuration = const Duration(milliseconds: 300),
    this.animationCurve = Curves.easeInOut,
  });
  
  @override
  State<CustomTabPageTransition> createState() => _CustomTabPageTransitionState();
}

class _CustomTabPageTransitionState extends State<CustomTabPageTransition> with SingleTickerProviderStateMixin {
  late PageController _pageController;
  
  @override
  void initState() {
    super.initState();
    _pageController = PageController(initialPage: widget.controller.index);
    
    widget.controller.addListener(_handleTabChange);
  }
  
  void _handleTabChange() {
    _pageController.animateToPage(
      widget.controller.index,
      duration: widget.animationDuration,
      curve: widget.animationCurve,
    );
  }
  
  @override
  void dispose() {
    widget.controller.removeListener(_handleTabChange);
    _pageController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Tab bar
        TabBar(
          controller: widget.controller,
          tabs: widget.tabs,
          indicatorColor: widget.indicatorColor,
          labelColor: widget.labelColor,
          unselectedLabelColor: widget.unselectedLabelColor,
          labelStyle: widget.labelStyle,
          unselectedLabelStyle: widget.unselectedLabelStyle,
          indicatorWeight: widget.indicatorWeight,
          indicatorPadding: widget.indicatorPadding,
        ),
        
        // Tab content with custom page transition
        Expanded(
          child: PageView(
            controller: _pageController,
            physics: const NeverScrollableScrollPhysics(),
            children: widget.tabViews,
            onPageChanged: (index) {
              if (widget.controller.index != index) {
                widget.controller.animateTo(index);
              }
            },
          ),
        ),
      ],
    );
  }
}

/// Animated tab indicator for custom tab bars
class AnimatedTabIndicator extends Decoration {
  final double height;
  final Color color;
  final double radius;
  final EdgeInsetsGeometry padding;
  final double width;
  
  const AnimatedTabIndicator({
    this.height = 4.0,
    required this.color,
    this.radius = 2.0,
    this.padding = const EdgeInsets.symmetric(horizontal: 8.0),
    this.width = 20.0,
  });
  
  @override
  BoxPainter createBoxPainter([VoidCallback? onChanged]) {
    return _AnimatedTabIndicatorPainter(
      height: height,
      color: color,
      radius: radius,
      padding: padding,
      width: width,
    );
  }
}

class _AnimatedTabIndicatorPainter extends BoxPainter {
  final double height;
  final Color color;
  final double radius;
  final EdgeInsetsGeometry padding;
  final double width;
  
  _AnimatedTabIndicatorPainter({
    required this.height,
    required this.color,
    required this.radius,
    required this.padding,
    required this.width,
  });
  
  @override
  void paint(Canvas canvas, Offset offset, ImageConfiguration configuration) {
    assert(configuration.size != null);
    
    final Rect rect = offset & configuration.size!;
    final Paint paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;
    
    final Offset paddingOffset = padding.resolve(TextDirection.ltr).topLeft;
    final double indicatorWidth = width;
    
    final Rect indicatorRect = Rect.fromLTWH(
      rect.left + (rect.width - indicatorWidth) / 2,
      rect.bottom - height,
      indicatorWidth,
      height,
    );
    
    canvas.drawRRect(
      RRect.fromRectAndRadius(indicatorRect, Radius.circular(radius)),
      paint,
    );
  }
}