import 'package:flutter/material.dart';

class AnimationConstants {
  // Duration
  static const Duration shortDuration = Duration(milliseconds: 150);
  static const Duration mediumDuration = Duration(milliseconds: 300);
  static const Duration longDuration = Duration(milliseconds: 500);
  static const Duration extraLongDuration = Duration(milliseconds: 800);

  // Curves
  static const standardCurve = Curves.easeInOut;
  static const accelerateCurve = Curves.easeIn;
  static const decelerateCurve = Curves.easeOut;
  static const sharpCurve = Curves.easeInOutCubic;
  static const bounceCurve = Curves.elasticOut;
  static const anticipateCurve = Curves.easeInBack;
  static const overshootCurve = Curves.easeOutBack;
  
  // Page Transitions
  static const pageTransitionDuration = Duration(milliseconds: 300);
  static const dialogTransitionDuration = Duration(milliseconds: 250);
  static const modalBottomSheetDuration = Duration(milliseconds: 350);
  
  // Staggered Animations
  static const staggeredDelay = Duration(milliseconds: 50);
  static const listItemStaggerDelay = Duration(milliseconds: 30);
  
  // Specific Animation Durations
  static const fabAnimationDuration = Duration(milliseconds: 200);
  static const cardExpandDuration = Duration(milliseconds: 250);
  static const loadingIndicatorDuration = Duration(milliseconds: 1500);
  static const refreshIndicatorDuration = Duration(milliseconds: 1000);
  static const tooltipAnimationDuration = Duration(milliseconds: 200);
  static const snackbarAnimationDuration = Duration(milliseconds: 250);
  static const tabTransitionDuration = Duration(milliseconds: 300);
  static const drawerTransitionDuration = Duration(milliseconds: 250);
  
  // Animation Values
  static const double scaleFactorSmall = 0.95;
  static const double scaleFactorMedium = 0.9;
  static const double scaleFactorLarge = 0.8;
  static const double opacityFactorLight = 0.8;
  static const double opacityFactorMedium = 0.6;
  static const double opacityFactorHeavy = 0.4;
  static const double slideOffset = 20.0;
  static const double slideOffsetLarge = 50.0;
}