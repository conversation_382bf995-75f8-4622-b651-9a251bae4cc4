class ApiConstants {
  // Base URLs
  static const String baseUrl = 'https://backend2.hemmasportacademy.com';

  // Customer Endpoints
  static const String customersSelectAll = '$baseUrl/customers/select_all.php';
  static const String customersSelect = '$baseUrl/customers/select_all.php';
  static const String customersCrud = '$baseUrl/crud.php?table=customers';

  // Sports Endpoints
  static const String sportsSelect = '$baseUrl/fetch_sports.php';
  static const String sportsCrud = '$baseUrl/crud.php?table=sports';

  // Stadium Endpoints
  static const String stadiumsSelect = '$baseUrl/stadiums/select_stadiums.php';
  static const String stadiumsInsert = '$baseUrl/stadiums/insert_stadium.php';
  static const String stadiumsUpdate = '$baseUrl/stadiums/update_stadium.php';
  static const String stadiumsDelete = '$baseUrl/stadiums/delete_stadium.php';

  // Stadium Booking Endpoints
  static const String stadiumBookingsSelect =
      '$baseUrl/stadium_bookings/select_stadium_bookings.php';
  static const String stadiumBookingsInsert =
      '$baseUrl/stadium_bookings/insert_stadium_booking.php';
  static const String stadiumBookingsUpdate =
      '$baseUrl/stadium_bookings/update_stadium_booking.php';
  static const String stadiumBookingsDelete =
      '$baseUrl/stadium_bookings/delete_stadium_booking.php';

  // Visit Booking Endpoints
  static const String visitBookingsSelect =
      '$baseUrl/booking/select_visit_bookings.php';
  static const String visitBookingsInsert =
      '$baseUrl/booking/insert_visit_booking.php';

  // Employee Endpoints
  static const String employeesSelect =
      '$baseUrl/employees/select_employee.php';

  // Payroll Endpoints
  static const String payrollSelect = '$baseUrl/payroll/select_payroll.php';
  static const String payrollInsert = '$baseUrl/payroll/insert_payroll.php';
  static const String payrollUpdate = '$baseUrl/payroll/update_payroll.php';
  static const String payrollDelete = '$baseUrl/payroll/delete_payroll.php';

  // Trainer Endpoints
  static const String trainersSelect = '$baseUrl/trainers/get_trainers.php';

  // Time Planning Endpoints
  static const String sportTimePlan = '$baseUrl/timeplan/sport_time_plan.php';
  static const String trainerTrainees =
      '$baseUrl/timeplan/trainer_trainees.php';

  // General Ledger Endpoints
  static const String generalLedgerSelect =
      '$baseUrl/gledger/select_customer.php';

  // Promo Code Endpoints
  static const String promoCodesSelect =
      '$baseUrl/promocodes/get/promocode_select.php';
  static const String promoCodesInsert =
      '$baseUrl/promocodes/insert/promocode_insert.php';
  static const String promoCodesUpdate =
      '$baseUrl/promocodes/update/promocode_update.php';
  static const String promoCodesDelete =
      '$baseUrl/promocodes/delete/promocodes_delete.php';
  static const String promoCodeEligibilitySelect =
      '$baseUrl/promocodes/get/select_promo_code_eligibility.php';
  static const String promoCodeEligibilityInsert =
      '$baseUrl/promocodes/insert/insert_promo_code_eligibility.php';
  static const String promoCodeEligibilityDelete =
      '$baseUrl/promocodes/delete/delete_promo_code_eligibility.php';
  static const String promoCodeUsagesSelect =
      '$baseUrl/promocodes/get/select_promo_code_usages.php';
  static const String promoCodeUsagesInsert =
      '$baseUrl/promocodes/insert/insert_promo_code_usage.php';
  static const String promoCodeUsagesDelete =
      '$baseUrl/promocodes/delete/delete_promo_code_usage.php';

  // Timeout
  static const int connectionTimeout = 30000; // 30 seconds
  static const int receiveTimeout = 30000; // 30 seconds
}
