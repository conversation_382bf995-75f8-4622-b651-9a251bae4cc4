// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'colors.dart'; // This imports both AppColors and ModernAppColors
import 'text_styles.dart';

/// App theme configuration
class AppTheme {
  /// Get light theme data
  /// Based on Argon Dashboard Chakra UI design
  static ThemeData getLightTheme() {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      primaryColor: ModernAppColors.primary,
      colorScheme: ColorScheme.light(
        primary: ModernAppColors.primary,
        secondary: ModernAppColors.secondary,
        surface: ModernAppColors.surface,
        background: ModernAppColors.background,
        error: ModernAppColors.error,
        onPrimary: ModernAppColors.onPrimary, // White
        onSecondary: ModernAppColors.onSecondary, // Black
        onSurface: ModernAppColors.onSurface, // Dark Gray / Off-Black
        onBackground: ModernAppColors.onBackground, // Dark Gray / Off-Black
        onError: ModernAppColors.onPrimary, // White
      ),
      scaffoldBackgroundColor: ModernAppColors.background,
      cardColor: ModernAppColors.cardColor,
      dividerColor: ModernAppColors.dividerColor,
      
      // Text Theme
      textTheme: TextTheme(
        displayLarge: TextStyles.heading1,
        displayMedium: TextStyles.heading2,
        displaySmall: TextStyles.heading3,
        headlineMedium: TextStyles.titleLarge,
        titleLarge: TextStyles.titleMedium,
        titleMedium: TextStyles.titleSmall,
        bodyLarge: TextStyles.bodyLarge,
        bodyMedium: TextStyles.bodyMedium,
        bodySmall: TextStyles.bodySmall,
        labelLarge: TextStyles.labelLarge,
        labelMedium: TextStyles.labelMedium,
        labelSmall: TextStyles.labelSmall,
      ),
      
      // AppBar Theme - Argon Dashboard style
      appBarTheme: AppBarTheme(
        backgroundColor: ModernAppColors.primary,
        foregroundColor: ModernAppColors.onPrimary,
        elevation: 4,
        centerTitle: false,
        titleTextStyle: TextStyles.titleLarge.copyWith(
          color: ModernAppColors.onPrimary,
          fontWeight: FontWeight.w600,
          letterSpacing: 0.5,
        ),
        shadowColor: ModernAppColors.primary.withOpacity(0.3),
        toolbarHeight: 64,
      ),
      
      // Button Themes - Argon Dashboard style
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: ModernAppColors.primary,
          foregroundColor: ModernAppColors.onPrimary,
          textStyle: TextStyles.buttonText.copyWith(
            fontWeight: FontWeight.w600,
            letterSpacing: 0.5,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(6),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          elevation: 2,
          shadowColor: ModernAppColors.primary.withOpacity(0.3),
        ).copyWith(
          overlayColor: MaterialStateProperty.resolveWith<Color?>(
            (Set<MaterialState> states) {
              if (states.contains(MaterialState.hovered)) {
                return ModernAppColors.primaryDark.withOpacity(0.1);
              }
              if (states.contains(MaterialState.pressed)) {
                return ModernAppColors.primaryDark.withOpacity(0.2);
              }
              return null;
            },
          ),
        ),
      ),
      
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: ModernAppColors.primary,
          textStyle: TextStyles.buttonText,
          side: BorderSide(color: ModernAppColors.primary),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(6),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        ),
      ),
      
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: ModernAppColors.primary,
          textStyle: TextStyles.buttonText,
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        ),
      ),
      // Input Decoration Theme - Argon Dashboard style
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: ModernAppColors.surface, // White
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: BorderSide(color: ModernAppColors.borderColor.withOpacity(0.2), width: 1),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: BorderSide(color: ModernAppColors.borderColor.withOpacity(0.2), width: 1),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: BorderSide(color: ModernAppColors.primary, width: 1.5),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: BorderSide(color: ModernAppColors.error, width: 1),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: BorderSide(color: ModernAppColors.error, width: 1.5),
        ),
        hintStyle: TextStyle(color: ModernAppColors.textHint.withOpacity(0.6)),
        labelStyle: TextStyle(color: ModernAppColors.textSecondary),
        floatingLabelStyle: TextStyle(color: ModernAppColors.primary),
        errorStyle: TextStyles.errorText.copyWith(color: ModernAppColors.error), // Ensure error text uses error color
        ),
    
      // Card Theme - Argon Dashboard style
      cardTheme: CardTheme(
        color: ModernAppColors.cardColor,
        elevation: 1,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(6),
        ),
        margin: const EdgeInsets.all(8),
      ),
      
      // Dialog Theme
      dialogTheme: DialogTheme(
        backgroundColor: ModernAppColors.surface,
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        titleTextStyle: TextStyles.titleMedium,
        contentTextStyle: TextStyles.bodyMedium,
      ),
      
      // Checkbox Theme
      checkboxTheme: CheckboxThemeData(
        fillColor: MaterialStateProperty.resolveWith<Color>((states) {
          if (states.contains(MaterialState.selected)) {
            return ModernAppColors.primary;
          }
          return Colors.transparent; // Or ModernAppColors.surface if a background is always desired
        }),
        checkColor: MaterialStateProperty.all(ModernAppColors.onPrimary),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(4),
        ),
      ),
      
      // Switch Theme
      switchTheme: SwitchThemeData(
        thumbColor: MaterialStateProperty.resolveWith<Color>((states) {
          if (states.contains(MaterialState.selected)) {
            return ModernAppColors.primary;
          }
          return ModernAppColors.disabledColor; // Use a color from the palette for disabled state
        }),
        trackColor: MaterialStateProperty.resolveWith<Color>((states) {
          if (states.contains(MaterialState.selected)) {
            return ModernAppColors.primary.withOpacity(0.5);
          }
          return ModernAppColors.disabledColor.withOpacity(0.3); // Lighter track for disabled
        }),
      ),
      
      // Divider Theme
      dividerTheme: DividerThemeData(
        color: ModernAppColors.dividerColor,
        thickness: 1,
        space: 16,
      ),
      
      // Tab Bar Theme
      tabBarTheme: TabBarTheme(
        labelColor: ModernAppColors.primary,
        unselectedLabelColor: ModernAppColors.textSecondary,
        indicatorColor: ModernAppColors.primary,
        labelStyle: TextStyles.labelMedium.copyWith(fontWeight: FontWeight.bold),
        unselectedLabelStyle: TextStyles.labelMedium,
      ),
    );
  }

  /// Get dark theme data
  static ThemeData getDarkTheme() {
    // Define base dark colors. These could be added to ModernAppColors if widely used.
    const Color darkBackground = Color(0xFF111827); // Dark Cool Gray
    const Color darkSurface = Color(0xFF1F2937);    // Slightly Lighter Cool Gray
    const Color lightTextOnDark = ModernAppColors.surface; // White, for text on dark backgrounds

    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      primaryColor: ModernAppColors.accent, // Vibrant Teal as primary for dark theme
      colorScheme: ColorScheme.dark(
        primary: ModernAppColors.accent, // Vibrant Teal
        secondary: ModernAppColors.primaryLight, // Lighter Deep Blue as secondary
        surface: darkSurface,
        background: darkBackground,
        error: ModernAppColors.error, // Red
        onPrimary: ModernAppColors.onAccent, // Black (text on Teal)
        onSecondary: lightTextOnDark, // White text on Lighter Deep Blue (ModernAppColors.primaryLight)
        onSurface: lightTextOnDark, // White text on darkSurface
        onBackground: lightTextOnDark, // White text on darkBackground
        onError: ModernAppColors.onPrimary, // White text on Red
      ),
      scaffoldBackgroundColor: darkBackground,
      cardColor: darkSurface,
      dividerColor: ModernAppColors.secondaryDark, // Darker Gray from palette (originally BDBDBD)
      
      // Text Theme - Ensure all text is legible on dark backgrounds
      textTheme: TextTheme(
        displayLarge: TextStyles.heading1.copyWith(color: lightTextOnDark),
        displayMedium: TextStyles.heading2.copyWith(color: lightTextOnDark),
        displaySmall: TextStyles.heading3.copyWith(color: lightTextOnDark),
        headlineMedium: TextStyles.titleLarge.copyWith(color: lightTextOnDark),
        titleLarge: TextStyles.titleMedium.copyWith(color: lightTextOnDark),
        titleMedium: TextStyles.titleSmall.copyWith(color: lightTextOnDark),
        bodyLarge: TextStyles.bodyLarge.copyWith(color: lightTextOnDark),
        bodyMedium: TextStyles.bodyMedium.copyWith(color: lightTextOnDark),
        bodySmall: TextStyles.bodySmall.copyWith(color: lightTextOnDark),
        labelLarge: TextStyles.labelLarge.copyWith(color: lightTextOnDark),
        labelMedium: TextStyles.labelMedium.copyWith(color: lightTextOnDark),
        labelSmall: TextStyles.labelSmall.copyWith(color: lightTextOnDark),
      ).apply(
        bodyColor: lightTextOnDark,
        displayColor: lightTextOnDark,
      ),
      
      // AppBar Theme
      appBarTheme: AppBarTheme(
        backgroundColor: darkSurface, // Use dark surface for AppBar
        foregroundColor: lightTextOnDark, // White text
        elevation: 0, // Flatter design for dark mode often preferred
        centerTitle: false,
        titleTextStyle: TextStyles.titleLarge.copyWith(
          color: lightTextOnDark,
          fontWeight: FontWeight.w600,
          letterSpacing: 0.5,
        ),
        shadowColor: ModernAppColors.accent.withOpacity(0.3), // Teal shadow
      ),

      // Button Themes - Adapted for dark mode with Teal as primary
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: ModernAppColors.accent, // Teal
          foregroundColor: ModernAppColors.onAccent, // Black text on Teal
          textStyle: TextStyles.buttonText.copyWith(
            fontWeight: FontWeight.w600,
            letterSpacing: 0.5,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(6),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          elevation: 2,
          shadowColor: ModernAppColors.accent.withOpacity(0.3),
        ).copyWith(
          overlayColor: MaterialStateProperty.resolveWith<Color?>(
            (Set<MaterialState> states) {
              if (states.contains(MaterialState.hovered)) {
                return ModernAppColors.accentDark.withOpacity(0.1);
              }
              if (states.contains(MaterialState.pressed)) {
                return ModernAppColors.accentDark.withOpacity(0.2);
              }
              return null;
            },
          ),
        ),
      ),
      
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: ModernAppColors.accentLight, // Lighter Teal for outlines
          textStyle: TextStyles.buttonText,
          side: BorderSide(color: ModernAppColors.accentLight),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(6),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        ),
      ),
      
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: ModernAppColors.accentLight, // Lighter Teal for text buttons
          textStyle: TextStyles.buttonText,
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        ),
      ),

      // Input Decoration Theme - Adapted for dark mode
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: darkSurface, // Darker fill for inputs
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: BorderSide(color: ModernAppColors.secondaryDark.withOpacity(0.5), width: 1), // Softer border
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: BorderSide(color: ModernAppColors.secondaryDark.withOpacity(0.5), width: 1),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: BorderSide(color: ModernAppColors.accentLight, width: 1.5), // Focus with light Teal
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: BorderSide(color: ModernAppColors.error, width: 1),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: BorderSide(color: ModernAppColors.error, width: 1.5),
        ),
        hintStyle: TextStyle(color: ModernAppColors.textHint.withOpacity(0.7)), // Use a lighter hint from palette
        labelStyle: TextStyle(color: ModernAppColors.secondaryLight), // Lighter gray for labels
        floatingLabelStyle: TextStyle(color: ModernAppColors.accentLight),
        errorStyle: TextStyles.errorText.copyWith(color: ModernAppColors.error),
        ),
    
      // Card Theme
      cardTheme: CardTheme(
        color: darkSurface, // Use dark surface for cards
        elevation: 1, // Subtle elevation
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(6),
        ),
        margin: const EdgeInsets.all(8),
      ),
      
      // Dialog Theme
      dialogTheme: DialogTheme(
        backgroundColor: darkSurface,
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        titleTextStyle: TextStyles.titleMedium.copyWith(color: lightTextOnDark),
        contentTextStyle: TextStyles.bodyMedium.copyWith(color: lightTextOnDark),
      ),
      
      // Checkbox Theme
      checkboxTheme: CheckboxThemeData(
        fillColor: MaterialStateProperty.resolveWith<Color>((states) {
          if (states.contains(MaterialState.selected)) {
            return ModernAppColors.accent; // Teal for selected
          }
          return Colors.transparent;
        }),
        checkColor: MaterialStateProperty.all(ModernAppColors.onAccent), // Black check on Teal
        side: BorderSide(color: ModernAppColors.secondaryLight), // Border for unchecked state
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(4),
        ),
      ),
      
      // Switch Theme
      switchTheme: SwitchThemeData(
        thumbColor: MaterialStateProperty.resolveWith<Color>((states) {
          if (states.contains(MaterialState.selected)) {
            return ModernAppColors.accent; // Teal thumb
          }
          return ModernAppColors.secondaryLight; // Lighter gray for disabled thumb
        }),
        trackColor: MaterialStateProperty.resolveWith<Color>((states) {
          if (states.contains(MaterialState.selected)) {
            return ModernAppColors.accent.withOpacity(0.5);
          }
          return ModernAppColors.secondaryDark.withOpacity(0.5); // Darker gray for disabled track
        }),
      ),
      
      // Divider Theme
      dividerTheme: DividerThemeData(
        color: ModernAppColors.secondaryDark.withOpacity(0.7),
        thickness: 1,
        space: 16,
      ),
      
      // Tab Bar Theme
      tabBarTheme: TabBarTheme(
        labelColor: ModernAppColors.accentLight, // Light Teal for selected tab
        unselectedLabelColor: ModernAppColors.secondaryLight, // Lighter Gray for unselected
        indicatorColor: ModernAppColors.accentLight,
        labelStyle: TextStyles.labelMedium.copyWith(fontWeight: FontWeight.bold),
        unselectedLabelStyle: TextStyles.labelMedium,
      ),
    );
  }
}