import 'package:flutter/material.dart';

/// App color constants used throughout the application
/// Based on Argon Dashboard Chakra UI color palette
class AppColors {
  // Primary colors - Argon Dashboard blue
  static const Color primary = Color(0xFF5E72E4);
  static const Color primaryLight = Color(0xFF8A98F1);
  static const Color primaryDark = Color(0xFF324CDD);

  // Secondary colors - Light gray
  static const Color secondary = Color(0xFFF4F5F7);
  static const Color secondaryLight = Color(0xFFFAFBFC);
  static const Color secondaryDark = Color(0xFFDCE0EE);

  // Accent colors - Warning orange
  static const Color accent = Color(0xFFFB6340);
  static const Color accentLight = Color(0xFFFC8464);
  static const Color accentDark = Color(0xFFEA4C2D);

  // Status colors
  static const Color success = Color(0xFF2DCE89);
  static const Color warning = Color(0xFFFB6340);
  static const Color error = Color(0xFFF5365C);
  static const Color info = Color(0xFF11CDEF);

  // Neutral colors
  static const Color background = Color(0xFFF6F9FC);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color cardColor = Color(0xFFFFFFFF);

  // Text colors
  static const Color textPrimary = Color(0xFF172B4D);
  static const Color textSecondary = Color(0xFF525F7F);
  static const Color textHint = Color(0xFF8898AA);

  // Border and divider colors
  static const Color borderColor = Color(0xFFE9ECEF);
  static const Color dividerColor = Color(0xFFDEE2E6);

  // Icon colors
  static const Color iconColor = Color(0xFF525F7F);
  static const Color iconColorLight = Color(0xFF8898AA);

  // Disabled colors
  static const Color disabledColor = Color(0xFFADB5BD);
  static const Color disabledTextColor = Color(0xFF8898AA);
}

/// Modern App color constants - Original Dark Blue Theme
class ModernAppColors {
  // Primary colors - Original Dark Blue
  static const Color primary = Color(0xFF2563EB); // Original dark blue
  static const Color primaryLight = Color(0xFF60A5FA); // Light blue
  static const Color primaryDark = Color(0xFF1E40AF); // Darker blue

  // Accent colors - Complementary Blue
  static const Color accent = Color(0xFF3B82F6); // Blue accent
  static const Color accentLight = Color(0xFF93C5FD); // Light blue accent
  static const Color accentDark = Color(0xFF1D4ED8); // Dark blue accent

  // Secondary colors - Soft Gray
  static const Color secondary = Color(0xFFE0E0E0); // Soft Gray
  static const Color secondaryLight = Color(0xFFF5F5F5);
  static const Color secondaryDark = Color(0xFFBDBDBD);

  // Neutral colors
  static const Color background = Color(0xFF0F172A); // Very dark blue
  static const Color surface = Color(0xFF1E293B); // Dark blue surface
  static const Color cardColor = Color(0xFF1E293B); // Dark blue surface
  static const Color onPrimary = Color(
    0xFFFFFFFF,
  ); // Text/icon color on primary background
  static const Color onSecondary = Color(
    0xFF000000,
  ); // Text/icon color on secondary background
  static const Color onAccent = Color(
    0xFF000000,
  ); // Text/icon color on accent background
  static const Color onBackground = Color(
    0xFF212121,
  ); // Text/icon color on background
  static const Color onSurface = Color(
    0xFF212121,
  ); // Text/icon color on surface

  // Text colors - Original Dark Blue Theme
  static const Color textPrimary = Color(
    0xFFF1F5F9,
  ); // Light text for dark theme
  static const Color textSecondary = Color(0xFFE2E8F0); // Medium light text
  static const Color textHint = Color(0xFFCBD5E1); // Light gray text

  // Status colors - Original Dark Blue Theme
  static const Color success = Color(0xFF10B981); // Green
  static const Color warning = Color(0xFFF59E0B); // Amber
  static const Color error = Color(0xFFEF4444); // Red
  static const Color info = Color(0xFF2563EB); // Blue

  // Border and divider colors
  static const Color borderColor = Color(
    0xFF475569,
  ); // Blue-gray borders for dark theme
  static const Color dividerColor = Color(
    0xFF334155,
  ); // Darker blue-gray dividers for dark theme

  // Icon colors
  static const Color iconColor = Color(
    0xFF757575,
  ); // Medium Gray (same as textSecondary)
  static const Color iconColorLight = Color(
    0xFFBDBDBD,
  ); // Light Gray (same as textHint)

  // Disabled colors
  static const Color disabledColor = Color(0xFFBDBDBD); // Light Gray
  static const Color disabledTextColor = Color(0xFF9E9E9E); // Lighter Gray
}
