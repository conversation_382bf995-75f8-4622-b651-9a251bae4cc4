import 'package:flutter/material.dart';

/// App color constants used throughout the application
/// Based on Argon Dashboard Chakra UI color palette
class AppColors {
  // Primary colors - Argon Dashboard blue
  static const Color primary = Color(0xFF5E72E4);
  static const Color primaryLight = Color(0xFF8A98F1);
  static const Color primaryDark = Color(0xFF324CDD);
  
  // Secondary colors - Light gray
  static const Color secondary = Color(0xFFF4F5F7);
  static const Color secondaryLight = Color(0xFFFAFBFC);
  static const Color secondaryDark = Color(0xFFDCE0EE);
  
  // Accent colors - Warning orange
  static const Color accent = Color(0xFFFB6340);
  static const Color accentLight = Color(0xFFFC8464);
  static const Color accentDark = Color(0xFFEA4C2D);
  
  // Status colors
  static const Color success = Color(0xFF2DCE89);
  static const Color warning = Color(0xFFFB6340);
  static const Color error = Color(0xFFF5365C);
  static const Color info = Color(0xFF11CDEF);
  
  // Neutral colors
  static const Color background = Color(0xFFF6F9FC);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color cardColor = Color(0xFFFFFFFF);
  
  // Text colors
  static const Color textPrimary = Color(0xFF172B4D);
  static const Color textSecondary = Color(0xFF525F7F);
  static const Color textHint = Color(0xFF8898AA);
  
  // Border and divider colors
  static const Color borderColor = Color(0xFFE9ECEF);
  static const Color dividerColor = Color(0xFFDEE2E6);
  
  // Icon colors
  static const Color iconColor = Color(0xFF525F7F);
  static const Color iconColorLight = Color(0xFF8898AA);
  
  // Disabled colors
  static const Color disabledColor = Color(0xFFADB5BD);
  static const Color disabledTextColor = Color(0xFF8898AA);
}

/// Modern App color constants
class ModernAppColors {
  // Primary colors - Deep Blue
  static const Color primary = Color(0xFF0A2540); // Deep Blue
  static const Color primaryLight = Color(0xFF3E5A78);
  static const Color primaryDark = Color(0xFF00122A);

  // Accent colors - Vibrant Teal
  static const Color accent = Color(0xFF00C49A); // Vibrant Teal
  static const Color accentLight = Color(0xFF50F8CB);
  static const Color accentDark = Color(0xFF00926C);

  // Secondary colors - Soft Gray
  static const Color secondary = Color(0xFFE0E0E0); // Soft Gray
  static const Color secondaryLight = Color(0xFFF5F5F5);
  static const Color secondaryDark = Color(0xFFBDBDBD);

  // Neutral colors
  static const Color background = Color(0xFFF4F6F8); // Light Gray
  static const Color surface = Color(0xFFFFFFFF); // White
  static const Color cardColor = Color(0xFFFFFFFF); // White
  static const Color onPrimary = Color(0xFFFFFFFF); // Text/icon color on primary background
  static const Color onSecondary = Color(0xFF000000); // Text/icon color on secondary background
  static const Color onAccent = Color(0xFF000000); // Text/icon color on accent background
  static const Color onBackground = Color(0xFF212121); // Text/icon color on background
  static const Color onSurface = Color(0xFF212121); // Text/icon color on surface

  // Text colors
  static const Color textPrimary = Color(0xFF212121); // Dark Gray / Off-Black
  static const Color textSecondary = Color(0xFF757575); // Medium Gray
  static const Color textHint = Color(0xFFBDBDBD); // Light Gray

  // Status colors
  static const Color success = Color(0xFF4CAF50); // Green
  static const Color warning = Color(0xFFFFC107); // Amber
  static const Color error = Color(0xFFF44336); // Red
  static const Color info = Color(0xFF2196F3); // Blue

  // Border and divider colors
  static const Color borderColor = Color(0xFFE0E0E0); // Soft Gray (same as secondary)
  static const Color dividerColor = Color(0xFFBDBDBD); // Darker Gray (same as secondaryDark)

  // Icon colors
  static const Color iconColor = Color(0xFF757575); // Medium Gray (same as textSecondary)
  static const Color iconColorLight = Color(0xFFBDBDBD); // Light Gray (same as textHint)

  // Disabled colors
  static const Color disabledColor = Color(0xFFBDBDBD); // Light Gray
  static const Color disabledTextColor = Color(0xFF9E9E9E); // Lighter Gray
}
