class ErrorConstants {
  // Network Errors
  static const String networkError = 'Network error. Please check your internet connection.';
  static const String serverError = 'Server error. Please try again later.';
  static const String timeoutError = 'Request timed out. Please try again.';
  static const String unexpectedError = 'An unexpected error occurred. Please try again.';
  
  // Authentication Errors
  static const String invalidCredentials = 'Invalid username or password.';
  static const String sessionExpired = 'Your session has expired. Please log in again.';
  static const String unauthorizedAccess = 'You do not have permission to access this resource.';
  static const String accountLocked = 'Your account has been locked. Please contact an administrator.';
  
  // Form Validation Errors
  static const String requiredField = 'This field is required.';
  static const String invalidEmail = 'Please enter a valid email address.';
  static const String invalidPhone = 'Please enter a valid phone number.';
  static const String invalidDate = 'Please enter a valid date.';
  static const String passwordMismatch = 'Passwords do not match.';
  static const String weakPassword = 'Password is too weak. It should be at least 8 characters with letters and numbers.';
  static const String invalidAmount = 'Please enter a valid amount.';
  
  // Data Errors
  static const String dataNotFound = 'The requested data was not found.';
  static const String duplicateEntry = 'This entry already exists.';
  static const String dataCorrupted = 'The data appears to be corrupted.';
  
  // Operation Errors
  static const String operationFailed = 'The operation failed. Please try again.';
  static const String insufficientPermissions = 'You do not have sufficient permissions for this action.';
  static const String resourceBusy = 'This resource is currently in use. Please try again later.';
}