import 'package:flutter/material.dart';

class LayoutConstants {
  // Padding
  static const double paddingXS = 4.0;
  static const double paddingS = 8.0;
  static const double paddingM = 16.0;
  static const double paddingL = 24.0;
  static const double paddingXL = 32.0;
  static const double paddingXXL = 48.0;

  // Margin
  static const double marginXS = 4.0;
  static const double marginS = 8.0;
  static const double marginM = 16.0;
  static const double marginL = 24.0;
  static const double marginXL = 32.0;
  static const double marginXXL = 48.0;

  // Border Radius
  static const double borderRadiusXS = 4.0;
  static const double borderRadiusS = 8.0;
  static const double borderRadiusM = 12.0;
  static const double borderRadiusL = 16.0;
  static const double borderRadiusXL = 24.0;
  static const double borderRadiusCircular = 999.0;

  // Border Width
  static const double borderWidthThin = 0.5;
  static const double borderWidthRegular = 1.0;
  static const double borderWidthThick = 2.0;

  // Icon Sizes
  static const double iconSizeXS = 16.0;
  static const double iconSizeS = 20.0;
  static const double iconSizeM = 24.0;
  static const double iconSizeL = 32.0;
  static const double iconSizeXL = 48.0;

  // Button Sizes
  static const double buttonHeightS = 32.0;
  static const double buttonHeightM = 40.0;
  static const double buttonHeightL = 48.0;

  // Input Field Heights
  static const double inputFieldHeight = 48.0;
  static const double inputFieldHeightSmall = 40.0;

  // Card Elevation
  static const double cardElevation = 2.0;
  static const double dialogElevation = 8.0;
  static const double appBarElevation = 4.0;

  // Responsive Breakpoints - Updated for web
  static const double breakpointMobile = 768.0;  // Increased from 600
  static const double breakpointTablet = 1024.0; // Increased from 960
  static const double breakpointDesktop = 1440.0; // Increased from 1280
  
  // Web-specific constants
  static const double maxContentWidth = 1600.0;
  static const double sidebarWidth = 280.0;
  static const double contentPadding = 24.0;

  // Common Widgets
  static const double appBarHeight = 56.0;
  static const double tabBarHeight = 48.0;
  static const double bottomNavBarHeight = 56.0;
  static const double fabSize = 56.0;
  static const double drawerWidth = 280.0;
  static const double modalBottomSheetHeight = 300.0;

  // Spacing
  static const SizedBox spacingXS = SizedBox(height: 4.0, width: 4.0);
  static const SizedBox spacingS = SizedBox(height: 8.0, width: 8.0);
  static const SizedBox spacingM = SizedBox(height: 16.0, width: 16.0);
  static const SizedBox spacingL = SizedBox(height: 24.0, width: 24.0);
  static const SizedBox spacingXL = SizedBox(height: 32.0, width: 32.0);
  static const SizedBox spacingXXL = SizedBox(height: 48.0, width: 48.0);
}