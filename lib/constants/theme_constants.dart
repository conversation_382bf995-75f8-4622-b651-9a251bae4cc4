import 'package:flutter/material.dart';

class ThemeConstants {
  // Primary Colors
  static const Color primaryColor = Color(0xFF1E88E5);
  static const Color primaryDarkColor = Color(0xFF1565C0);
  static const Color primaryLightColor = Color(0xFF42A5F5);
  static const Color accentColor = Color(0xFFFF4081);

  // Background Colors
  static const Color backgroundColor = Color(0xFF121212);
  static const Color surfaceColor = Color(0xFF1E1E1E);
  static const Color cardColor = Color(0xFF2C2C2C);
  static const Color dialogColor = Color(0xFF323232);
  static const Color appBarColor = Color(0xFF1A1A1A);
  static const Color drawerColor = Color(0xFF212121);

  // Text Colors
  static const Color primaryTextColor = Color(0xFFFFFFFF);
  static const Color secondaryTextColor = Color(0xFFB3B3B3);
  static const Color disabledTextColor = Color(0xFF757575);
  static const Color hintTextColor = Color(0xFF9E9E9E);

  // Border Colors
  static const Color borderColor = Color(0xFF424242);
  static const Color dividerColor = Color(0xFF424242);

  // Status Colors
  static const Color successColor = Color(0xFF4CAF50);
  static const Color errorColor = Color(0xFFF44336);
  static const Color warningColor = Color(0xFFFFB74D);
  static const Color infoColor = Color(0xFF29B6F6);

  // Chart Colors
  static const List<Color> chartColors = [
    Color(0xFF1E88E5),
    Color(0xFFFF8A65),
    Color(0xFF4CAF50),
    Color(0xFFBA68C8),
    Color(0xFFFFB74D),
    Color(0xFF4DB6AC),
    Color(0xFFFF5252),
    Color(0xFF9575CD),
  ];

  // Shadows
  static const List<BoxShadow> cardShadow = [
    BoxShadow(
      color: Color(0x33000000),
      blurRadius: 8,
      offset: Offset(0, 2),
    ),
  ];

  // Gradients
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [Color(0xFF1E88E5), Color(0xFF1565C0)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  // Opacity
  static const double disabledOpacity = 0.38;
  static const double hoverOpacity = 0.08;
  static const double focusOpacity = 0.12;
  static const double pressedOpacity = 0.16;
}