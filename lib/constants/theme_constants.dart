import 'package:flutter/material.dart';

class ThemeConstants {
  // Original Dark Blue Primary Colors
  static const Color primaryColor = Color(0xFF2563EB);
  static const Color primaryDarkColor = Color(0xFF1E40AF);
  static const Color primaryLightColor = Color(0xFF60A5FA);
  static const Color accentColor = Color(0xFF3B82F6);

  // Original Dark Blue Background Colors
  static const Color backgroundColor = Color(0xFF0F172A);
  static const Color surfaceColor = Color(0xFF1E293B);
  static const Color cardColor = Color(0xFF1E293B);
  static const Color dialogColor = Color(0xFF334155);
  static const Color appBarColor = Color(0xFF1E293B);
  static const Color drawerColor = Color(0xFF1E293B);

  // Original Dark Blue Text Colors
  static const Color primaryTextColor = Color(0xFFF1F5F9);
  static const Color secondaryTextColor = Color(0xFFE2E8F0);
  static const Color disabledTextColor = Color(0xFFCBD5E1);
  static const Color hintTextColor = Color(0xFFCBD5E1);

  // Original Dark Blue Border Colors
  static const Color borderColor = Color(0xFF475569);
  static const Color dividerColor = Color(0xFF334155);

  // Original Dark Blue Status Colors
  static const Color successColor = Color(0xFF10B981);
  static const Color errorColor = Color(0xFFEF4444);
  static const Color warningColor = Color(0xFFF59E0B);
  static const Color infoColor = Color(0xFF2563EB);

  // Original Dark Blue Chart Colors
  static const List<Color> chartColors = [
    Color(0xFF2563EB), // Primary dark blue
    Color(0xFF3B82F6), // Blue
    Color(0xFF60A5FA), // Light blue
    Color(0xFF10B981), // Green
    Color(0xFFF59E0B), // Amber
    Color(0xFFEF4444), // Red
    Color(0xFF8B5CF6), // Purple
    Color(0xFF06B6D4), // Cyan
  ];

  // Enhanced Shadows for Dark Theme
  static const List<BoxShadow> cardShadow = [
    BoxShadow(color: Color(0x40000000), blurRadius: 12, offset: Offset(0, 4)),
  ];

  // Original Dark Blue Gradients
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [Color(0xFF2563EB), Color(0xFF1E40AF)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient surfaceGradient = LinearGradient(
    colors: [Color(0xFF1E293B), Color(0xFF334155)],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );

  // Opacity
  static const double disabledOpacity = 0.38;
  static const double hoverOpacity = 0.08;
  static const double focusOpacity = 0.12;
  static const double pressedOpacity = 0.16;
}
