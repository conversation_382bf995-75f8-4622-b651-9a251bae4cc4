// ignore_for_file: unused_import

import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

class AppLocalizations {
  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  String get appTitle => intl.Intl.message(
        'Hemma ERP',
        name: 'appTitle',
        desc: 'The title of the application',
      );

  String get welcome => intl.Intl.message(
        'Welcome to Hemma ERP',
        name: 'welcome',
        desc: 'Welcome message on home screen',
      );

  String get generalLedger => intl.Intl.message(
        'General Ledger',
        name: 'generalLedger',
        desc: 'General Ledger screen title',
      );

  String get customers => intl.Intl.message(
        'Customers',
        name: 'customers',
        desc: 'Customers screen title',
      );

  String get sports => intl.Intl.message(
        'Sports',
        name: 'sports',
        desc: 'Sports screen title',
      );

  String get employees => intl.Intl.message(
        'Employees',
        name: 'employees',
        desc: 'Employees screen title',
      );

  String get trainers => intl.Intl.message(
        'Trainers',
        name: 'trainers',
        desc: 'Trainers screen title',
      );

  String get bookings => intl.Intl.message(
        'Bookings',
        name: 'bookings',
        desc: 'Bookings screen title',
      );

  String get stadiumBooking => intl.Intl.message(
        'Stadium Booking',
        name: 'stadiumBooking',
        desc: 'Stadium Booking screen title',
      );

  String get visitBooking => intl.Intl.message(
        'Visit Booking',
        name: 'visitBooking',
        desc: 'Visit Booking screen title',
      );

  String get manageBookings => intl.Intl.message(
        'Manage Bookings',
        name: 'manageBookings',
        desc: 'Manage Bookings screen title',
      );

  String get payroll => intl.Intl.message(
        'Payroll',
        name: 'payroll',
        desc: 'Payroll screen title',
      );

  String get sportsSchedule => intl.Intl.message(
        'Sports Schedule',
        name: 'sportsSchedule',
        desc: 'Sports Schedule screen title',
      );

  String get stadiumRegistration => intl.Intl.message(
        'Stadium Registration',
        name: 'stadiumRegistration',
        desc: 'Stadium Registration screen title',
      );

  String get trainerTrainee => intl.Intl.message(
        'Trainer-Trainee',
        name: 'trainerTrainee',
        desc: 'Trainer-Trainee screen title',
      );
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(AppLocalizations());
  }

  @override
  bool isSupported(Locale locale) {
    return ['en', 'ar'].contains(locale.languageCode);
  }

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}