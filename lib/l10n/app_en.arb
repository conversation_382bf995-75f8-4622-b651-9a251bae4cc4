{"@@locale": "en", "appTitle": "Hemma ERP", "@appTitle": {"description": "The title of the application"}, "welcome": "Welcome to Hemma ERP", "@welcome": {"description": "Welcome message on home screen"}, "generalLedger": "General <PERSON><PERSON>", "@generalLedger": {"description": "General Ledger screen title"}, "customers": "Customers", "@customers": {"description": "Customers screen title"}, "sports": "Sports", "@sports": {"description": "Sports screen title"}, "employees": "Employees", "@employees": {"description": "Employees screen title"}, "trainers": "Trainers", "@trainers": {"description": "Trainers screen title"}, "experience": "Experience", "@experience": {"description": "Experience label for trainers"}, "subscriptionType": "Subscription Type", "@subscriptionType": {"description": "Subscription type label"}, "subscriptionDuration": "Subscription Duration", "@subscriptionDuration": {"description": "Subscription duration label"}, "includeUniform": "Include Uniform", "@includeUniform": {"description": "Include uniform option label"}, "uniformPrice": "Uniform Price", "@uniformPrice": {"description": "Uniform price label"}, "bookings": "Bookings", "@bookings": {"description": "Bookings screen title"}, "stadiumBooking": "Stadium Booking", "@stadiumBooking": {"description": "Stadium Booking screen title"}, "visitBooking": "Visit Booking", "@visitBooking": {"description": "Visit Booking screen title"}, "manageBookings": "Manage Bookings", "@manageBookings": {"description": "Manage Bookings screen title"}, "payroll": "Payroll", "@payroll": {"description": "Payroll screen title"}, "sportsSchedule": "Sports Schedule", "@sportsSchedule": {"description": "Sports Schedule screen title"}, "stadiumRegistration": "Stadium Registration", "@stadiumRegistration": {"description": "Stadium Registration screen title"}, "customerRegistration": "Customer Registration", "@customerRegistration": {"description": "Customer Registration screen title"}, "trainerTrainee": "Trainer-Trainee", "@trainerTrainee": {"description": "Trainer-Trainee screen title"}, "allCardsAndCharts": "All Cards and Charts", "@allCardsAndCharts": {"description": "Title for the All Cards and Charts section"}, "deleteEmployeeConfirmation": "Are you sure you want to delete this employee?", "@deleteEmployeeConfirmation": {"description": "Confirmation message for deleting an employee"}, "required": "Required", "@required": {"description": "Generic required field validation message"}, "mobileNumberRequired": "Mobile number is required", "@mobileNumberRequired": {"description": "Mobile number required validation message"}, "emailRequired": "Email is required", "@emailRequired": {"description": "Email required validation message"}, "search": "Search", "@search": {"description": "Search placeholder text"}, "back": "Back", "@back": {"description": "Back button text"}, "next": "Next", "@next": {"description": "Next button text"}, "submit": "Submit", "@submit": {"description": "Submit button text"}, "save": "Save", "@save": {"description": "Save button text"}, "delete": "Delete", "@delete": {"description": "Delete button text"}, "edit": "Edit", "@edit": {"description": "Edit button text"}, "add": "Add", "@add": {"description": "Add button text"}, "pending": "Pending", "@pending": {}, "confirmed": "Confirmed", "@confirmed": {}, "cancelled": "Cancelled", "@cancelled": {}, "unknown": "Unknown", "@unknown": {}, "confirm": "Confirm", "@confirm": {}, "cancel": "Cancel", "@cancel": {}, "noBookingsFound": "No bookings found", "@noBookingsFound": {}, "trainedWith": "with", "@trainedWith": {"description": "Text used to connect sport name with trainer"}, "email": "Email", "@email": {}, "phone": "Phone", "@phone": {}, "day": "Day", "@day": {}, "time": "Time", "@time": {}, "created": "Created", "@created": {}, "updated": "Updated", "@updated": {}, "trainedBy": "trained by", "@trainedBy": {"description": "Text used to connect trainer name with sport"}, "customerList": "Customer List", "@customerList": {"description": "Title for the customer list screen"}, "renewSubscription": "Renew Subscription", "@renewSubscription": {"description": "Title for subscription renewal dialog"}, "selectCustomerToRenew": "Select a customer to renew subscription:", "@selectCustomerToRenew": {"description": "Prompt to select customer for subscription renewal"}, "refundSubscription": "Refund Subscription", "@refundSubscription": {"description": "Title for subscription refund dialog"}, "customer": "Customer", "@customer": {"description": "Label for customer name"}, "refundAmount": "Refund Amount", "@refundAmount": {"description": "Label for refund amount input"}, "refundReason": "Reason for Refund", "taxStatus": "Tax Status", "@taxStatus": {"description": "Label for tax status"}, "taxesIncluded": "Taxes Included", "@taxesIncluded": {"description": "Taxes included status"}, "taxesExcluded": "Taxes Excluded", "@taxesExcluded": {"description": "Taxes excluded status"}, "noScreensToDisplayStatusFor": "No screens to display status for", "@noScreensToDisplayStatusFor": {"description": "Message shown when no screens are available to display API status"}, "loadingSampleData": "Loading sample data...", "@loadingSampleData": {"description": "Message displayed when loading sample data"}, "noDataToExport": "No data available to export.", "@noDataToExport": {}, "stadiumNameColumn": "Stadium Name", "@stadiumNameColumn": {}, "exportedSuccessfullyTo": "Exported successfully to {filePath}", "@exportedSuccessfullyTo": {"placeholders": {"filePath": {}}}, "exportCancelled": "Export cancelled.", "@exportCancelled": {}, "errorExportingCsv": "Error exporting CSV: {error}", "@errorExportingCsv": {"placeholders": {"error": {}}}, "exportToCsv": "Export to CSV", "@exportToCsv": {}, "searchBookings": "Search Bookings", "@searchBookings": {}, "searchBookingsHint": "Search by name, email, phone...", "@searchBookingsHint": {}, "manageBookingsTitle": "Manage Bookings", "@manageBookingsTitle": {}, "actionsColumn": "Actions", "@actionsColumn": {}, "noActionsAvailable": "No actions available.", "@noActionsAvailable": {}, "businessHoursHelp": "Business Hours Help", "@businessHoursHelp": {"description": "Tooltip for business hours help button"}, "businessHoursHelpText": "To set business hours:\n\n1. Toggle a day to \"Open\" to add time slots\n2. Add time slots by clicking the \"+\" button\n3. Select start and end times for each slot\n4. Assign a trainer to each time slot\n5. You can add multiple time slots per day\n6. To remove a time slot, click the trash icon", "@businessHoursHelpText": {"description": "Detailed help text for business hours"}, "enterSportName": "Enter sport name", "@enterSportName": {"description": "Hint text for sport name input"}, "enterFees": "Enter fees amount", "@enterFees": {"description": "Hint text for fees input"}, "enterNumberOfSessions": "Enter number of sessions", "@enterNumberOfSessions": {"description": "Hint text for number of sessions input"}, "updateSport": "Update Sport", "@updateSport": {"description": "Button text for updating a sport"}, "createSport": "Create Sport", "@createSport": {"description": "Button text for creating a sport"}, "@refundReason": {"description": "Label for refund reason input"}, "suspendAccount": "Suspend Account", "@suspendAccount": {"description": "Label for account suspension option"}, "updateSubscription": "Update Subscription", "@updateSubscription": {"description": "Update subscription button text"}, "duration": "Duration", "@duration": {"description": "Duration label for subscription"}, "enterDuration": "Enter duration", "@enterDuration": {"description": "Duration input placeholder"}, "amount": "Amount", "@amount": {"description": "Amount label for subscription"}, "enterAmount": "Enter amount", "@enterAmount": {"description": "Amount input placeholder"}, "subscriptionUpdated": "Subscription updated successfully", "@subscriptionUpdated": {"description": "Subscription update success message"}, "updateFailed": "Failed to update subscription", "@updateFailed": {"description": "Subscription update failure message"}, "afghan": "Afghan", "@afghan": {"description": "Afghan nationality"}, "albanian": "Albanian", "@albanian": {"description": "Albanian nationality"}, "algerian": "Algerian", "@algerian": {"description": "Algerian nationality"}, "american": "American", "@american": {"description": "American nationality"}, "andorran": "Andorran", "@andorran": {"description": "Andorran nationality"}, "angolan": "Angolan", "@angolan": {"description": "Angolan nationality"}, "argentine": "Argentine", "@argentine": {"description": "Argentine nationality"}, "armenian": "Armenian", "@armenian": {"description": "Armenian nationality"}, "australian": "Australian", "@australian": {"description": "Australian nationality"}, "austrian": "Austrian", "@austrian": {"description": "Austrian nationality"}, "azerbaijani": "Azerbaijani", "@azerbaijani": {"description": "Azerbaijani nationality"}, "bahamian": "<PERSON><PERSON><PERSON>", "@bahamian": {"description": "Bahamian nationality"}, "bahraini": "Bahraini", "@bahraini": {"description": "Bahraini nationality"}, "bangladeshi": "Bangladeshi", "@bangladeshi": {"description": "Bangladeshi nationality"}, "barbadian": "Barbadian", "@barbadian": {"description": "Barbadian nationality"}, "belarusian": "Belarusian", "@belarusian": {"description": "Belarusian nationality"}, "belgian": "Belgian", "@belgian": {"description": "Belgian nationality"}, "belizean": "Belizean", "@belizean": {"description": "Belizean nationality"}, "beninese": "Beninese", "@beninese": {"description": "Beninese nationality"}, "bhutanese": "Bhutanese", "@bhutanese": {"description": "Bhutanese nationality"}, "bolivian": "Bolivian", "@bolivian": {"description": "Bolivian nationality"}, "bosnian": "Bosnian", "@bosnian": {"description": "Bosnian nationality"}, "brazilian": "Brazilian", "@brazilian": {"description": "Brazilian nationality"}, "british": "British", "@british": {"description": "British nationality"}, "bruneian": "Bruneian", "@bruneian": {"description": "Bruneian nationality"}, "bulgarian": "Bulgarian", "@bulgarian": {"description": "Bulgarian nationality"}, "burkinabe": "Burkinabe", "@burkinabe": {"description": "Burkinabe nationality"}, "burmese": "Burmese", "@burmese": {"description": "Burmese nationality"}, "burundian": "Burundian", "@burundian": {"description": "Burundian nationality"}, "cambodian": "Cambodian", "@cambodian": {"description": "Cambodian nationality"}, "cameroonian": "Cameroonian", "@cameroonian": {"description": "Cameroonian nationality"}, "canadian": "Canadian", "@canadian": {"description": "Canadian nationality"}, "chadian": "Chadian", "@chadian": {"description": "Chadian nationality"}, "chilean": "Chilean", "@chilean": {"description": "Chilean nationality"}, "chinese": "Chinese", "@chinese": {"description": "Chinese nationality"}, "colombian": "Colombian", "@colombian": {"description": "Colombian nationality"}, "comoran": "Comoran", "@comoran": {"description": "Comoran nationality"}, "congolese": "Congolese", "@congolese": {"description": "Congolese nationality"}, "costaRican": "Costa Rican", "@costaRican": {"description": "Costa Rican nationality"}, "croatian": "Croatian", "@croatian": {"description": "Croatian nationality"}, "cuban": "Cuban", "@cuban": {"description": "Cuban nationality"}, "cypriot": "Cypriot", "@cypriot": {"description": "Cypriot nationality"}, "czech": "Czech", "@czech": {"description": "Czech nationality"}, "danish": "Danish", "@danish": {"description": "Danish nationality"}, "dominican": "Dominican", "@dominican": {"description": "Dominican nationality"}, "dutch": "Dutch", "@dutch": {"description": "Dutch nationality"}, "eastTimorese": "East Timorese", "@eastTimorese": {"description": "East Timorese nationality"}, "egyptian": "Egyptian", "@egyptian": {"description": "Egyptian nationality"}, "emirian": "<PERSON><PERSON><PERSON>", "@emirian": {"description": "Emirian nationality"}, "equatorialGuinean": "Equatorial Guinean", "@equatorialGuinean": {"description": "Equatorial Guinean nationality"}, "eritrean": "Eritrean", "@eritrean": {"description": "Eritrean nationality"}, "estonian": "Estonian", "@estonian": {"description": "Estonian nationality"}, "ethiopian": "Ethiopian", "@ethiopian": {"description": "Ethiopian nationality"}, "fijian": "Fijian", "@fijian": {"description": "Fijian nationality"}, "filipino": "Filipino", "@filipino": {"description": "Filipino nationality"}, "finnish": "Finnish", "@finnish": {"description": "Finnish nationality"}, "french": "French", "@french": {"description": "French nationality"}, "gabonese": "Gabonese", "@gabonese": {"description": "Gabonese nationality"}, "gambian": "Gambian", "@gambian": {"description": "Gambian nationality"}, "georgian": "Georgian", "@georgian": {"description": "Georgian nationality"}, "german": "German", "@german": {"description": "German nationality"}, "ghanaian": "Ghanaian", "@ghanaian": {"description": "Ghanaian nationality"}, "greek": "Greek", "@greek": {"description": "Greek nationality"}, "grenadian": "Grenadian", "@grenadian": {"description": "Grenadian nationality"}, "guatemalan": "Guatemalan", "@guatemalan": {"description": "Guatemalan nationality"}, "guinean": "Guinean", "@guinean": {"description": "Guinean nationality"}, "guyanese": "Guyanese", "@guyanese": {"description": "Guyanese nationality"}, "haitian": "Haitian", "@haitian": {"description": "Haitian nationality"}, "honduran": "<PERSON><PERSON><PERSON>", "@honduran": {"description": "Honduran nationality"}, "hungarian": "Hungarian", "@hungarian": {"description": "Hungarian nationality"}, "indian": "Indian", "@indian": {"description": "Indian nationality"}, "indonesian": "Indonesian", "@indonesian": {"description": "Indonesian nationality"}, "iranian": "Iranian", "@iranian": {"description": "Iranian nationality"}, "iraqi": "Iraqi", "@iraqi": {"description": "Iraqi nationality"}, "irish": "Irish", "@irish": {"description": "Irish nationality"}, "italian": "Italian", "@italian": {"description": "Italian nationality"}, "ivorian": "Ivorian", "@ivorian": {"description": "Ivorian nationality"}, "jamaican": "Jamaican", "@jamaican": {"description": "Jamaican nationality"}, "japanese": "Japanese", "@japanese": {"description": "Japanese nationality"}, "jordanian": "<PERSON><PERSON>", "@jordanian": {"description": "Jordanian nationality"}, "kazakhstani": "Kazakhstani", "@kazakhstani": {"description": "Kazakhstani nationality"}, "kenyan": "Kenyan", "@kenyan": {"description": "Kenyan nationality"}, "kuwaiti": "Kuwaiti", "@kuwaiti": {"description": "Kuwaiti nationality"}, "kyrgyz": "Kyrgyz", "@kyrgyz": {"description": "Kyrgyz nationality"}, "laotian": "Laotian", "@laotian": {"description": "Laotian nationality"}, "latvian": "Latvian", "@latvian": {"description": "Latvian nationality"}, "lebanese": "Lebanese", "@lebanese": {"description": "Lebanese nationality"}, "liberian": "Liberian", "@liberian": {"description": "Liberian nationality"}, "libyan": "Libyan", "@libyan": {"description": "Libyan nationality"}, "liechtensteiner": "Liechtensteiner", "@liechtensteiner": {"description": "Liechtensteiner nationality"}, "lithuanian": "Lithuanian", "@lithuanian": {"description": "Lithuanian nationality"}, "macedonian": "Macedonian", "@macedonian": {"description": "Macedonian nationality"}, "malagasy": "Malagasy", "@malagasy": {"description": "Malagasy nationality"}, "malawian": "Malawian", "@malawian": {"description": "Malawian nationality"}, "malaysian": "Malaysian", "@malaysian": {"description": "Malaysian nationality"}, "maldivian": "Maldivian", "@maldivian": {"description": "Maldivian nationality"}, "malian": "<PERSON><PERSON>", "@malian": {"description": "Malian nationality"}, "maltese": "Maltese", "@maltese": {"description": "Maltese nationality"}, "mauritanian": "Mauritanian", "@mauritanian": {"description": "Mauritanian nationality"}, "mauritian": "<PERSON><PERSON><PERSON>", "@mauritian": {"description": "Mauritian nationality"}, "mexican": "Mexican", "@mexican": {"description": "Mexican nationality"}, "moldovan": "Moldovan", "@moldovan": {"description": "Moldovan nationality"}, "monacan": "Monacan", "@monacan": {"description": "Monacan nationality"}, "mongolian": "Mongolian", "@mongolian": {"description": "Mongolian nationality"}, "montenegrin": "Montenegrin", "@montenegrin": {"description": "Montenegrin nationality"}, "moroccan": "Moroccan", "@moroccan": {"description": "Moroccan nationality"}, "mozambican": "Mozambican", "@mozambican": {"description": "Mozambican nationality"}, "namibian": "Namibian", "@namibian": {"description": "Namibian nationality"}, "nepalese": "Nepalese", "@nepalese": {"description": "Nepalese nationality"}, "newZealander": "New Zealander", "@newZealander": {"description": "New Zealander nationality"}, "nicaraguan": "Nicaraguan", "@nicaraguan": {"description": "Nicaraguan nationality"}, "nigerian": "Nigerian", "@nigerian": {"description": "Nigerian nationality"}, "nigerien": "Nigerien", "@nigerien": {"description": "Nigerien nationality"}, "northKorean": "North Korean", "@northKorean": {"description": "North Korean nationality"}, "norwegian": "Norwegian", "@norwegian": {"description": "Norwegian nationality"}, "omani": "Omani", "@omani": {"description": "Omani nationality"}, "pakistani": "Pakistani", "@pakistani": {"description": "Pakistani nationality"}, "palauan": "<PERSON><PERSON><PERSON>", "@palauan": {"description": "Palauan nationality"}, "panamanian": "Panamanian", "@panamanian": {"description": "Panamanian nationality"}, "papuaNewGuinean": "Papua New Guinean", "@papuaNewGuinean": {"description": "Papua New Guinean nationality"}, "paraguayan": "Paraguayan", "@paraguayan": {"description": "Paraguayan nationality"}, "peruvian": "Peruvian", "@peruvian": {"description": "Peruvian nationality"}, "polish": "Polish", "@polish": {"description": "Polish nationality"}, "portuguese": "Portuguese", "@portuguese": {"description": "Portuguese nationality"}, "qatari": "Qatari", "@qatari": {"description": "Qatari nationality"}, "romanian": "Romanian", "@romanian": {"description": "Romanian nationality"}, "russian": "Russian", "@russian": {"description": "Russian nationality"}, "rwandan": "Rwandan", "@rwandan": {"description": "Rwandan nationality"}, "saintLucian": "Saint Lucian", "@saintLucian": {"description": "Saint Lucian nationality"}, "salvadoran": "Salvadoran", "@salvadoran": {"description": "Salvadoran nationality"}, "samoan": "Samoan", "@samoan": {"description": "Samoan nationality"}, "sanMarinese": "San Marinese", "@sanMarinese": {"description": "San Marinese nationality"}, "saoTomean": "Sao Tomean", "@saoTomean": {"description": "Sao Tomean nationality"}, "saudi": "Saudi", "@saudi": {"description": "Saudi nationality"}, "senegalese": "Senegalese", "@senegalese": {"description": "Senegalese nationality"}, "serbian": "Serbian", "@serbian": {"description": "Serbian nationality"}, "seychellois": "<PERSON><PERSON><PERSON><PERSON>", "@seychellois": {"description": "Seychellois nationality"}, "sierraLeonean": "Sierra Leonean", "@sierraLeonean": {"description": "Sierra Leonean nationality"}, "singaporean": "Singaporean", "@singaporean": {"description": "Singaporean nationality"}, "slovenian": "Slovenian", "@slovenian": {"description": "Slovenian nationality"}, "somali": "Somali", "@somali": {"description": "Somali nationality"}, "southAfrican": "South African", "@southAfrican": {"description": "South African nationality"}, "southKorean": "South Korean", "@southKorean": {"description": "South Korean nationality"}, "spanish": "Spanish", "@spanish": {"description": "Spanish nationality"}, "sriLankan": "Sri Lankan", "@sriLankan": {"description": "Sri Lankan nationality"}, "sudanese": "Sudanese", "@sudanese": {"description": "Sudanese nationality"}, "swazi": "Swazi", "@swazi": {"description": "Swazi nationality"}, "swedish": "Swedish", "@swedish": {"description": "Swedish nationality"}, "swiss": "Swiss", "@swiss": {"description": "Swiss nationality"}, "syrian": "Syrian", "@syrian": {"description": "Syrian nationality"}, "taiwanese": "Taiwanese", "@taiwanese": {"description": "Taiwanese nationality"}, "tajik": "Tajik", "@tajik": {"description": "Tajik nationality"}, "tanzanian": "Tanzanian", "@tanzanian": {"description": "Tanzanian nationality"}, "thai": "Thai", "@thai": {"description": "Thai nationality"}, "togolese": "Togolese", "@togolese": {"description": "Togolese nationality"}, "tongan": "Tongan", "@tongan": {"description": "Tongan nationality"}, "tunisian": "Tunisian", "@tunisian": {"description": "Tunisian nationality"}, "turkish": "Turkish", "@turkish": {"description": "Turkish nationality"}, "tuvaluan": "Tuvaluan", "@tuvaluan": {"description": "Tuvaluan nationality"}, "ugandan": "Ugandan", "@ugandan": {"description": "Ugandan nationality"}, "ukrainian": "Ukrainian", "@ukrainian": {"description": "Ukrainian nationality"}, "uruguayan": "Uruguayan", "@uruguayan": {"description": "Uruguayan nationality"}, "venezuelan": "Venezuelan", "@venezuelan": {"description": "Venezuelan nationality"}, "vietnamese": "Vietnamese", "@vietnamese": {"description": "Vietnamese nationality"}, "yemenite": "Yemenite", "@yemenite": {"description": "Yemenite nationality"}, "zambian": "Zambian", "@zambian": {"description": "Zambian nationality"}, "zimbabwean": "Zimbabwean", "@zimbabwean": {"description": "Zimbabwean nationality"}, "antiguan": "Antiguan", "@antiguan": {"description": "Antiguan nationality"}, "botswanan": "Botswanan", "@botswanan": {"description": "Botswanan nationality"}, "capeVerdean": "Cape Verdean", "@capeVerdean": {"description": "Cape Verdean nationality"}, "centralAfrican": "Central African", "@centralAfrican": {"description": "Central African nationality"}, "djibouti": "Djibouti", "@djibouti": {"description": "Djibouti nationality"}, "ecuadorean": "Ecuadorean", "@ecuadorean": {"description": "Ecuadorean nationality"}, "guineaBissauan": "Guinea-Bissauan", "@guineaBissauan": {"description": "Guinea-Bissauan nationality"}, "herzegovinian": "Herzegovinian", "@herzegovinian": {"description": "Herzegovinian nationality"}, "icelander": "<PERSON><PERSON>", "@icelander": {"description": "Icelander nationality"}, "kiribati": "Kiribati", "@kiribati": {"description": "Kiribati nationality"}, "luxembourger": "<PERSON>er", "@luxembourger": {"description": "Luxembourger nationality"}, "marshallese": "<PERSON><PERSON>", "@marshallese": {"description": "Marshallese nationality"}, "micronesian": "Micronesian", "@micronesian": {"description": "Micronesian nationality"}, "mosotho": "Mosoth<PERSON>", "@mosotho": {"description": "Mosotho nationality"}, "motswana": "Motswana", "@motswana": {"description": "Motswana nationality"}, "nauruan": "Nauruan", "@nauruan": {"description": "Nauruan nationality"}, "northernIrish": "Northern Irish", "@northernIrish": {"description": "Northern Irish nationality"}, "scottish": "Scottish", "@scottish": {"description": "Scottish nationality"}, "slovakian": "Slovakian", "@slovakian": {"description": "Slovakian nationality"}, "solomonIslander": "Solomon Islander", "@solomonIslander": {"description": "Solomon Islander nationality"}, "surinamer": "Surinamer", "@surinamer": {"description": "Surinamer nationality"}, "trinidadianOrTobagonian": "Trinidadian or Tobagonian", "@trinidadianOrTobagonian": {"description": "Trinidadian or Tobagonian nationality"}, "uzbekistani": "Uzbekistani", "@uzbekistani": {"description": "Uzbekistani nationality"}, "welsh": "Welsh", "@welsh": {"description": "Welsh nationality"}, "suspendAccountWarning": "Warning: Suspending the account will prevent the customer from accessing services until reactivated.", "@suspendAccountWarning": {"description": "Warning message for account suspension"}, "employeeManagement": "Employee Management", "@employeeManagement": {"description": "Employee Management screen title"}, "personalInfo": "Personal Information", "@personalInfo": {"description": "Personal Information section title"}, "employmentInfo": "Employment Information", "@employmentInfo": {"description": "Employment Information section title"}, "educationInfo": "Education Information", "@educationInfo": {"description": "Education Information section title"}, "financialInfo": "Financial Information", "@financialInfo": {"description": "Financial Information section title"}, "contactInfo": "Contact Information", "@contactInfo": {"description": "Contact Information section title"}, "additionalInfo": "Additional Information", "@additionalInfo": {"description": "Additional Information section title"}, "employeeCode": "Employee Code", "@employeeCode": {"description": "Employee Code field label"}, "position": "Position", "@position": {"description": "Position field label"}, "jobTitle": "Job Title", "@jobTitle": {"description": "Job title field label"}, "department": "Department", "@department": {"description": "Department field label"}, "contractType": "Contract Type", "@contractType": {"description": "Contract Type field label"}, "startDate": "Start Date", "@startDate": {"description": "Start Date field label"}, "contractDate": "Contract Date", "@contractDate": {"description": "Contract Date field label"}, "probationEndDate": "Probation End Date", "@probationEndDate": {"description": "Probation End Date field label"}, "contractEndDate": "Contract End Date", "@contractEndDate": {"description": "Contract End Date field label"}, "university": "University", "@university": {"description": "University field label"}, "college": "College", "@college": {"description": "College field label"}, "major": "Major", "@major": {"description": "Major field label"}, "graduationYear": "Graduation Year", "@graduationYear": {"description": "Graduation Year field label"}, "gpaGrade": "GPA Grade", "@gpaGrade": {"description": "GPA Grade field label"}, "salaryAmount": "<PERSON><PERSON> Amount", "@salaryAmount": {"description": "Salary Amount field label"}, "accountNumber": "Account Number", "@accountNumber": {"description": "Account Number field label"}, "ibanNumber": "IBAN Number", "@ibanNumber": {"description": "IBAN Number field label"}, "passwordRequired": "Password is required for new employees", "@passwordRequired": {"description": "Password required validation message"}, "children": "Children", "@children": {"description": "Children field label"}, "gender": "Gender", "@gender": {"description": "Gender field label"}, "dateOfBirth": "Date of Birth", "@dateOfBirth": {"description": "Date of Birth field label"}, "notes": "Notes", "@notes": {"description": "Notes field label"}, "password": "Password", "@password": {"description": "Password field label"}, "personalInformation": "Personal Information", "@personalInformation": {}, "healthInformation": "Health Information", "@healthInformation": {}, "sportsActivities": "Sports Activities", "@sportsActivities": {}, "paymentDetails": "Payment Details", "@paymentDetails": {}, "fullName": "Full Name", "@fullName": {}, "parentPhone": "Parent Phone", "@parentPhone": {}, "nationalId": "National ID", "@nationalId": {}, "birthdate": "Birthdate", "@birthdate": {}, "nationality": "Nationality", "@nationality": {}, "fullAddress": "Full Address", "@fullAddress": {"description": "Full address label"}, "additionalInformation": "Additional Information", "@additionalInformation": {"description": "Additional information section title"}, "address": "Address", "@address": {"description": "Address label"}, "pleaseEnterName": "Please enter a name", "@pleaseEnterName": {"description": "Validation message for name field"}, "pleaseEnterEmail": "Please enter an email", "@pleaseEnterEmail": {"description": "Validation message for email field"}, "healthConditions": "Health Conditions", "@healthConditions": {}, "paymentType": "Payment Type", "@paymentType": {}, "searchEmployees": "Search Employees", "@searchEmployees": {}, "noEmployeesFound": "No Employees Found", "@noEmployeesFound": {}, "noMatchingEmployeesFound": "No Matching Employees Found", "@noMatchingEmployeesFound": {}, "addEmployee": "Add Employee", "@addEmployee": {}, "name": "Name", "@name": {"description": "Label for name field"}, "dailyIncomeSummary": "Daily Income Summary", "@dailyIncomeSummary": {"description": "Title for the daily income summary section"}, "stadiumBookings": "Stadium Bookings", "@stadiumBookings": {"description": "Label for stadium bookings income"}, "subscriptions": "Subscriptions", "@subscriptions": {"description": "Label for subscriptions income"}, "uniforms": "Uniforms", "@uniforms": {"description": "Label for uniforms income"}, "total": "Total", "@total": {"description": "Label for total amount"}, "english": "English", "@english": {"description": "English language option"}, "arabic": "Arabic", "@arabic": {"description": "Arabic language option"}, "home": "Home", "@home": {"description": "Home screen title"}, "refreshBookings": "Refresh Bookings", "@refreshBookings": {"description": "Tooltip for refresh bookings button"}, "noBookingsAvailable": "No bookings available", "@noBookingsAvailable": {"description": "Message shown when no bookings are available"}, "notAvailable": "N/A", "@notAvailable": {"description": "Shown when a value is not available"}, "date": "Date", "@date": {"description": "Label for date field"}, "bookingStatus": "Booking Status", "@bookingStatus": {"description": "Label for booking status"}, "bookingUpdated": "Booking {status} successfully", "@bookingUpdated": {"description": "Message shown when booking status is updated", "placeholders": {"status": {"type": "String", "example": "confirmed"}}}, "bookingDeleted": "Booking deleted successfully", "@bookingDeleted": {"description": "Message shown when booking is deleted"}, "payrollManagement": "Payroll Management", "@payrollManagement": {"description": "Title for the payroll management screen"}, "selectEmployee": "Select Employee", "@selectEmployee": {"description": "Label for employee selection dropdown"}, "employeeRequired": "Please select an employee", "@employeeRequired": {"description": "Validation message for employee selection"}, "basicSalary": "Basic Salary", "@basicSalary": {"description": "Label for basic salary input"}, "basicSalaryRequired": "Basic salary is required", "@basicSalaryRequired": {"description": "Validation message for basic salary"}, "deductions": "Deductions", "@deductions": {"description": "Label for deductions input"}, "deductionsRequired": "Deductions amount is required", "@deductionsRequired": {"description": "Validation message for deductions"}, "incentives": "Incentives", "@incentives": {"description": "Label for incentives input"}, "incentivesRequired": "Incentives amount is required", "@incentivesRequired": {"description": "Validation message for incentives"}, "payrollRecords": "Payroll Records", "@payrollRecords": {"description": "Title for the payroll records section"}, "sportsManagement": "Sports Management", "@sportsManagement": {"description": "Sports Management screen title"}, "noSportsFound": "No sports found", "@noSportsFound": {"description": "Message shown when no sports are available"}, "fees": "Fees", "@fees": {"description": "Label for sport fees"}, "sessionsPerMonth": "Sessions per month", "@sessionsPerMonth": {"description": "Label for number of sessions per month"}, "trainingDays": "Training days", "@trainingDays": {"description": "Label for training days"}, "trainingTimes": "Training Times", "@trainingTimes": {"description": "Label for training times"}, "editSport": "Edit Sport", "@editSport": {}, "addSport": "Add Sport", "@addSport": {}, "sportName": "Sport Name", "@sportName": {}, "requiredField": "This field is required", "@requiredField": {}, "numberOfSessions": "Number of Sessions", "@numberOfSessions": {}, "businessHours": "Business Hours", "@businessHours": {}, "sportSavedSuccessfully": "Sport saved successfully", "@sportSavedSuccessfully": {}, "deleteSport": "Delete Sport", "@deleteSport": {}, "deleteConfirmation": "Are you sure you want to delete {name}?", "@deleteConfirmation": {"placeholders": {"name": {"type": "String", "example": "Football"}}}, "balance": "Balance", "@balance": {"description": "Balance label for financial information"}, "basicInformation": "Basic Information", "@basicInformation": {"description": "Basic Information section title"}, "phoneNumber": "Phone Number", "@phoneNumber": {"description": "Phone Number field label"}, "enterCustomerPhoneNumber": "Enter customer phone number", "@enterCustomerPhoneNumber": {"description": "Hint for customer phone number field"}, "pleaseEnterCustomerPhoneNumber": "Please enter customer phone number", "@pleaseEnterCustomerPhoneNumber": {"description": "Validation message for customer phone number"}, "enterParentPhoneNumber": "Enter parent phone number", "@enterParentPhoneNumber": {"description": "Hint for parent phone number field"}, "addressInformation": "Address Information", "@addressInformation": {"description": "Address Information section title"}, "enterAddress": "Enter address", "@enterAddress": {"description": "Hint for address field"}, "pleaseEnterAddress": "Please enter address", "@pleaseEnterAddress": {"description": "Validation message for address field"}, "enterDetailedAddress": "Enter detailed address", "@enterDetailedAddress": {"description": "Hint for detailed address field"}, "enterNationalIdNumber": "Enter national ID number", "@enterNationalIdNumber": {"description": "Hint for national ID field"}, "pleaseEnterNationalId": "Please enter national ID", "@pleaseEnterNationalId": {"description": "Validation message for national ID field"}, "selectDateOfBirth": "Select date of birth", "@selectDateOfBirth": {"description": "Hint for date of birth field"}, "pleaseSelectDateOfBirth": "Please select date of birth", "@pleaseSelectDateOfBirth": {"description": "Validation message for date of birth field"}, "age": "Age", "@age": {"description": "Age label"}, "years": "years", "@years": {"description": "Years unit for age"}, "male": "Male", "@male": {"description": "Male gender option"}, "female": "Female", "@female": {"description": "Female gender option"}, "searchNationality": "Search nationality", "@searchNationality": {"description": "Hint for nationality search field"}, "pleaseEnterAName": "Please enter a name", "@pleaseEnterAName": {"description": "Validation message for name field"}, "pleaseEnterAnEmail": "Please enter an email", "@pleaseEnterAnEmail": {"description": "Validation message for email field"}, "sportDeletedSuccessfully": "Sport deleted successfully", "@sportDeletedSuccessfully": {"description": "Message shown when a sport is deleted"}, "failedToDeleteSport": "Failed to delete sport: {error}", "@failedToDeleteSport": {"description": "Error message when sport deletion fails", "placeholders": {"error": {"type": "String", "example": "Network error"}}}, "sportRefundedSuccessfully": "Sport refunded successfully", "@sportRefundedSuccessfully": {"description": "Message shown when a sport is refunded"}, "failedToRefundSport": "Failed to refund sport: {error}", "@failedToRefundSport": {"description": "Error message when sport refund fails", "placeholders": {"error": {"type": "String", "example": "Network error"}}}, "refundSport": "Refund Sport", "@refundSport": {"description": "Title for sport refund dialog"}, "areYouSureRefundSport": "Are you sure you want to refund this sport?", "@areYouSureRefundSport": {"description": "Confirmation message for sport refund"}, "trainerDeletedSuccessfully": "Trainer deleted successfully", "@trainerDeletedSuccessfully": {"description": "Message shown when a trainer is deleted"}, "deleteTrainer": "Delete Trainer", "@deleteTrainer": {"description": "Title for trainer deletion dialog"}, "areYouSureDeleteTrainer": "Are you sure you want to delete this trainer?", "@areYouSureDeleteTrainer": {"description": "Confirmation message for trainer deletion"}, "selectStadiumAndTimeSlots": "Please select both stadium and time slots", "@selectStadiumAndTimeSlots": {"description": "Validation message for stadium booking"}, "fillAllUserInformation": "Please fill in all user information", "@fillAllUserInformation": {"description": "Validation message for user information"}, "bookingSuccessful": "Booking Successful!", "@bookingSuccessful": {"description": "Title for booking success dialog"}, "bookingDeletedSuccessfully": "Booking deleted successfully!", "@bookingDeletedSuccessfully": {"description": "Message shown when a booking is deleted"}, "sportFeesAmount": "Fees: {amount}", "@sportFeesAmount": {"description": "Label for sport fees with amount", "placeholders": {"amount": {"type": "String", "example": "$100.00"}}}, "sportSessions": "Sessions: {count}", "@sportSessions": {"description": "Label for sport sessions count", "placeholders": {"count": {"type": "String", "example": "10"}}}, "sportTime": "Time: {time}", "@sportTime": {"description": "Label for sport time", "placeholders": {"time": {"type": "String", "example": "10:00 AM"}}}, "sportTrainers": "Trainers: {trainers}", "@sportTrainers": {"description": "Label for sport trainers", "placeholders": {"trainers": {"type": "String", "example": "<PERSON>, <PERSON>"}}}, "feesPerHour": "Fees per Hour", "@feesPerHour": {"description": "Label for stadium hourly fees"}, "perHour": "/hour", "@perHour": {"description": "Suffix for hourly rate"}, "pleaseEnterStadiumName": "Please enter stadium name", "@pleaseEnterStadiumName": {"description": "Validation message for stadium name"}, "pleaseEnterFeesPerHour": "Please enter fees per hour", "@pleaseEnterFeesPerHour": {"description": "Validation message for stadium hourly fees"}, "registerStadium": "Register Stadium", "@registerStadium": {"description": "Button label for stadium registration"}, "sportsFees": "Sports Fees:", "@sportsFees": {"description": "Label for sports fees"}, "uniformFees": "Uniform Fees:", "@uniformFees": {"description": "Label for uniform fees"}, "sar": "SAR", "@sar": {"description": "Saudi Riyal currency code"}, "otherHealthConditions": "Other Health Conditions", "@otherHealthConditions": {"description": "Label for other health conditions field"}, "enterHealthConditions": "Enter any other health conditions, allergies, or medications", "@enterHealthConditions": {"description": "Hint for health conditions input field"}, "separateConditions": "Separate multiple conditions with commas", "@separateConditions": {"description": "Instruction for entering multiple health conditions"}, "provideAccurateHealth": "Please provide accurate health information...", "@provideAccurateHealth": {"description": "Message about providing accurate health information"}, "consultDoctor": "If you have any serious health conditions, please consult with a doctor...", "@consultDoctor": {"description": "Message about consulting with a doctor for serious health conditions"}, "unknownSport": "Unknown Sport", "@unknownSport": {"description": "Label for unknown sport"}, "trainer": "Trainer", "@trainer": {"description": "Label for trainer information"}, "sessionDays": "Session Days:", "@sessionDays": {"description": "Label for session days information"}, "weeklyOn": "Weekly on", "@weeklyOn": {"description": "Prefix for weekly schedule days"}, "currentScreen": "Current Screen:", "@currentScreen": {"description": "Label for current screen information"}, "status": "Status", "@status": {"description": "Status label for items"}, "promoCodes": "Promo Codes", "@promoCodes": {"description": "Promo Codes screen title"}, "addPromoCode": "Add Promo Code", "@addPromoCode": {"description": "Add Promo Code dialog title"}, "editPromoCode": "Edit Promo Code", "@editPromoCode": {"description": "Edit Promo Code dialog title"}, "code": "Code", "@code": {"description": "Promo code label"}, "description": "Description", "@description": {"description": "Description label"}, "discountType": "Discount Type", "@discountType": {"description": "Discount type label"}, "percentage": "Percentage", "@percentage": {"description": "Percentage discount type"}, "fixed": "Fixed Amount", "@fixed": {"description": "Fixed amount discount type"}, "discountValue": "Discount Value", "@discountValue": {"description": "Discount value label"}, "maxUses": "Maximum Uses", "@maxUses": {"description": "Maximum uses label"}, "endDate": "End Date", "@endDate": {"description": "End date label"}, "isActive": "Is Active", "@isActive": {"description": "Is active label"}, "update": "Update", "@update": {"description": "Update button text"}, "refresh": "Refresh", "@refresh": {"description": "Refresh button text"}, "noPromoCodesFound": "No promo codes found", "@noPromoCodesFound": {"description": "Message when no promo codes are found"}, "usage": "Usages", "@usage": {}, "usageStatistics": "Usage Statistics", "@usageStatistics": {"description": "Title for promo code usage statistics section"}, "totalUsages": "Total Usages", "@totalUsages": {"description": "Label for total number of promo code usages"}, "remaining": "Remaining", "@remaining": {"description": "Label for remaining promo code uses"}, "usageHistory": "Usage History", "@usageHistory": {"description": "Title for promo code usage history section"}, "noUsagesYet": "No usages recorded yet", "@noUsagesYet": {"description": "Message shown when no promo code usages exist"}, "addNewUsage": "Add New Usage", "@addNewUsage": {"description": "Title for adding new promo code usage section"}, "selectCustomer": "Select Customer", "@selectCustomer": {"description": "Label for customer selection dropdown"}, "maxUsesReached": "Maximum uses reached", "@maxUsesReached": {"description": "Message shown when promo code has reached maximum uses"}, "noEligibleCustomers": "No eligible customers", "@noEligibleCustomers": {"description": "Message shown when no customers are eligible for promo code"}, "close": "Close", "@close": {"description": "Close button text for dialogs"}, "eligibility": "Eligibility", "@eligibility": {"description": "Label for promo code eligibility button and dialog title"}, "selectCustomers": "Select customers eligible for this promo code:", "@selectCustomers": {"description": "Instruction text for selecting customers eligible for a promo code"}, "validity": "Validity", "@validity": {}, "createdAt": "Created At", "@createdAt": {}, "confirmDelete": "Are you sure you want to delete?", "@confirmDelete": {}, "deletePromoCodeConfirmation": "Are you sure you want to delete this promo code?", "@deletePromoCodeConfirmation": {}, "discount": "Discount", "@discount": {}, "discountAmount": "Discount Amount", "@discountAmount": {}, "currency": "<PERSON><PERSON><PERSON><PERSON>", "@currency": {}, "validFrom": "<PERSON><PERSON>", "@validFrom": {}, "validTo": "<PERSON><PERSON>", "@validTo": {}, "usesCount": "Uses Count", "@usesCount": {}, "usages": "Usages", "@usages": {}, "asthma": "Asthma", "@asthma": {"description": "Asthma health condition label"}, "diabetes": "Diabetes", "@diabetes": {"description": "Diabetes health condition label"}, "heartCondition": "Heart Condition", "@heartCondition": {"description": "Heart condition label"}, "allergies": "Allergies", "@allergies": {"description": "Allergies health condition label"}, "highBloodPressure": "High Blood Pressure", "@highBloodPressure": {"description": "High blood pressure health condition label"}, "jointProblems": "Joint Problems", "@jointProblems": {"description": "Joint problems health condition label"}, "backPain": "Back Pain", "@backPain": {"description": "Back pain health condition label"}, "epilepsy": "Epilepsy", "@epilepsy": {"description": "Epilepsy health condition label"}, "none": "None", "@none": {"description": "None health condition label"}, "selectHealthConditions": "Select any health conditions that apply:", "@selectHealthConditions": {"description": "Prompt to select health conditions"}, "healthDisclaimer": "Health Disclaimer", "@healthDisclaimer": {"description": "Health disclaimer section title"}, "importantInformation": "Important Information", "@importantInformation": {"description": "Important information label in disclaimer"}, "summary": "Summary", "@summary": {}, "customerRegisteredSuccessfully": "Customer registered successfully.", "@customerRegisteredSuccessfully": {}, "customerUpdatedSuccessfully": "Customer updated successfully.", "@customerUpdatedSuccessfully": {}, "customerDeletedSuccessfully": "Customer deleted successfully.", "@customerDeletedSuccessfully": {}, "errorLoadingBookings": "An error occurred while loading bookings: {error}", "bookingStatusUpdated": "Booking status updated to {status}", "failedToUpdateBookingStatus": "Failed to update the booking status.", "@failedToUpdateBookingStatus": {}, "errorUpdatingBookingStatus": "An error occurred while updating the booking status.", "@errorUpdatingBookingStatus": {}, "searchCustomers": "Search customers", "@searchCustomers": {}, "pleaseEnterValidRefundAmount": "Please enter a valid refund amount.", "@pleaseEnterValidRefundAmount": {}, "errorWithDetails": "An error occurred: {details}", "@errorWithDetails": {"placeholders": {"details": {}}}, "failedToLoadSportsWithStatus": "Failed to load sports with status: {status}", "@failedToLoadSportsWithStatus": {"placeholders": {"status": {}}}, "errorLoadingSportsWithDetails": "Error loading sports: {details}", "@errorLoadingSportsWithDetails": {"placeholders": {"details": {}}}, "football": "Football", "@football": {}, "swimming": "Swimming", "@swimming": {}, "basketball": "Basketball", "@basketball": {}, "errorLoadingTrainersWithDetails": "Error loading trainers: {details}", "@errorLoadingTrainersWithDetails": {"placeholders": {"details": {}}}, "errorDeletingCustomerWithDetails": "Error deleting customer: {details}", "@errorDeletingCustomerWithDetails": {"placeholders": {"details": {}}}, "enterOtherHealthConditions": "Enter any other health conditions", "@enterOtherHealthConditions": {}, "separateWithCommas": "Separate multiple conditions with commas", "@separateWithCommas": {}, "provideAccurateHealthInfo": "Please provide accurate health information", "@provideAccurateHealthInfo": {}, "consultDoctorBeforeSports": "Consult a doctor before engaging in sports activities", "@consultDoctorBeforeSports": {}, "cash": "Cash", "@cash": {}, "creditCard": "Credit Card", "@creditCard": {}, "debitCard": "Debit Card", "@debitCard": {}, "bankTransfer": "Bank Transfer", "@bankTransfer": {}, "stcPay": "STC Pay", "@stcPay": {}, "mada": "<PERSON><PERSON>", "@mada": {}, "applePay": "Apple Pay", "@applePay": {}, "installment": "Installment", "@installment": {}, "active": "Active", "@active": {}, "inactive": "Inactive", "@inactive": {}, "paymentMethod": "Payment Method", "@paymentMethod": {}, "selectPaymentMethod": "Select Payment Method", "@selectPaymentMethod": {}, "paymentNote": "Payment Note", "@paymentNote": {}, "enterPaymentNotes": "Enter payment notes here", "@enterPaymentNotes": {}, "customerStatus": "Customer Status", "@customerStatus": {}, "activeCustomerDescription": "This customer is active and can make bookings.", "@activeCustomerDescription": {}, "inactiveCustomerDescription": "This customer is inactive and cannot make bookings.", "@inactiveCustomerDescription": {}, "promoCode": "Promo Code", "@promoCode": {}, "enterPromoCode": "Enter Promo Code", "@enterPromoCode": {}, "enterPromoCodeHint": "e.g., SAVE10", "@enterPromoCodeHint": {}, "apply": "Apply", "@apply": {}, "newTotal": "New Total", "@newTotal": {}, "paymentSummary": "Payment Summary", "@paymentSummary": {}, "sportsTax": "Sports Tax", "@sportsTax": {}, "uniformTax": "Uniform Tax", "@uniformTax": {}, "totalFees": "Total Fees", "@totalFees": {}, "finalAmount": "Final Amount", "@finalAmount": {}, "paymentInstructions": "Please follow the instructions below to complete your payment:", "@paymentInstructions": {}, "registrationSummary": "Registration Summary", "@registrationSummary": {}, "reviewYourInformation": "Please Review Your Information", "@reviewYourInformation": {}, "disclaimerText": "By submitting this form, you confirm that all the information provided is accurate and complete. You agree to the terms and conditions of Hemma Sports Club.", "@disclaimerText": {}, "noSportsSelected": "No sports selected", "@noSportsSelected": {}, "level": "Level", "@level": {}, "subscription": "Subscription", "@subscription": {}, "notSelected": "Not selected", "@notSelected": {}, "basedOnSessions": "Based on sessions", "@basedOnSessions": {}, "notSpecified": "Not specified", "@notSpecified": {}, "notCalculated": "Not calculated", "@notCalculated": {}, "noName": "No Name", "@noName": {}, "noPosition": "No Position", "@noPosition": {}, "noDepartment": "No Department", "@noDepartment": {}, "errorLoadingEmployees": "Error loading employees: {error}", "@errorLoadingEmployees": {"description": "Error message displayed when employee data fails to load.", "placeholders": {"error": {"type": "String", "example": "Network request failed"}}}, "other": "Other", "@other": {}, "notSet": "Not set", "@notSet": {}, "mobileNumber": "Mobile Number", "@mobileNumber": {}, "emailAddress": "Email Address", "@emailAddress": {}, "invalidEmail": "Please enter a valid email address", "@invalidEmail": {}, "numberOfChildren": "Number of Children", "@numberOfChildren": {}, "permanent": "Permanent", "@permanent": {}, "temporary": "Temporary", "@temporary": {}, "intern": "Intern", "@intern": {}, "additionalContactInfo": "Additional Contact Info", "@additionalContactInfo": {}, "city": "City", "@city": {}, "street": "Street", "@street": {}, "passwordRequiredForNewEmployees": "Password is required for new employees", "@passwordRequiredForNewEmployees": {}, "createEmployee": "Create Employee", "@createEmployee": {}, "updateEmployee": "Update Employee", "@updateEmployee": {}, "employeeCreatedSuccessfully": "Employee created successfully", "@employeeCreatedSuccessfully": {}, "employeeUpdatedSuccessfully": "Employee updated successfully", "@employeeUpdatedSuccessfully": {}, "employeeDeletedSuccessfully": "Employee deleted successfully", "@employeeDeletedSuccessfully": {}, "error": "Error: {error}", "@error": {"description": "Generic error message with an error placeholder.", "placeholders": {"error": {"type": "String", "example": "Network request failed"}}}, "errorLoadingStadiumBookings": "Error loading stadium bookings: {error}", "errorLoadingSubscriptions": "Error loading subscriptions: {error}", "errorLoadingUniforms": "Error loading uniforms: {error}", "@errorLoadingStadiumBookings": {"description": "Error message displayed when stadium bookings fail to load.", "placeholders": {"error": {"type": "String", "example": "Network request failed"}}}, "@errorLoadingSubscriptions": {"description": "Error message displayed when subscriptions fail to load.", "placeholders": {"error": {"type": "String", "example": "Server returned an error"}}}, "@errorLoadingUniforms": {"description": "Error message displayed when uniforms fail to load.", "placeholders": {"error": {"type": "String", "example": "API endpoint not found"}}}, "unknownStadium": "Unknown Stadium", "@unknownStadium": {}, "serverReturnedError": "Server returned error: {statusCode}", "errorUpdatingBooking": "Error updating booking: {error}", "@bookingStatusUpdated": {"description": "Message displayed when a booking's status is updated successfully.", "placeholders": {"status": {"type": "String", "example": "confirmed"}}}, "@errorLoadingBookings": {"description": "Error message displayed when bookings fail to load.", "placeholders": {"error": {"type": "String", "example": "Network request failed"}}}, "@serverReturnedError": {"description": "Error message displayed when the server returns an unexpected status code.", "placeholders": {"statusCode": {"type": "String", "example": "500"}}}, "@errorUpdatingBooking": {"description": "Error message displayed when updating a booking fails.", "placeholders": {"error": {"type": "String", "example": "Invalid booking ID"}}}, "pleaseEnterValidEmail": "Please enter a valid email address.", "@pleaseEnterValidEmail": {}, "pleaseEnterPhoneOrParentPhone": "Please enter either the subscriber's mobile number or the guardian's mobile number.", "@pleaseEnterPhoneOrParentPhone": {}, "enterPhoneNumber": "Enter phone number", "@enterPhoneNumber": {"description": "Hint text for phone number input field"}, "pleaseSelectGender": "Please select your gender.", "@pleaseSelectGender": {}, "selectNationality": "Select Nationality", "@selectNationality": {}, "pleaseSelectNationality": "Please select your nationality.", "@pleaseSelectNationality": {}, "pleaseEnterAddressOrFullAddress": "Please enter your residential address.", "@pleaseEnterAddressOrFullAddress": {}, "sport": "Sport", "@sport": {}, "actions": "Actions", "@actions": {}, "tax": "Tax", "@tax": {"description": "Label for tax"}, "pleaseEnterSportName": "Please enter sport name", "@pleaseEnterSportName": {}, "pleaseEnterNumberOfSessions": "Please enter number of sessions", "@pleaseEnterNumberOfSessions": {}, "pleaseEnterValidNumber": "Please enter a valid number", "@pleaseEnterValidNumber": {}, "pleaseEnterFees": "Please enter fees", "@pleaseEnterFees": {}, "pleaseEnterValidAmount": "Please enter a valid amount", "@pleaseEnterValidAmount": {}, "accountType": "Account Type", "selectAccountType": "Select Account Type", "individual": "Individual", "family": "Family", "enterNumberOfChildren": "Enter number of children", "pleaseSelectAccountType": "Please select an account type", "pleaseEnterNumberOfChildren": "Please enter the number of children", "previous": "Previous", "familyMember": "Family Member", "pleaseEnterNationality": "Please enter nationality", "sportActivity": "Sport Activity", "pleaseEnterSportActivity": "Please enter sport activity", "sportLevel": "Sport Level", "pleaseEnterSportLevel": "Please enter sport level", "pleaseEnterTrainer": "Please enter trainer", "pleaseEnterSubscriptionType": "Please enter subscription type", "sportsInformation": "Sports Information", "paymentInformation": "Payment Information", "familyMembers": "Family Members", "howDidHearAboutUs": "How did you hear about us?", "enterHowDidHearAboutUs": "Enter how you heard about us"}