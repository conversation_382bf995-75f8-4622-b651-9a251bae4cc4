import 'package:flutter/material.dart';
import 'package:hemmaerp/layouts/responsive_layout.dart';
import '../constants/constants.dart';

class AuthLayout extends StatelessWidget {
  final Widget child;
  final String title;
  final String? subtitle;
  final Widget? logo;

  const AuthLayout({
    super.key,
    required this.child,
    required this.title,
    this.subtitle,
    this.logo,
  });

  @override
  Widget build(BuildContext context) {
    final bool isDesktopView = ResponsiveLayout.isDesktop(context);
    
    return Scaffold(
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(LayoutConstants.paddingL),
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  maxWidth: isDesktopView ? 500 : 400,
                ),
                child: Card(
                  elevation: isDesktopView ? 4 : 1,
                  child: Padding(
                    padding: const EdgeInsets.all(LayoutConstants.paddingL),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        if (logo != null) ...[
                          Center(child: logo),
                          LayoutConstants.spacingXL,
                        ] else ...[
                          const Icon(
                            Icons.business,
                            size: 80,
                            color: ThemeConstants.primaryColor,
                          ),
                          LayoutConstants.spacingXL,
                        ],
                        Text(
                          title,
                          style: Theme.of(context).textTheme.headlineMedium,
                          textAlign: TextAlign.center,
                        ),
                        if (subtitle != null) ...[
                          LayoutConstants.spacingS,
                          Text(
                            subtitle!,
                            style: Theme.of(context).textTheme.bodyMedium,
                            textAlign: TextAlign.center,
                          ),
                        ],
                        LayoutConstants.spacingXL,
                        child,
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}