import 'package:flutter/material.dart';
import 'package:hemmaerp/layouts/responsive_layout.dart';
import '../constants/constants.dart';
// Make sure this import is present

class DashboardLayout extends StatelessWidget {
  final List<Widget> cards;
  final List<Widget>? charts;
  final List<Widget>? tables;
  final Widget? header;

  const DashboardLayout({
    super.key,
    required this.cards,
    this.charts,
    this.tables,
    this.header,
  });

  @override
  Widget build(BuildContext context) {
    final bool isDesktopView = ResponsiveLayout.isDesktop(context);
    
    return SingleChildScrollView(
      child: Center(
        child: ConstrainedBox(
          constraints: BoxConstraints(
            maxWidth: isDesktopView ? LayoutConstants.maxContentWidth : double.infinity,
          ),
          child: Padding(
            padding: const EdgeInsets.all(LayoutConstants.paddingM),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (header != null) ...[
                  header!,
                  LayoutConstants.spacingL,
                ],
                _buildCardGrid(context),
                if (charts != null && charts!.isNotEmpty) ...[
                  LayoutConstants.spacingL,
                  Text('Analytics', style: TextStyles.titleLarge),
                  LayoutConstants.spacingM,
                  _buildChartsSection(context),
                ],
                if (tables != null && tables!.isNotEmpty) ...[
                  LayoutConstants.spacingL,
                  Text('Recent Activity', style: TextStyles.titleLarge),
                  LayoutConstants.spacingM,
                  ...tables!,
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCardGrid(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final width = constraints.maxWidth;
        int crossAxisCount;
        
        if (width < LayoutConstants.breakpointMobile) {
          crossAxisCount = 1;
        } else if (width < LayoutConstants.breakpointTablet) {
          crossAxisCount = 2;
        } else if (width < LayoutConstants.breakpointDesktop) {
          crossAxisCount = 3;
        } else {
          crossAxisCount = 4;
        }
        
        return GridView.count(
          crossAxisCount: crossAxisCount,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          mainAxisSpacing: LayoutConstants.marginM,
          crossAxisSpacing: LayoutConstants.marginM,
          childAspectRatio: 1.5, // Wider cards for web
          children: cards,
        );
      },
    );
  }

  Widget _buildChartsSection(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final width = constraints.maxWidth;
        
        if (width < LayoutConstants.breakpointTablet) {
          // Stack charts vertically on smaller screens
          return Column(
            children: [
              for (var chart in charts!) ...[
                chart,
                if (chart != charts!.last) LayoutConstants.spacingM,
              ],
            ],
          );
        } else {
          // Display charts in a row with wrapping on larger screens
          return Wrap(
            spacing: LayoutConstants.marginM,
            runSpacing: LayoutConstants.marginM,
            children: charts!,
          );
        }
      },
    );
  }
}