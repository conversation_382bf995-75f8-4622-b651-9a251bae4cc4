import 'package:flutter/material.dart';
import '../constants/constants.dart';

class DetailLayout extends StatelessWidget {
  final String title;
  final List<Widget> sections;
  final List<Widget>? actions;
  final Widget? header;
  final Widget? footer;
  final EdgeInsetsGeometry? padding;

  const DetailLayout({
    super.key,
    required this.title,
    required this.sections,
    this.actions,
    this.header,
    this.footer,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader(context),
        Expanded(
          child: SingleChildScrollView(
            padding: padding ?? const EdgeInsets.all(LayoutConstants.paddingM),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: _buildSections(),
            ),
          ),
        ),
        if (footer != null) footer!,
      ],
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(LayoutConstants.paddingM),
      decoration: const BoxDecoration(
        color: ThemeConstants.surfaceColor,
        border: Border(
          bottom: BorderSide(
            color: ThemeConstants.borderColor,
            width: LayoutConstants.borderWidthRegular,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  title,
                  style: TextStyles.headline6,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              if (actions != null && actions!.isNotEmpty)
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: actions!,
                ),
            ],
          ),
          if (header != null) ...[
            LayoutConstants.spacingM,
            header!,
          ],
        ],
      ),
    );
  }

  List<Widget> _buildSections() {
    final List<Widget> result = [];
    
    for (int i = 0; i < sections.length; i++) {
      result.add(sections[i]);
      
      if (i < sections.length - 1) {
        result.add(LayoutConstants.spacingL);
      }
    }
    
    return result;
  }
}