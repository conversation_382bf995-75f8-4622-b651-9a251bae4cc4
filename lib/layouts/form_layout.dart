import 'package:flutter/material.dart';
import 'package:hemmaerp/layouts/responsive_layout.dart';
import '../constants/constants.dart';

class FormLayout extends StatelessWidget {
  final String title;
  final List<Widget> formFields;
  final Widget? submitButton;
  final Widget? cancelButton;
  final Widget? header;
  final bool showDividers;
  final EdgeInsetsGeometry? padding;
  final ScrollPhysics? physics;

  const FormLayout({
    super.key,
    required this.title,
    required this.formFields,
    this.submitButton,
    this.cancelButton,
    this.header,
    this.showDividers = false,
    this.padding,
    this.physics,
  });

  @override
  Widget build(BuildContext context) {
    final bool isDesktopView = ResponsiveLayout.isDesktop(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(LayoutConstants.paddingM),
          child: Text(title, style: TextStyles.headline6),
        ),
        if (header != null) header!,
        Expanded(
          child: Center(
            child: ConstrainedBox(
              constraints: BoxConstraints(
                maxWidth: isDesktopView ? 800 : double.infinity,
              ),
              child: Card(
                margin: const EdgeInsets.all(LayoutConstants.marginM),
                child: SingleChildScrollView(
                  physics: physics,
                  padding: padding ?? const EdgeInsets.all(LayoutConstants.paddingL),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: _buildFormFields(),
                  ),
                ),
              ),
            ),
          ),
        ),
        if (submitButton != null || cancelButton != null)
          _buildButtonBar(context),
      ],
    );
  }

  List<Widget> _buildFormFields() {
    final List<Widget> fields = [];
    
    for (int i = 0; i < formFields.length; i++) {
      fields.add(formFields[i]);
      
      if (showDividers && i < formFields.length - 1) {
        fields.add(const Divider());
      } else if (i < formFields.length - 1) {
        fields.add(LayoutConstants.spacingM);
      }
    }
    
    return fields;
  }

  Widget _buildButtonBar(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(LayoutConstants.paddingM),
      decoration: const BoxDecoration(
        color: ThemeConstants.surfaceColor,
        border: Border(
          top: BorderSide(
            color: ThemeConstants.borderColor,
            width: LayoutConstants.borderWidthRegular,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          if (cancelButton != null) cancelButton!,
          if (cancelButton != null && submitButton != null)
            LayoutConstants.spacingM,
          if (submitButton != null) submitButton!,
        ],
      ),
    );
  }
}