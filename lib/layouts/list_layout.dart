import 'package:flutter/material.dart';
import '../constants/constants.dart';

class ListLayout extends StatelessWidget {
  final String title;
  final List<Widget> items;
  final Widget? header;
  final Widget? footer;
  final bool showDividers;
  final EdgeInsetsGeometry? padding;
  final ScrollPhysics? physics;
  final bool shrinkWrap;

  const ListLayout({
    super.key,
    required this.title,
    required this.items,
    this.header,
    this.footer,
    this.showDividers = true,
    this.padding,
    this.physics,
    this.shrinkWrap = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: LayoutConstants.paddingM,
            vertical: LayoutConstants.paddingS,
          ),
          child: Text(title, style: TextStyles.headline6),
        ),
        if (header != null) header!,
        Expanded(
          child: ListView.separated(
            padding: padding ?? const EdgeInsets.all(LayoutConstants.paddingM),
            physics: physics,
            shrinkWrap: shrinkWrap,
            itemCount: items.length,
            separatorBuilder: (context, index) => showDividers
                ? const Divider(height: 1)
                : LayoutConstants.spacingS,
            itemBuilder: (context, index) => items[index],
          ),
        ),
        if (footer != null) footer!,
      ],
    );
  }
}