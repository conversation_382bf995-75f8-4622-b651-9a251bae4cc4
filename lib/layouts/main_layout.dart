// ignore_for_file: deprecated_member_use, sort_child_properties_last

import 'package:flutter/material.dart';

class MainLayout extends StatelessWidget {
  final Widget child;
  final String title;

  const MainLayout({
    Key? key,
    required this.child,
    required this.title,
  }) : super(key: key);

  Widget _buildDrawerItem({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String route,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListTile(
        leading: Icon(icon, color: Theme.of(context).colorScheme.primary),
        title: Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        onTap: () {
          Navigator.pushReplacementNamed(context, route);
        },
        hoverColor: Theme.of(context).colorScheme.primary.withOpacity(0.1),
        selectedTileColor: Theme.of(context).colorScheme.primary.withOpacity(0.1),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                Icons.sports_soccer,
                color: Theme.of(context).colorScheme.primary,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            Text(
              'Hemma ERP',
              style: TextStyle(
                color: Theme.of(context).colorScheme.onSurface, // Assuming Colors.white AppBar background
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.notifications_outlined, color: Theme.of(context).colorScheme.onSurface), // Assuming Colors.white AppBar background
            onPressed: () {},
          ),
          IconButton(
            icon: Icon(Icons.settings_outlined, color: Theme.of(context).colorScheme.onSurface), // Assuming Colors.white AppBar background
            onPressed: () {},
          ),
          Container(
            margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.person_outline,
                  color: Theme.of(context).colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Admin',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onSurface, // Assuming Colors.white AppBar background
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      drawer: Drawer(
        elevation: 0,
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white, // This should ideally be Theme.of(context).colorScheme.surface
            boxShadow: [
              BoxShadow(
                color: Theme.of(context).colorScheme.shadow.withOpacity(0.1), // Or primaryContainer
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: ListView(
            padding: EdgeInsets.zero,
            children: [
              DrawerHeader(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Theme.of(context).colorScheme.primaryContainer, // Or primaryColorDark
                      Theme.of(context).colorScheme.primary, // Or primaryColor
                    ],
                  ),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.sports_soccer,
                        size: 50,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'Hemma ERP',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        letterSpacing: 0.5,
                      ),
                    ),
                  ],
                ),
              ),
            _buildDrawerItem(
              context: context,
              icon: Icons.book,
              title: 'General Ledger',
              route: '/general-ledger',
            ),
            _buildDrawerItem(
              context: context,
              icon: Icons.people,
              title: 'Customers',
              route: '/customers',
            ),
            _buildDrawerItem(
              context: context,
              icon: Icons.sports,
              title: 'Sports',
              route: '/sports',
            ),
            _buildDrawerItem(
              context: context,
              icon: Icons.calendar_today,
              title: 'Bookings',
              route: '/bookings',
            ),
            _buildDrawerItem(
              context: context,
              icon: Icons.fitness_center,
              title: 'Trainers',
              route: '/trainers',
            ),
            _buildDrawerItem(
              context: context,
              icon: Icons.group,
              title: 'Employees',
              route: '/employees',
            ),
            _buildDrawerItem(
              context: context,
              icon: Icons.payments,
              title: 'Payroll',
              route: '/payroll',
            ),
          ],
        ),
      ),),
      body: Container(
        padding: const EdgeInsets.all(24.0),
        decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor, // Or colorScheme.background
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(24),
            topRight: Radius.circular(24),
          ),
        ),
        child: child,
      ),
    );
  }
}