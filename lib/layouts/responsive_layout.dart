import 'package:flutter/material.dart';
import '../constants/constants.dart';

class ResponsiveLayout extends StatelessWidget {
  final Widget mobile;
  final Widget? tablet;
  final Widget? desktop;
  final bool centerContent;

  const ResponsiveLayout({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
    this.centerContent = true,
  });

  static bool isMobile(BuildContext context) =>
      MediaQuery.of(context).size.width < LayoutConstants.breakpointMobile;

  static bool isTablet(BuildContext context) =>
      MediaQuery.of(context).size.width >= LayoutConstants.breakpointMobile &&
      MediaQuery.of(context).size.width < LayoutConstants.breakpointDesktop;

  static bool isDesktop(BuildContext context) =>
      MediaQuery.of(context).size.width >= LayoutConstants.breakpointDesktop;

  @override
  Widget build(BuildContext context) {
    final Size size = MediaQuery.of(context).size;

    // Desktop layout
    if (size.width >= LayoutConstants.breakpointDesktop) {
      Widget content = desktop ?? tablet ?? mobile;
      
      if (centerContent && size.width > LayoutConstants.maxContentWidth) {
        return Center(
          child: SizedBox(
            width: LayoutConstants.maxContentWidth,
            child: content,
          ),
        );
      }
      return content;
    }
    
    // Tablet layout
    if (size.width >= LayoutConstants.breakpointMobile) {
      return tablet ?? mobile;
    }
    
    // Mobile layout
    return mobile;
  }
}