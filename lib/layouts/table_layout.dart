// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:hemmaerp/layouts/responsive_layout.dart';
import '../constants/constants.dart';

class TableLayout extends StatelessWidget {
  final String title;
  final List<DataColumn> columns;
  final List<DataRow> rows;
  final Widget? header;
  final Widget? footer;
  final bool showCheckboxColumn;
  final bool isLoading;
  final String? emptyMessage;
  final Function(bool?)? onSelectAll;

  const TableLayout({
    super.key,
    required this.title,
    required this.columns,
    required this.rows,
    this.header,
    this.footer,
    this.showCheckboxColumn = false,
    this.isLoading = false,
    this.emptyMessage,
    this.onSelectAll,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(LayoutConstants.paddingM),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(title, style: TextStyles.headline6),
              if (isLoading)
                const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
            ],
          ),
        ),
        if (header != null) header!,
        Expanded(
          child: _buildTableContent(context),
        ),
        if (footer != null) footer!,
      ],
    );
  }

  Widget _buildTableContent(BuildContext context) {
    if (rows.isEmpty && emptyMessage != null) {
      return Center(
        child: Text(
          emptyMessage!,
          style: TextStyles.bodyMedium.copyWith(
            color: ThemeConstants.secondaryTextColor,
          ),
        ),
      );
    }

    final bool isDesktopView = ResponsiveLayout.isDesktop(context);
    
    return Card(
      margin: const EdgeInsets.all(LayoutConstants.marginS),
      child: Padding(
        padding: const EdgeInsets.all(LayoutConstants.paddingS),
        child: isDesktopView
          ? DataTable(
              showCheckboxColumn: showCheckboxColumn,
              columns: columns,
              rows: rows,
              onSelectAll: onSelectAll,
              headingRowColor: WidgetStateProperty.all(
                ThemeConstants.surfaceColor,
              ),
              dataRowColor: WidgetStateProperty.resolveWith<Color?>(
                (Set<WidgetState> states) {
                  if (states.contains(WidgetState.selected)) {
                    return ThemeConstants.primaryColor.withOpacity(0.1);
                  }
                  return null;
                },
              ),
              dividerThickness: 1,
              horizontalMargin: LayoutConstants.marginM,
              columnSpacing: LayoutConstants.marginL,
            )
          : SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: SingleChildScrollView(
                child: DataTable(
                  showCheckboxColumn: showCheckboxColumn,
                  columns: columns,
                  rows: rows,
                  onSelectAll: onSelectAll,
                  headingRowColor: MaterialStateProperty.all(
                    ThemeConstants.surfaceColor,
                  ),
                  dataRowColor: MaterialStateProperty.resolveWith<Color?>(
                    (Set<MaterialState> states) {
                      if (states.contains(MaterialState.selected)) {
                        return ThemeConstants.primaryColor.withOpacity(0.1);
                      }
                      return null;
                    },
                  ),
                  dividerThickness: 1,
                  horizontalMargin: LayoutConstants.marginM,
                  columnSpacing: LayoutConstants.marginL,
                ),
              ),
            ),
      ),
    );
  }
}