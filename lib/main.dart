// ignore_for_file: unused_import, use_super_parameters, lines_longer_than_80_chars

import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart' as gen_l10n;
import 'package:hemmaerp/constants/app_theme.dart'; // Import AppTheme
import 'package:hemmaerp/providers/language_provider.dart';
import 'package:hemmaerp/providers/theme_provider.dart'; // Import ThemeProvider
import 'package:provider/provider.dart';
import 'package:hemmaerp/generated/l10n.dart';
import 'package:hemmaerp/screens/booking_list_screen.dart';
import 'package:hemmaerp/screens/employee_list_screen.dart';
import 'package:hemmaerp/screens/promocodes/promocode_screen.dart';
import 'package:hemmaerp/screens/sports_list_screen.dart';
import 'package:hemmaerp/screens/sports_schedule_screen.dart';
import 'package:hemmaerp/screens/stadium_booking_screen.dart';
import 'package:hemmaerp/screens/stadium_registration_screen.dart';
import 'package:hemmaerp/screens/trainer_trainee_screen.dart';
import 'package:hemmaerp/screens/visit_booking_screen.dart';
import 'screens/customer_list_screen_new.dart';
import 'screens/customer_registration_multi_screen.dart';
import 'screens/general_ledger_screen.dart';
import 'screens/manage_bookings_screen.dart';
import 'screens/payroll_screen.dart';
import 'screens/trainer_list_screen.dart';
import 'services/service_provider.dart';
import 'layouts/layouts.dart';
import 'screens/home_screen.dart';

void main() {
  WidgetsFlutterBinding.ensureInitialized();
  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => LanguageProvider()),
        ChangeNotifierProvider(create: (context) => ThemeProvider()), // Add ThemeProvider
      ],
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer2<LanguageProvider, ThemeProvider>( // Use Consumer2 for multiple providers
      builder: (context, languageProvider, themeProvider, child) { // Add themeProvider
        return MaterialApp(
          locale: languageProvider.locale,
          title: 'Hemma ERP',
          localizationsDelegates: const [
            gen_l10n.AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [
            Locale('en'), // English
            Locale('ar'), // Arabic
          ],
          theme: AppTheme.getLightTheme(), // Use light theme
          darkTheme: AppTheme.getDarkTheme(), // Use dark theme
          themeMode: themeProvider.themeMode, // Use theme mode from ThemeProvider
          debugShowCheckedModeBanner: false,
          home: const HomeScreen(),
        );
      }
    );
  }
}
