
class Account {
  final int id;
  final String name;
  final double balance;
  final String type;
  final String code;
  final String description;
  final bool isActive;

  Account({
    required this.id,
    required this.name,
    required this.balance,
    required this.type,
    required this.code,
    required this.description,
    required this.isActive,
  });

  factory Account.fromJson(Map<String, dynamic> json) {
    return Account(
      id: json['id'],
      name: json['name'],
      balance: json['balance'],
      type: json['type'],
      code: json['code'],
      description: json['description'],
      isActive: json['is_active'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'balance': balance,
      'type': type,
      'code': code,
      'description': description,
      'is_active': isActive,
    };
  }

  Account copyWith({
    int? id,
    String? name,
    double? balance,
    String? type,
    String? code,
    String? description,
    bool? isActive,
  }) {
    return Account(
      id: id ?? this.id,
      name: name ?? this.name,
      balance: balance ?? this.balance,
      type: type ?? this.type,
      code: code ?? this.code,
      description: description ?? this.description,
      isActive: isActive ?? this.isActive,
    );
  }

  @override
  String toString() {
    return 'Account(id: $id, name: $name, balance: $balance, type: $type, code: $code, description: $description, isActive: $isActive)';
  }
}
