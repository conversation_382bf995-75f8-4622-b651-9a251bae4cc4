class Attendance {
  final int id;
  final int employeeId;
  final DateTime date;
  final bool present;

  Attendance({
    required this.id,
    required this.employeeId,
    required this.date,
    required this.present,
  });

  factory Attendance.fromJson(Map<String, dynamic> json) {
    return Attendance(
      id: json['id'],
      employeeId: json['employee_id'],
      date: DateTime.parse(json['date']),
      present: json['present'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'employee_id': employeeId,
      'date': date.toIso8601String(),
      'present': present,
    };
  }

  Attendance copyWith({
    int? id,
    int? employeeId,
    DateTime? date,
    bool? present,
  }) {
    return Attendance(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      date: date ?? this.date,
      present: present ?? this.present,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Attendance &&
        other.id == id &&
        other.employeeId == employeeId &&
        other.date == date &&
        other.present == present;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        employeeId.hashCode ^
        date.hashCode ^
        present.hashCode;
  }

  @override
  String toString() {
    return 'Attendance(id: $id, employeeId: $employeeId, date: $date, present: $present)';
  }
}
