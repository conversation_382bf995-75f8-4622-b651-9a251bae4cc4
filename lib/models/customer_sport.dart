class CustomerSport {
  final int? id;
  final int? customerId;
  final int sportId; // Make sure this is non-nullable
  final String sportName;
  String sportLevel;
  String trainer;
  int trainerId; // Add trainerId field
  String trainerName; // Add trainerName field
  String subscriptionType;
  String subscriptionDuration;
  int numberOfSessions;
  double fees;
  double? additionalFees; // Additional fees
  double? taxAmount; // Tax amount (15%)
  double? totalAmount; // Total amount including taxes
  bool? uniformIncluded;
  double? uniformPrice;
  String trainingTime;
  String trainingDays;
  List<String> trainingTimes; // Add trainingTimes field
  String? startDate; // Training start date
  String? endDate; // Training end date

  CustomerSport({
    this.id,
    this.customerId,
    required this.sportId,
    required this.sportName,
    required this.sportLevel,
    required this.trainer,
    this.trainerId = 0,
    this.trainerName = '',
    required this.subscriptionType,
    required this.subscriptionDuration,
    required this.numberOfSessions,
    required this.fees,
    this.additionalFees = 0,
    this.taxAmount = 0,
    this.totalAmount = 0,
    this.uniformIncluded,
    this.uniformPrice,
    required this.trainingTime,
    required this.trainingDays,
    this.trainingTimes = const [],
    this.startDate,
    this.endDate,
  });
  
  // Add copyWith method
  CustomerSport copyWith({
    int? id,
    int? customerId,
    int? sportId,
    String? sportName,
    String? sportLevel,
    String? trainer,
    int? trainerId,
    String? trainerName,
    String? subscriptionType,
    String? subscriptionDuration,
    int? numberOfSessions,
    double? fees,
    double? additionalFees,
    double? taxAmount,
    double? totalAmount,
    bool? uniformIncluded,
    double? uniformPrice,
    String? trainingTime,
    String? trainingDays,
    List<String>? trainingTimes,
    String? startDate,
    String? endDate,
  }) {
    return CustomerSport(
      id: id ?? this.id,
      customerId: customerId ?? this.customerId,
      sportId: sportId ?? this.sportId,
      sportName: sportName ?? this.sportName,
      sportLevel: sportLevel ?? this.sportLevel,
      trainer: trainer ?? this.trainer,
      trainerId: trainerId ?? this.trainerId,
      trainerName: trainerName ?? this.trainerName,
      subscriptionType: subscriptionType ?? this.subscriptionType,
      subscriptionDuration: subscriptionDuration ?? this.subscriptionDuration,
      numberOfSessions: numberOfSessions ?? this.numberOfSessions,
      fees: fees ?? this.fees,
      additionalFees: additionalFees ?? this.additionalFees,
      taxAmount: taxAmount ?? this.taxAmount,
      totalAmount: totalAmount ?? this.totalAmount,
      uniformIncluded: uniformIncluded ?? this.uniformIncluded,
      uniformPrice: uniformPrice ?? this.uniformPrice,
      trainingTime: trainingTime ?? this.trainingTime,
      trainingDays: trainingDays ?? this.trainingDays,
      trainingTimes: trainingTimes ?? this.trainingTimes,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
    );
  }

  // Update toJson method
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'customer_id': customerId,
      'sport_id': sportId,
      'sport_name': sportName,
      'sport_level': sportLevel,
      'trainer': trainer,
      'trainer_id': trainerId,
      'trainer_name': trainerName,
      'subscription_type': subscriptionType,
      'subscription_duration': subscriptionDuration,
      'number_of_sessions': numberOfSessions,
      'amount': fees,
      'additional_fees': additionalFees,
      'tax_amount': taxAmount,
      'total_amount': totalAmount,
      'uniform_included': uniformIncluded,
      'uniform_price': uniformPrice,
      'training_time': trainingTime,
      'training_days': trainingDays,
      'training_times': trainingTimes,
      'start_date': startDate,
      'end_date': endDate
    };
  }

  // Update fromJson method
  factory CustomerSport.fromJson(Map<String, dynamic> json) {
    List<String> parseTrainingTimes(dynamic value) {
      if (value == null) return [];
      if (value is List) return value.map((item) => item.toString()).toList();
      if (value is String) {
        if (value.isEmpty) return [];
        return value.split(',').map((item) => item.trim()).toList();
      }
      return [];
    }
    
    return CustomerSport(
      id: json['id'] != null ? int.tryParse(json['id'].toString()) ?? 0 : null,
      customerId: json['customer_id'] != null ? int.tryParse(json['customer_id'].toString()) ?? 0 : null,
      sportId: json['sport_id'] != null ? int.tryParse(json['sport_id'].toString()) ?? 0 : 0,
      sportName: json['sport_name'] ?? '',
      sportLevel: json['sport_level'] ?? '',
      trainer: json['trainer'] ?? '',
      trainerId: json['trainer_id'] != null ? int.tryParse(json['trainer_id'].toString()) ?? 0 : 0,
      trainerName: json['trainer_name'] ?? '',
      subscriptionType: json['subscription_type'] ?? '',
      subscriptionDuration: json['subscription_duration'] ?? '',
      numberOfSessions: json['number_of_sessions'] != null ? int.tryParse(json['number_of_sessions'].toString()) ?? 0 : 0,
      fees: double.tryParse(json['amount'].toString()) ?? 0.0,
      uniformIncluded: json['uniform_included'] == 1 || json['uniform_included'] == '1' || json['uniform_included'] == true,
      uniformPrice: double.tryParse(json['uniform_price'].toString()) ?? 0.0,
      trainingTime: json['training_time'] ?? '',
      trainingDays: json['training_days'] ?? '',
      trainingTimes: parseTrainingTimes(json['training_times'])
    );
  }
}