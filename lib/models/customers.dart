class Customer {
  final String id;
  final String name;
  final String email;
  final String phone;
  final String address;
  final String nationalId;
  final String birthdate;
  final String gender;
  final String nationality;
  final String? parentPhone;
  final String fullAddress;
  final String healthConditions;
  final String sportActivity;
  final String sportLevel;
  final String trainer;
  final String subscriptionType;
  final String subscriptionDuration;
  final int numberOfSessions;
  final String trainingTimes;
  final String paymentMethod;
  final String? paymentNote;
  final double totalFees;
  final String howDidHearAboutUs;
  final String accountType;
  final int? numberOfChildren;
  final bool taxEnabled;
  final String status;

  Customer({
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    required this.address,
    required this.nationalId,
    required this.birthdate,
    required this.gender,
    required this.nationality,
    this.parentPhone,
    required this.fullAddress,
    required this.healthConditions,
    required this.sportActivity,
    required this.sportLevel,
    required this.trainer,
    required this.subscriptionType,
    required this.subscriptionDuration,
    required this.numberOfSessions,
    required this.trainingTimes,
    required this.paymentMethod,
    this.paymentNote,
    required this.totalFees,
    required this.howDidHearAboutUs,
    required this.accountType,
    this.numberOfChildren,
    required this.taxEnabled,
    required this.status,
  });

  factory Customer.fromJson(Map<String, dynamic> json) {
    return Customer(
      id: json['id'] as String,
      name: json['name'] as String,
      email: json['email'] as String,
      phone: json['phone'] as String,
      address: json['address'] as String,
      nationalId: json['nationalId'] as String,
      birthdate: json['birthdate'] as String,
      gender: json['gender'] as String,
      nationality: json['nationality'] as String,
      parentPhone: json['parentPhone'] as String?,
      fullAddress: json['fullAddress'] as String,
      healthConditions: json['healthConditions'] as String,
      sportActivity: json['sportActivity'] as String,
      sportLevel: json['sportLevel'] as String,
      trainer: json['trainer'] as String,
      subscriptionType: json['subscriptionType'] as String,
      subscriptionDuration: json['subscriptionDuration'] as String,
      numberOfSessions: json['numberOfSessions'] as int,
      trainingTimes: json['trainingTimes'] as String,
      paymentMethod: json['paymentMethod'] as String,
      paymentNote: json['paymentNote'] as String?,
      totalFees: json['totalFees'] as double,
      howDidHearAboutUs: json['howDidHearAboutUs'] as String,
      accountType: json['accountType'] as String,
      numberOfChildren: json['numberOfChildren'] as int?,
      taxEnabled: json['taxEnabled'] as bool,
      status: json['status'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'address': address,
      'nationalId': nationalId,
      'birthdate': birthdate,
      'gender': gender,
      'nationality': nationality,
      'parentPhone': parentPhone,
      'fullAddress': fullAddress,
      'healthConditions': healthConditions,
      'sportActivity': sportActivity,
      'sportLevel': sportLevel,
      'trainer': trainer,
      'subscriptionType': subscriptionType,
      'subscriptionDuration': subscriptionDuration,
      'numberOfSessions': numberOfSessions,
      'trainingTimes': trainingTimes,
      'paymentMethod': paymentMethod,
      'paymentNote': paymentNote,
      'totalFees': totalFees,
      'howDidHearAboutUs': howDidHearAboutUs,
      'accountType': accountType,
      'numberOfChildren': numberOfChildren,
      'taxEnabled': taxEnabled,
      'status': status,
    };
  }

  Customer copyWith({
    String? id,
    String? name,
    String? email,
    String? phone,
    String? address,
    String? nationalId,
    String? birthdate,
    String? gender,
    String? nationality,
    String? parentPhone,
    String? fullAddress,
    String? healthConditions,
    String? sportActivity,
    String? sportLevel,
    String? trainer,
    String? subscriptionType,
    String? subscriptionDuration,
    int? numberOfSessions,
    String? trainingTimes,
    String? paymentMethod,
    String? paymentNote,
    double? totalFees,
    String? howDidHearAboutUs,
    String? accountType,
    int? numberOfChildren,
    bool? taxEnabled,
    String? status,
  }) {
    return Customer(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      address: address ?? this.address,
      nationalId: nationalId ?? this.nationalId,
      birthdate: birthdate ?? this.birthdate,
      gender: gender ?? this.gender,
      nationality: nationality ?? this.nationality,
      parentPhone: parentPhone ?? this.parentPhone,
      fullAddress: fullAddress ?? this.fullAddress,
      healthConditions: healthConditions ?? this.healthConditions,
      sportActivity: sportActivity ?? this.sportActivity,
      sportLevel: sportLevel ?? this.sportLevel,
      trainer: trainer ?? this.trainer,
      subscriptionType: subscriptionType ?? this.subscriptionType,
      subscriptionDuration: subscriptionDuration ?? this.subscriptionDuration,
      numberOfSessions: numberOfSessions ?? this.numberOfSessions,
      trainingTimes: trainingTimes ?? this.trainingTimes,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      paymentNote: paymentNote ?? this.paymentNote,
      totalFees: totalFees ?? this.totalFees,
      howDidHearAboutUs: howDidHearAboutUs ?? this.howDidHearAboutUs,
      accountType: accountType ?? this.accountType,
      numberOfChildren: numberOfChildren ?? this.numberOfChildren,
      taxEnabled: taxEnabled ?? this.taxEnabled,
      status: status ?? this.status,
    );
  }

  // Add equality operator
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Customer &&
        other.id == id &&
        other.name == name &&
        other.email == email &&
        other.phone == phone &&
        other.status == status;
  }

  // Add hashCode implementation
  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        email.hashCode ^
        phone.hashCode ^
        status.hashCode;
  }

  // Add toString method
  @override
  String toString() {
    return 'Customer(id: $id, name: $name, email: $email, status: $status)';
  }

  // Add utility methods
  bool get isActive => status.toLowerCase() == 'active';

  DateTime? getBirthdate() {
    try {
      return birthdate.isNotEmpty ? DateTime.parse(birthdate) : null;
    } catch (e) {
      return null;
    }
  }

  int? getAge() {
    final birthDate = getBirthdate();
    if (birthDate == null) return null;

    final today = DateTime.now();
    int age = today.year - birthDate.year;
    if (today.month < birthDate.month ||
        (today.month == birthDate.month && today.day < birthDate.day)) {
      age--;
    }
    return age;
  }

  bool isMinor() {
    final age = getAge();
    return age != null && age < 18;
  }

  bool hasHealthConditions() =>
      healthConditions.isNotEmpty &&
      healthConditions.toLowerCase() != 'none' &&
      healthConditions.toLowerCase() != 'n/a';

  List<String> getTrainingTimesList() {
    if (trainingTimes.isEmpty) return [];
    return trainingTimes.split(',').map((time) => time.trim()).toList();
  }
}
