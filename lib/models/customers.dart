class Customer {
  final String id;
  final String name;
  final String email;
  final String phone;
  final String address;
  final String nationalId;
  final String birthdate;
  final String gender;
  final String nationality;
  final String? parentPhone;
  final String fullAddress;
  final String healthConditions;
  final String sportActivity;
  final String sportLevel;
  final String trainer;
  final String subscriptionType;
  final String subscriptionDuration;
  final int numberOfSessions;
  final String trainingTimes;
  final String paymentMethod;
  final String? paymentNote;
  final double totalFees;
  final String howDidHearAboutUs;
  final String accountType;
  final int? numberOfChildren;
  final bool taxEnabled;
  final String status;

  Customer({
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    required this.address,
    required this.nationalId,
    required this.birthdate,
    required this.gender,
    required this.nationality,
    this.parentPhone,
    required this.fullAddress,
    required this.healthConditions,
    required this.sportActivity,
    required this.sportLevel,
    required this.trainer,
    required this.subscriptionType,
    required this.subscriptionDuration,
    required this.numberOfSessions,
    required this.trainingTimes,
    required this.paymentMethod,
    this.paymentNote,
    required this.totalFees,
    required this.howDidHearAboutUs,
    required this.accountType,
    this.numberOfChildren,
    required this.taxEnabled,
    required this.status,
  });

  factory Customer.fromJson(Map<String, dynamic> json) {
    return Customer(
      id: json['id']?.toString() ?? '',
      name: json['name']?.toString() ?? '',
      email: json['email']?.toString() ?? '',
      phone: json['phone']?.toString() ?? '',
      address: json['address']?.toString() ?? '',
      // Handle both camelCase and snake_case field names
      nationalId: (json['nationalId'] ?? json['national_id'])?.toString() ?? '',
      birthdate: json['birthdate']?.toString() ?? '',
      gender: json['gender']?.toString() ?? '',
      nationality: json['nationality']?.toString() ?? '',
      parentPhone: (json['parentPhone'] ?? json['parent_phone'])?.toString(),
      fullAddress:
          (json['fullAddress'] ?? json['full_address'])?.toString() ?? '',
      healthConditions:
          (json['healthConditions'] ?? json['health_conditions'])?.toString() ??
          '',
      sportActivity:
          (json['sportActivity'] ?? json['sport_activity'])?.toString() ?? '',
      sportLevel: (json['sportLevel'] ?? json['sport_level'])?.toString() ?? '',
      trainer: json['trainer']?.toString() ?? '',
      subscriptionType:
          (json['subscriptionType'] ?? json['subscription_type'])?.toString() ??
          '',
      subscriptionDuration:
          (json['subscriptionDuration'] ?? json['subscription_duration'])
              ?.toString() ??
          '',
      numberOfSessions:
          _parseIntSafely(
            json['numberOfSessions'] ?? json['number_of_sessions'],
          ) ??
          0,
      trainingTimes:
          (json['trainingTimes'] ?? json['training_times'])?.toString() ?? '',
      paymentMethod:
          (json['paymentMethod'] ?? json['payment_method'])?.toString() ?? '',
      paymentNote: (json['paymentNote'] ?? json['payment_note'])?.toString(),
      totalFees:
          _parseDoubleSafely(json['totalFees'] ?? json['total_fees']) ?? 0.0,
      howDidHearAboutUs:
          (json['howDidHearAboutUs'] ?? json['how_did_hear_about_us'])
              ?.toString() ??
          '',
      accountType:
          (json['accountType'] ?? json['account_type'])?.toString() ?? '',
      numberOfChildren: _parseIntSafely(
        json['numberOfChildren'] ?? json['number_of_children'],
      ),
      taxEnabled:
          _parseBoolSafely(json['taxEnabled'] ?? json['tax_enabled']) ?? false,
      status: json['status']?.toString() ?? 'active',
    );
  }

  // Helper methods for safe parsing
  static int? _parseIntSafely(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is String) return int.tryParse(value);
    return null;
  }

  static double? _parseDoubleSafely(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value);
    return null;
  }

  static bool? _parseBoolSafely(dynamic value) {
    if (value == null) return null;
    if (value is bool) return value;
    if (value is int) return value == 1;
    if (value is String) {
      final lower = value.toLowerCase();
      return lower == 'true' || lower == '1' || lower == 'yes';
    }
    return null;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'address': address,
      'nationalId': nationalId,
      'birthdate': birthdate,
      'gender': gender,
      'nationality': nationality,
      'parentPhone': parentPhone,
      'fullAddress': fullAddress,
      'healthConditions': healthConditions,
      'sportActivity': sportActivity,
      'sportLevel': sportLevel,
      'trainer': trainer,
      'subscriptionType': subscriptionType,
      'subscriptionDuration': subscriptionDuration,
      'numberOfSessions': numberOfSessions,
      'trainingTimes': trainingTimes,
      'paymentMethod': paymentMethod,
      'paymentNote': paymentNote,
      'totalFees': totalFees,
      'howDidHearAboutUs': howDidHearAboutUs,
      'accountType': accountType,
      'numberOfChildren': numberOfChildren,
      'taxEnabled': taxEnabled,
      'status': status,
    };
  }

  Customer copyWith({
    String? id,
    String? name,
    String? email,
    String? phone,
    String? address,
    String? nationalId,
    String? birthdate,
    String? gender,
    String? nationality,
    String? parentPhone,
    String? fullAddress,
    String? healthConditions,
    String? sportActivity,
    String? sportLevel,
    String? trainer,
    String? subscriptionType,
    String? subscriptionDuration,
    int? numberOfSessions,
    String? trainingTimes,
    String? paymentMethod,
    String? paymentNote,
    double? totalFees,
    String? howDidHearAboutUs,
    String? accountType,
    int? numberOfChildren,
    bool? taxEnabled,
    String? status,
  }) {
    return Customer(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      address: address ?? this.address,
      nationalId: nationalId ?? this.nationalId,
      birthdate: birthdate ?? this.birthdate,
      gender: gender ?? this.gender,
      nationality: nationality ?? this.nationality,
      parentPhone: parentPhone ?? this.parentPhone,
      fullAddress: fullAddress ?? this.fullAddress,
      healthConditions: healthConditions ?? this.healthConditions,
      sportActivity: sportActivity ?? this.sportActivity,
      sportLevel: sportLevel ?? this.sportLevel,
      trainer: trainer ?? this.trainer,
      subscriptionType: subscriptionType ?? this.subscriptionType,
      subscriptionDuration: subscriptionDuration ?? this.subscriptionDuration,
      numberOfSessions: numberOfSessions ?? this.numberOfSessions,
      trainingTimes: trainingTimes ?? this.trainingTimes,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      paymentNote: paymentNote ?? this.paymentNote,
      totalFees: totalFees ?? this.totalFees,
      howDidHearAboutUs: howDidHearAboutUs ?? this.howDidHearAboutUs,
      accountType: accountType ?? this.accountType,
      numberOfChildren: numberOfChildren ?? this.numberOfChildren,
      taxEnabled: taxEnabled ?? this.taxEnabled,
      status: status ?? this.status,
    );
  }

  // Add equality operator
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Customer &&
        other.id == id &&
        other.name == name &&
        other.email == email &&
        other.phone == phone &&
        other.status == status;
  }

  // Add hashCode implementation
  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        email.hashCode ^
        phone.hashCode ^
        status.hashCode;
  }

  // Add toString method
  @override
  String toString() {
    return 'Customer(id: $id, name: $name, email: $email, status: $status)';
  }

  // Add utility methods
  bool get isActive => status.toLowerCase() == 'active';

  DateTime? getBirthdate() {
    try {
      return birthdate.isNotEmpty ? DateTime.parse(birthdate) : null;
    } catch (e) {
      return null;
    }
  }

  int? getAge() {
    final birthDate = getBirthdate();
    if (birthDate == null) return null;

    final today = DateTime.now();
    int age = today.year - birthDate.year;
    if (today.month < birthDate.month ||
        (today.month == birthDate.month && today.day < birthDate.day)) {
      age--;
    }
    return age;
  }

  bool isMinor() {
    final age = getAge();
    return age != null && age < 18;
  }

  bool hasHealthConditions() =>
      healthConditions.isNotEmpty &&
      healthConditions.toLowerCase() != 'none' &&
      healthConditions.toLowerCase() != 'n/a';

  List<String> getTrainingTimesList() {
    if (trainingTimes.isEmpty) return [];
    return trainingTimes.split(',').map((time) => time.trim()).toList();
  }
}
