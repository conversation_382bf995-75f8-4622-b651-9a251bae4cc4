// models/employee.dart

// ignore_for_file: hash_and_equals

class Employee {
  final int id;
  final String name;
  final String position;
  final String department;
  final String contactInfo;
  final String employeeCode;
  final String jobTitle;
  final String emailAddress;
  final String idNumber;
  final String idExpiryDate;
  final String passportNumber;
  final String passportExpiryDate;
  final String gender;
  final String dateOfBirth;
  final String mobileNumber;
  final int children;
  final String city;
  final String street;
  final String university;
  final String college;
  final String major;
  final int graduationYear;
  final String gpaGrade;
  final String contractType;
  final String startDate;
  final String contractDate;
  final String probationEndDate;
  final String contractEndDate;
  final String salaryAmount;
  final String accountNumber;
  final String ibanNumber;
  final String notes;

  // Constructor with named parameters only
  const Employee({
    this.id = 0,
    this.name = '',
    this.position = '',
    this.department = '',
    this.contactInfo = '',
    this.employeeCode = '',
    this.jobTitle = '',
    this.emailAddress = '',
    this.idNumber = '',
    this.idExpiryDate = '',
    this.passportNumber = '',
    this.passportExpiryDate = '',
    this.gender = '',
    this.dateOfBirth = '',
    this.mobileNumber = '',
    this.children = 0,
    this.city = '',
    this.street = '',
    this.university = '',
    this.college = '',
    this.major = '',
    this.graduationYear = 0,
    this.gpaGrade = '',
    this.contractType = '',
    this.startDate = '',
    this.contractDate = '',
    this.probationEndDate = '',
    this.contractEndDate = '',
    this.salaryAmount = '',
    this.accountNumber = '',
    this.ibanNumber = '',
    this.notes = '',
  });

  factory Employee.fromJson(Map<String, dynamic> json) {
    return Employee(
      id: json['id'] is int ? json['id'] : int.tryParse(json['id']?.toString() ?? '0') ?? 0,
      name: json['name']?.toString() ?? '',
      position: json['position']?.toString() ?? '',
      department: json['department']?.toString() ?? '',
      contactInfo: json['contact_info']?.toString() ?? '',
      employeeCode: json['employee_code']?.toString() ?? '',
      jobTitle: json['job_title']?.toString() ?? '',
      emailAddress: json['email_address']?.toString() ?? '',
      idNumber: json['id_number']?.toString() ?? '',
      idExpiryDate: json['id_expiry_date']?.toString() ?? '',
      passportNumber: json['passport_number']?.toString() ?? '',
      passportExpiryDate: json['passport_expiry_date']?.toString() ?? '',
      gender: json['gender']?.toString() ?? '',
      dateOfBirth: json['date_of_birth']?.toString() ?? '',
      mobileNumber: json['mobile_number']?.toString() ?? '',
      children: json['children'] is int ? json['children'] : int.tryParse(json['children']?.toString() ?? '0') ?? 0,
      city: json['city']?.toString() ?? '',
      street: json['street']?.toString() ?? '',
      university: json['university']?.toString() ?? '',
      college: json['college']?.toString() ?? '',
      major: json['major']?.toString() ?? '',
      graduationYear: json['graduation_year'] is int ? json['graduation_year'] : int.tryParse(json['graduation_year']?.toString() ?? '0') ?? 0,
      gpaGrade: json['gpa_grade']?.toString() ?? '',
      contractType: json['contract_type']?.toString() ?? '',
      startDate: json['start_date']?.toString() ?? '',
      contractDate: json['contract_date']?.toString() ?? '',
      probationEndDate: json['probation_end_date']?.toString() ?? '',
      contractEndDate: json['contract_end_date']?.toString() ?? '',
      salaryAmount: json['salary_amount']?.toString() ?? '',
      accountNumber: json['account_number']?.toString() ?? '',
      ibanNumber: json['iban_number']?.toString() ?? '',
      notes: json['notes']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'position': position,
      'department': department,
      'contact_info': contactInfo,
      'employee_code': employeeCode,
      'job_title': jobTitle,
      'email_address': emailAddress,
      'id_number': idNumber,
      'id_expiry_date': idExpiryDate,
      'passport_number': passportNumber,
      'passport_expiry_date': passportExpiryDate,
      'gender': gender,
      'date_of_birth': dateOfBirth,
      'mobile_number': mobileNumber,
      'children': children,
      'city': city,
      'street': street,
      'university': university,
      'college': college,
      'major': major,
      'graduation_year': graduationYear,
      'gpa_grade': gpaGrade,
      'contract_type': contractType,
      'start_date': startDate,
      'contract_date': contractDate,
      'probation_end_date': probationEndDate,
      'contract_end_date': contractEndDate,
      'salary_amount': salaryAmount,
      'account_number': accountNumber,
      'iban_number': ibanNumber,
      'notes': notes,
    };
  }
  
  // Create a copy of this Employee with optional new values
  Employee copyWith({
    int? id,
    String? name,
    String? position,
    String? department,
    String? contactInfo,
    String? employeeCode,
    String? jobTitle,
    String? emailAddress,
    String? idNumber,
    String? idExpiryDate,
    String? passportNumber,
    String? passportExpiryDate,
    String? gender,
    String? dateOfBirth,
    String? mobileNumber,
    int? children,
    String? city,
    String? street,
    String? university,
    String? college,
    String? major,
    int? graduationYear,
    String? gpaGrade,
    String? contractType,
    String? startDate,
    String? contractDate,
    String? probationEndDate,
    String? contractEndDate,
    String? salaryAmount,
    String? accountNumber,
    String? ibanNumber,
    String? notes,
  }) {
    return Employee(
      id: id ?? this.id,
      name: name ?? this.name,
      position: position ?? this.position,
      department: department ?? this.department,
      contactInfo: contactInfo ?? this.contactInfo,
      employeeCode: employeeCode ?? this.employeeCode,
      jobTitle: jobTitle ?? this.jobTitle,
      emailAddress: emailAddress ?? this.emailAddress,
      idNumber: idNumber ?? this.idNumber,
      idExpiryDate: idExpiryDate ?? this.idExpiryDate,
      passportNumber: passportNumber ?? this.passportNumber,
      passportExpiryDate: passportExpiryDate ?? this.passportExpiryDate,
      gender: gender ?? this.gender,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      mobileNumber: mobileNumber ?? this.mobileNumber,
      children: children ?? this.children,
      city: city ?? this.city,
      street: street ?? this.street,
      university: university ?? this.university,
      college: college ?? this.college,
      major: major ?? this.major,
      graduationYear: graduationYear ?? this.graduationYear,
      gpaGrade: gpaGrade ?? this.gpaGrade,
      contractType: contractType ?? this.contractType,
      startDate: startDate ?? this.startDate,
      contractDate: contractDate ?? this.contractDate,
      probationEndDate: probationEndDate ?? this.probationEndDate,
      contractEndDate: contractEndDate ?? this.contractEndDate,
      salaryAmount: salaryAmount ?? this.salaryAmount,
      accountNumber: accountNumber ?? this.accountNumber,
      ibanNumber: ibanNumber ?? this.ibanNumber,
      notes: notes ?? this.notes,
    );
  }

  // Override equality operator
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Employee &&
        other.id == id &&
        other.name == name &&
        other.position == position &&
        other.department == department &&
        other.contactInfo == contactInfo &&
        other.employeeCode == employeeCode &&
        other.jobTitle == jobTitle &&
        other.emailAddress == emailAddress &&
        other.idNumber == idNumber &&
        other.idExpiryDate == idExpiryDate &&
        other.passportNumber == passportNumber &&
        other.passportExpiryDate == passportExpiryDate &&
        other.gender == gender &&
        other.dateOfBirth == dateOfBirth &&
        other.mobileNumber == mobileNumber &&
        other.children == children &&
        other.city == city &&
        other.street == street &&
        other.university == university &&
        other.college == college &&
        other.major == major &&
        other.graduationYear == graduationYear &&
        other.gpaGrade == gpaGrade &&
        other.contractType == contractType &&
        other.startDate == startDate &&
        other.contractDate == contractDate &&
        other.probationEndDate == probationEndDate &&
        other.contractEndDate == contractEndDate &&
        other.salaryAmount == salaryAmount &&
        other.accountNumber == accountNumber &&
        other.ibanNumber == ibanNumber &&
        other.notes == notes;
  }

  // hashCode method has been removed

  // String representation for debugging
  @override
  String toString() {
    return 'Employee(id: $id, name: $name, position: $position, department: $department)';
  }

  // Utility methods
  bool get isActive {
    if (contractEndDate.isEmpty) return true;
    
    try {
      final endDate = DateTime.parse(contractEndDate);
      final now = DateTime.now();
      return endDate.isAfter(now);
    } catch (e) {
      return true; // If date parsing fails, assume active
    }
  }

  bool get isProbationPeriod {
    if (probationEndDate.isEmpty) return false;
    
    try {
      final probationEnd = DateTime.parse(probationEndDate);
      final now = DateTime.now();
      return now.isBefore(probationEnd);
    } catch (e) {
      return false;
    }
  }

  String get fullAddress {
    final parts = [street, city].where((part) => part.isNotEmpty).toList();
    return parts.join(', ');
  }

  String get educationSummary {
    if (university.isEmpty) return '';
    
    final parts = [major, college, university, 'Class of $graduationYear']
        .where((part) => part.isNotEmpty && part != 'Class of 0')
        .toList();
    
    return parts.join(', ');
  }

  // Validation methods
  bool get hasValidEmail {
    if (emailAddress.isEmpty) return false;
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    return emailRegex.hasMatch(emailAddress);
  }

  bool get hasValidMobileNumber {
    if (mobileNumber.isEmpty) return false;
    final phoneRegex = RegExp(r'^\+?[0-9]{8,15}$');
    return phoneRegex.hasMatch(mobileNumber);
  }

  bool get hasRequiredFields {
    return name.isNotEmpty && 
           employeeCode.isNotEmpty && 
           position.isNotEmpty && 
           department.isNotEmpty;
  }

  // Factory constructor for creating an empty employee
  factory Employee.empty() {
    return Employee();
  }
}
