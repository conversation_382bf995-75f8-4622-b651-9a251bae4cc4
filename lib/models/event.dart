class Event {
  final int id;
  final String title;
  final DateTime startDate;
  final DateTime endDate;
  final String location;
  final List<int> participantIds;
  final int maxParticipants;
  final String status;
  final String description;

  Event({
    required this.id,
    required this.title,
    required this.startDate,
    required this.endDate,
    required this.location,
    required this.participantIds,
    required this.maxParticipants,
    required this.status,
    required this.description,
  });

  factory Event.fromJson(Map<String, dynamic> json) => Event(
    id: json['id'] is int ? json['id'] : int.tryParse(json['id'].toString()) ?? 0,
    title: json['name'] ?? '',
    startDate: DateTime.parse(json['date']),
    endDate: json['end_date'] != null && json['end_date'].toString().isNotEmpty
        ? DateTime.parse(json['end_date'])
        : DateTime.parse(json['date']),
    location: json['location'] ?? '',
    participantIds: (json['participant_ids'] is List)
        ? (json['participant_ids'] as List)
            .map((e) => e is int ? e : int.tryParse(e.toString()) ?? 0)
            .toList()
        : <int>[],
    maxParticipants: json['max_participants'] is int
        ? json['max_participants']
        : int.tryParse(json['max_participants']?.toString() ?? '') ?? 0,
    status: json['status'] ?? '',
    description: json['description'] ?? '',
  );

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'start_date': startDate.toIso8601String(),
      'end_date': endDate.toIso8601String(),
      'location': location,
      'participant_ids': participantIds,
      'max_participants': maxParticipants,
      'status': status,
      'description': description,
    };
  }
  
  // Add copyWith method
  Event copyWith({
    int? id,
    String? title,
    DateTime? startDate,
    DateTime? endDate,
    String? location,
    List<int>? participantIds,
    int? maxParticipants,
    String? status,
    String? description,
  }) {
    return Event(
      id: id ?? this.id,
      title: title ?? this.title,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      location: location ?? this.location,
      participantIds: participantIds ?? this.participantIds,
      maxParticipants: maxParticipants ?? this.maxParticipants,
      status: status ?? this.status,
      description: description ?? this.description,
    );
  }
  
  // Add equality operator
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Event &&
        other.id == id &&
        other.title == title &&
        other.startDate == startDate &&
        other.endDate == endDate &&
        other.location == location &&
        listEquals(other.participantIds, participantIds) &&
        other.maxParticipants == maxParticipants &&
        other.status == status &&
        other.description == description;
  }
  
  // Add hashCode implementation
  @override
  int get hashCode {
    return id.hashCode ^
        title.hashCode ^
        startDate.hashCode ^
        endDate.hashCode ^
        location.hashCode ^
        participantIds.hashCode ^
        maxParticipants.hashCode ^
        status.hashCode ^
        description.hashCode;
  }
  
  // Add toString method
  @override
  String toString() {
    return 'Event(id: $id, title: $title, startDate: $startDate, endDate: $endDate, location: $location, participantIds: $participantIds, maxParticipants: $maxParticipants, status: $status)';
  }
  
  // Add utility methods
  bool get isUpcoming => startDate.isAfter(DateTime.now());
  bool get isPast => endDate.isBefore(DateTime.now());
  bool get isOngoing => startDate.isBefore(DateTime.now()) && endDate.isAfter(DateTime.now());
  bool get isFull => participantIds.length >= maxParticipants;
  int get availableSpots => maxParticipants - participantIds.length;
  Duration get duration => endDate.difference(startDate);
  
  // Add import at the top of the file
  static bool listEquals<T>(List<T>? a, List<T>? b) {
    if (a == null) return b == null;
    if (b == null || a.length != b.length) return false;
    for (int i = 0; i < a.length; i++) {
      if (a[i] != b[i]) return false;
    }
    return true;
  }
}
