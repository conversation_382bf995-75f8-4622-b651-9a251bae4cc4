class PromoCodeValidation {
  final String code;
  final String? id;
  final String? description;
  final String? discountType;
  final double? discountValue;
  final bool isValid;
  final String? message;

  PromoCodeValidation({
    required this.code,
    this.id,
    this.description,
    this.discountType,
    this.discountValue,
    required this.isValid,
    this.message,
  });

  factory PromoCodeValidation.fromJson(Map<String, dynamic> json) {
    // Determine validity: use is_valid if present, otherwise use is_active
    bool valid = false;
    if (json.containsKey('is_valid')) {
      valid = json['is_valid'] == true || json['is_valid'] == 1 || json['is_valid']?.toString() == '1';
    } else if (json.containsKey('is_active')) {
      valid = json['is_active'] == true || json['is_active'] == 1 || json['is_active']?.toString() == '1';
    }
    return PromoCodeValidation(
      code: json['code'] ?? '',
      id: json['id'],
      description: json['description'],
      discountType: json['discount_type'],
      discountValue: json['discount_value'] != null
          ? double.parse(json['discount_value'].toString())
          : null,
      isValid: valid,
      message: json['message'],
    );
  }

  factory PromoCodeValidation.invalid(String code, String message) {
    return PromoCodeValidation(
      code: code,
      isValid: false,
      message: message,
    );
  }

  // Calculate discount amount based on original price
  double calculateDiscount(double originalAmount) {
    if (!isValid || discountValue == null || discountType == null) {
      return 0.0;
    }

    if (discountType == 'percentage') {
      return originalAmount * (discountValue! / 100);
    } else {
      // Fixed amount discount
      return discountValue! > originalAmount ? originalAmount : discountValue!;
    }
  }
}