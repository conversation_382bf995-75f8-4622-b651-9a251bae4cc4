class Report {
  final String id;
  final String title;
  final String type;
  final DateTime generatedDate;
  final DateTime? periodStart;
  final DateTime? periodEnd;

  Report({
    required this.id,
    required this.title,
    required this.type,
    required this.generatedDate,
    this.periodStart,
    this.periodEnd,
  });

  factory Report.fromJson(Map<String, dynamic> json) {
    return Report(
      id: json['id'] as String,
      title: json['title'] as String,
      type: json['type'] as String,
      generatedDate: DateTime.parse(json['generatedDate'] as String),
      periodStart: json['periodStart'] != null ? DateTime.parse(json['periodStart'] as String) : null,
      periodEnd: json['periodEnd'] != null ? DateTime.parse(json['periodEnd'] as String) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'type': type,
      'generatedDate': generatedDate.toIso8601String(),
      'periodStart': periodStart?.toIso8601String(),
      'periodEnd': periodEnd?.toIso8601String(),
    };
  }
  
  // Add copyWith method
  Report copyWith({
    String? id,
    String? title,
    String? type,
    DateTime? generatedDate,
    DateTime? periodStart,
    DateTime? periodEnd,
  }) {
    return Report(
      id: id ?? this.id,
      title: title ?? this.title,
      type: type ?? this.type,
      generatedDate: generatedDate ?? this.generatedDate,
      periodStart: periodStart ?? this.periodStart,
      periodEnd: periodEnd ?? this.periodEnd,
    );
  }
  
  // Add equality operator
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Report &&
        other.id == id &&
        other.title == title &&
        other.type == type &&
        other.generatedDate == generatedDate &&
        other.periodStart == periodStart &&
        other.periodEnd == periodEnd;
  }
  
  // Add hashCode implementation
  @override
  int get hashCode {
    return id.hashCode ^
        title.hashCode ^
        type.hashCode ^
        generatedDate.hashCode ^
        periodStart.hashCode ^
        periodEnd.hashCode;
  }
  
  // Add toString method
  @override
  String toString() {
    return 'Report(id: $id, title: $title, type: $type, generatedDate: $generatedDate)';
  }
  
  // Add utility methods
  bool get hasPeriod => periodStart != null && periodEnd != null;
  
  Duration? get periodDuration {
    if (!hasPeriod) return null;
    return periodEnd!.difference(periodStart!);
  }
  
  int? get periodDays {
    if (!hasPeriod) return null;
    return periodEnd!.difference(periodStart!).inDays;
  }
  
  String get formattedGeneratedDate {
    return '${generatedDate.day}/${generatedDate.month}/${generatedDate.year}';
  }
  
  String? get formattedPeriod {
    if (!hasPeriod) return null;
    return '${periodStart!.day}/${periodStart!.month}/${periodStart!.year} - ${periodEnd!.day}/${periodEnd!.month}/${periodEnd!.year}';
  }
  
  bool isFinancialReport() => type.toLowerCase().contains('financial');
  bool isAttendanceReport() => type.toLowerCase().contains('attendance');
  bool isPerformanceReport() => type.toLowerCase().contains('performance');
}
