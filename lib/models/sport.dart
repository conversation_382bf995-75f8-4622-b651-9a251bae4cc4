
// ignore_for_file: non_constant_identifier_names

class Sport {
  final int? id;
  final String name;
  final String trainers;
  final double fees;
  final int number_of_sessions;
  final String training_time;
  final String training_days; // Add this field
  final DateTime? created_at;

  Sport({
    this.id,
    required this.name,
    required this.trainers,
    required this.fees,
    required this.number_of_sessions,
    required this.training_time,
    required this.training_days, // Add this parameter
    this.created_at,
  });

  // Add copyWith method
  Sport copyWith({
    int? id,
    String? name,
    String? trainers,
    double? fees,
    int? number_of_sessions,
    String? training_time,
    String? training_days, // Add this parameter
    DateTime? created_at,
  }) {
    return Sport(
      id: id ?? this.id,
      name: name ?? this.name,
      trainers: trainers ?? this.trainers,
      fees: fees ?? this.fees,
      number_of_sessions: number_of_sessions ?? this.number_of_sessions,
      training_time: training_time ?? this.training_time,
      training_days: training_days?? this.training_days, // Add this parameter
      created_at: created_at ?? this.created_at,

    );
  }

  factory Sport.fromJson(Map<String, dynamic> json) {
    return Sport(
      id: json['id'] != null ? int.parse(json['id'].toString()) : null,
      name: json['name'] ?? '',
      trainers: json['trainers'] ?? '',
      fees: json['fees'] != null ? double.parse(json['fees'].toString()) : 0.0,
      number_of_sessions: json['number_of_sessions'] != null ? int.parse(json['number_of_sessions'].toString()) : 0,
      training_time: json['training_time'] ?? '',
      training_days: json['training_days']?? '',
      created_at: json['created_at'] != null ? DateTime.parse(json['created_at']) : null,
    );
  }

  Map<String, String> toJson() {
    return {
      if (id != null) 'id': id.toString(),
      'name': name,
      'trainers': trainers,
      'fees': fees.toString(),
      'number_of_sessions': number_of_sessions.toString(),
      'training_time': training_time,
    };
  }
}