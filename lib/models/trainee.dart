class Trainee {
  final String id;
  final String name;
  final String trainerId;
  final String trainingProgram;
  final String skillLevel;
  final String joinDate;
  final String? phoneNumber;
  final String? email;
  final String? address;
  final String? notes;
  final bool isActive;

  Trainee({
    required this.id,
    required this.name,
    required this.trainerId,
    required this.trainingProgram,
    required this.skillLevel,
    required this.joinDate,
    this.phoneNumber,
    this.email,
    this.address,
    this.notes,
    this.isActive = true,
  });

  factory Trainee.fromJson(Map<String, dynamic> json) {
    return Trainee(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      trainerId: json['trainer_id'] ?? '',
      trainingProgram: json['training_program'] ?? '',
      skillLevel: json['skill_level'] ?? 'Beginner',
      joinDate: json['join_date'] ?? '',
      phoneNumber: json['phone_number'],
      email: json['email'],
      address: json['address'],
      notes: json['notes'],
      isActive: json['is_active'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'trainer_id': trainerId,
      'training_program': trainingProgram,
      'skill_level': skillLevel,
      'join_date': joinDate,
      'phone_number': phoneNumber,
      'email': email,
      'address': address,
      'notes': notes,
      'is_active': isActive,
    };
  }

  Trainee copyWith({
    String? id,
    String? name,
    String? trainerId,
    String? trainingProgram,
    String? skillLevel,
    String? joinDate,
    String? phoneNumber,
    String? email,
    String? address,
    String? notes,
    bool? isActive,
  }) {
    return Trainee(
      id: id ?? this.id,
      name: name ?? this.name,
      trainerId: trainerId ?? this.trainerId,
      trainingProgram: trainingProgram ?? this.trainingProgram,
      skillLevel: skillLevel ?? this.skillLevel,
      joinDate: joinDate ?? this.joinDate,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      email: email ?? this.email,
      address: address ?? this.address,
      notes: notes ?? this.notes,
      isActive: isActive ?? this.isActive,
    );
  }
}
