class Trainer {
  final int? id;
  final int sportId;
  final String name;
  final List<String> trainingDays;

  Trainer({
    this.id,
    required this.sportId,
    required this.name,
    required this.trainingDays,
  });

  factory Trainer.fromJson(Map<String, dynamic> json) {
    List<String> parseDaysList(String daysString) {
      if (daysString.isEmpty) return [];
      return daysString.split(',').map((day) => day.trim()).toList();
    }

    return Trainer(
      id: int.tryParse(json['id'].toString()),
      sportId: int.tryParse(json['sport_id'].toString()) ?? 0,
      name: json['name'] ?? '',
      trainingDays: parseDaysList(json['training_days'] ?? ''),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id.toString(),
      'sport_id': sportId.toString(),
      'name': name,
      'training_days': trainingDays.join(', '),
    };
  }

  Trainer copyWith({
    int? id,
    int? sportId,
    String? name,
    List<String>? trainingDays,
  }) {
    return Trainer(
      id: id ?? this.id,
      sportId: sportId ?? this.sportId,
      name: name ?? this.name,
      trainingDays: trainingDays ?? this.trainingDays,
    );
  }
}