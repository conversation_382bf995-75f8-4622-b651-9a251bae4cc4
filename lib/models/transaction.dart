class Transaction {
  final String id;
  final DateTime date;
  final double amount;
  final String description;
  final String category;
  final String accountId;
  final bool isExpense;

  // Add type getter and setter
  String get type => isExpense ? 'expense' : 'income';

  set type(String value) {
    // This setter is only meaningful if you want to mutate isExpense after construction.
    // If you want to allow changing type, you need to make isExpense non-final.
    // Otherwise, you can remove the setter or throw an error if someone tries to set it.
    throw UnimplementedError('Setting type is not supported. Use isExpense instead.');
  }

  Transaction({
    required this.id,
    required this.date,
    required this.amount,
    required this.description,
    required this.category,
    required this.accountId,
    this.isExpense = true,
  });

  factory Transaction.fromJson(Map<String, dynamic> json) {
    return Transaction(
      id: json['id'],
      date: DateTime.parse(json['date']),
      amount: json['amount'].toDouble(),
      description: json['description'],
      category: json['category'],
      accountId: json['account_id'],
      isExpense: json['is_expense'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'date': date.toIso8601String(),
      'amount': amount,
      'description': description,
      'category': category,
      'account_id': accountId,
      'is_expense': isExpense,
    };
  }

  Transaction copyWith({
    String? id,
    DateTime? date,
    double? amount,
    String? description,
    String? category,
    String? accountId,
    bool? isExpense,
  }) {
    return Transaction(
      id: id ?? this.id,
      date: date ?? this.date,
      amount: amount ?? this.amount,
      description: description ?? this.description,
      category: category ?? this.category,
      accountId: accountId ?? this.accountId,
      isExpense: isExpense ?? this.isExpense,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Transaction &&
        other.id == id &&
        other.date == date &&
        other.amount == amount &&
        other.description == description &&
        other.category == category &&
        other.accountId == accountId &&
        other.isExpense == isExpense;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        date.hashCode ^
        amount.hashCode ^
        description.hashCode ^
        category.hashCode ^
        accountId.hashCode ^
        isExpense.hashCode;
  }

  @override
  String toString() {
    return 'Transaction(id: $id, date: $date, amount: $amount, description: $description, category: $category, accountId: $accountId, isExpense: $isExpense)';
  }
}
