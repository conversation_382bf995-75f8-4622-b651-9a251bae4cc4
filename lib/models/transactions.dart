// ignore_for_file: prefer_is_not_operator

import 'package:flutter/material.dart';

class Transaction {
  final int id;
  final String date;
  final double amount;
  final String type;
  final int customerId;
  final int employeeId;

  // Add missing getters
  String get description => 'Transaction #$id';
  String get status => 'pending'; // Default status
  String get customerName => 'Customer #$customerId'; // Default customer name

  Transaction({
    required this.id,
    required this.date,
    required this.amount,
    required this.type,
    required this.customerId,
    required this.employeeId,
  });

  factory Transaction.fromJson(Map<String, dynamic> json) {
    // Universal safe number parser
    int safeInt(dynamic value) {
      if (value == null) return 0;
      if (value is int) return value;
      if (value is double) return value.toInt();
      if (value is String) {
        return int.tryParse(value) ?? double.tryParse(value)?.toInt() ?? 0;
      }
      return 0;
    }

    // Universal safe double parser
    double safeDouble(dynamic value) {
      if (value == null) return 0.0;
      if (value is double) return value;
      if (value is int) return value.toDouble();
      if (value is String) return double.tryParse(value) ?? 0.0;
      return 0.0;
    }

    // Universal safe string parser
    String safeString(dynamic value) {
      return (value?.toString() ?? '').trim(); // Trim whitespace and newlines
    }

    return Transaction(
      id: safeInt(json['id']),
      date: safeString(json['date']),
      amount: safeDouble(json['amount']),
      type: safeString(json['type']),
      customerId: safeInt(json['customer_id']),
      employeeId: safeInt(json['employee_id']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'date': date,
      'amount': amount,
      'type': type,
      'customer_id': customerId,
      'employee_id': employeeId,
    };
  }
}

List<Transaction> parseTransactionsResponse(Map<String, dynamic> json) {
  try {
    final data = json['data'];
    if (data == null || !(data is List)) {
      debugPrint('Invalid data structure: Expected a list, got ${data.runtimeType}');
      return [];
    }

    return data.map((item) {
      try {
        return Transaction.fromJson(item);
      } catch (e) {
        debugPrint('Error parsing transaction: $e, item: $item');
        return null;
      }
    }).whereType<Transaction>().toList();
  } catch (e) {
    debugPrint('Transaction parsing error: $e');
    return [];
  }
}

int getRecordsCount(Map<String, dynamic> json) {
  try {
    final records = json['records'];
    debugPrint('Records field type: ${records.runtimeType}, value: $records');

    if (records == null) return 0;

    if (records is int) {
      return records; // Already an integer
    } else if (records is String) {
      return int.tryParse(records.trim()) ?? 0; // Parse string to integer
    } else if (records is double) {
      return records.toInt(); // Convert double to integer
    }

    return 0; // Default fallback
  } catch (e) {
    debugPrint('Records count error: $e');
    return 0;
  }
}