class Treasury {
  final String id;
  final String name;
  final String description;
  final String currency;
  final double balance;
  final DateTime lastUpdated;
  final bool isPrimary;

  Treasury({
    required this.id,
    required this.name,
    required this.description,
    required this.currency,
    required this.balance,
    required this.lastUpdated,
    required this.isPrimary,
  });

  factory Treasury.fromJson(Map<String, dynamic> json) {
    return Treasury(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      currency: json['currency'] as String,
      balance: (json['balance'] as num).toDouble(),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
      isPrimary: json['isPrimary'] as bool,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'currency': currency,
      'balance': balance,
      'lastUpdated': lastUpdated.toIso8601String(),
      'isPrimary': isPrimary,
    };
  }
  
  // Add copyWith method
  Treasury copyWith({
    String? id,
    String? name,
    String? description,
    String? currency,
    double? balance,
    DateTime? lastUpdated,
    bool? isPrimary,
  }) {
    return Treasury(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      currency: currency ?? this.currency,
      balance: balance ?? this.balance,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      isPrimary: isPrimary ?? this.isPrimary,
    );
  }
  
  // Add equality operator
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Treasury &&
        other.id == id &&
        other.name == name &&
        other.description == description &&
        other.currency == currency &&
        other.balance == balance &&
        other.lastUpdated == lastUpdated &&
        other.isPrimary == isPrimary;
  }
  
  // Add hashCode implementation
  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        description.hashCode ^
        currency.hashCode ^
        balance.hashCode ^
        lastUpdated.hashCode ^
        isPrimary.hashCode;
  }
  
  // Add toString method
  @override
  String toString() {
    return 'Treasury(id: $id, name: $name, currency: $currency, balance: $balance, isPrimary: $isPrimary)';
  }
  
  // Add utility methods
  String formattedBalance() {
    return '$currency ${balance.toStringAsFixed(2)}';
  }
  
  Treasury deposit(double amount) {
    if (amount <= 0) throw ArgumentError('Deposit amount must be positive');
    return copyWith(
      balance: balance + amount,
      lastUpdated: DateTime.now(),
    );
  }
  
  Treasury withdraw(double amount) {
    if (amount <= 0) throw ArgumentError('Withdrawal amount must be positive');
    if (amount > balance) throw ArgumentError('Insufficient funds');
    return copyWith(
      balance: balance - amount,
      lastUpdated: DateTime.now(),
    );
  }
}
