class User {
  final String id;
  final String username;
  final String email;
  // Removed token field
  final String role;
  final DateTime lastLogin;

  User({
    this.id = '',
    this.username = '',
    this.email = '',
    // Removed token parameter
    this.role = 'user',
    DateTime? lastLogin,
  }) : lastLogin = lastLogin ?? DateTime.now();

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id']?.toString() ?? '',
      username: json['username']?.toString() ?? '',
      email: json['email']?.toString() ?? '',
      // Removed token assignment
      role: json['role']?.toString() ?? 'user',
      lastLogin: json['lastLogin'] != null 
          ? DateTime.parse(json['lastLogin']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'email': email,
      // Removed token field
      'role': role,
      'lastLogin': lastLogin.toIso8601String(),
    };
  }

  User copyWith({
    String? id,
    String? username,
    String? email,
    // Removed token parameter
    String? role,
    DateTime? lastLogin,
  }) {
    return User(
      id: id ?? this.id,
      username: username ?? this.username,
      email: email ?? this.email,
      // Removed token assignment
      role: role ?? this.role,
      lastLogin: lastLogin ?? this.lastLogin,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is User &&
        other.id == id &&
        other.username == username &&
        other.email == email &&
        other.role == role;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        username.hashCode ^
        email.hashCode ^
        role.hashCode;
  }

  @override
  String toString() {
    return 'User(id: $id, username: $username, email: $email, role: $role)';
  }

  bool get isAdmin => role.toLowerCase() == 'admin';
  bool get isManager => role.toLowerCase() == 'manager';
  bool hasPermission(String permission) {
    // Implement permission checking logic here
    if (isAdmin) return true;
    // Add more permission logic as needed
    return false;
  }
}