// ignore_for_file: library_private_types_in_public_api, use_build_context_synchronously, prefer_final_fields, deprecated_member_use, unnecessary_null_comparison, unused_element

import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:csv/csv.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'package:open_file/open_file.dart';
import 'dart:io';
import 'package:hemmaerp/constants/colors.dart'; // Import ModernAppColors

class BookingListScreen extends StatefulWidget {
  const BookingListScreen({Key? key}) : super(key: key);

  @override
  _BookingListScreenState createState() => _BookingListScreenState();
}

class _BookingListScreenState extends State<BookingListScreen> {
  bool _isLoading = false;
  List<Map<String, dynamic>> _bookings = [];
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  int _sortColumnIndex = 0;
  bool _sortAscending = true;

  @override
  void initState() {
    super.initState();
    _loadBookings();
  }

  Future<void> _loadBookings() async {
    setState(() => _isLoading = true);
    try {
      final response = await http.get(
        Uri.parse('https://backend2.hemmasportacademy.com/booking/select_visit_bookings.php'),
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        setState(() {
          _bookings = data.map((item) => Map<String, dynamic>.from(item)).toList();
          _sortBookings();
          _isLoading = false;
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading bookings: $e'))
      );
      setState(() => _isLoading = false);
    }
  }

  void _sortBookings() {
    if (_sortColumnIndex == null) return;

    _bookings.sort((a, b) {
      final aValue = _getColumnValue(a, _sortColumnIndex);
      final bValue = _getColumnValue(b, _sortColumnIndex);
      
      final comparison = aValue.toLowerCase().compareTo(bValue.toLowerCase());
      return _sortAscending ? comparison : -comparison;
    });
  }

  String _getColumnKey(int index) {
    switch (index) {
      case 0:
        return 'full_name';
      case 1:
        return 'sport_name';
      case 2:
        return 'trainer_name';
      case 3:
        return 'training_day';
      case 4:
        return 'training_time';
      case 5:
        return 'booking_status';
      default:
        return '';
    }
  }

  Future<void> _exportToCSV() async {
    try {
      final headers = [
        'Name',
        'Sport',
        'Trainer',
        'Day',
        'Time',
        'Status',
        'Email',
        'Phone',
        'Created At',
        'Updated At'
      ];

      List<List<dynamic>> rows = [
        headers,
        ..._bookings.map((booking) => [
              booking['full_name'] ?? '',
              booking['sport_name'] ?? '',
              booking['trainer_name'] ?? '',
              booking['training_day'] ?? '',
              booking['training_time'] ?? '',
              booking['booking_status'] ?? '',
              booking['email'] ?? '',
              booking['phone_number'] ?? '',
              booking['created_at'] ?? '',
              booking['updated_at'] ?? ''
            ])
      ];

      String csv = const ListToCsvConverter().convert(rows);
      final directory = await getApplicationDocumentsDirectory();
      final path = '${directory.path}/bookings_${DateTime.now().millisecondsSinceEpoch}.csv';
      final File file = File(path);
      await file.writeAsString(csv);

      await OpenFile.open(path);

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('CSV file exported successfully'))
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error exporting CSV: $e'))
      );
    }
  }

  String _getStatusText(String status) {
    final localizations = AppLocalizations.of(context);
    switch (status.toLowerCase()) {
      case 'pending':
        return localizations?.pending ?? 'Pending';
      case 'confirmed':
        return localizations?.confirmed ?? 'Confirmed';
      case 'cancelled':
        return localizations?.cancelled ?? 'Cancelled';
      default:
        return localizations?.unknown ?? 'Unknown';
    }
  }

  Color _getStatusBackgroundColor(String status, BuildContext context) { // Added context
    final theme = Theme.of(context);
    switch (status.toLowerCase()) {
      case 'pending':
        return ModernAppColors.warning.withOpacity(0.2); // Replaced Colors.orange
      case 'confirmed':
        return ModernAppColors.success.withOpacity(0.2); // Replaced Colors.green
      case 'cancelled':
        return theme.colorScheme.error.withOpacity(0.2);   // Replaced Colors.red
      default:
        return ModernAppColors.disabledColor.withOpacity(0.2); // Replaced Colors.grey
    }
  }

  Color _getStatusTextColor(String status, BuildContext context) { // Added context
    final theme = Theme.of(context);
    switch (status.toLowerCase()) {
      case 'pending':
        return ModernAppColors.warning; // Replaced Colors.orange
      case 'confirmed':
        return ModernAppColors.success; // Replaced Colors.green
      case 'cancelled':
        return theme.colorScheme.error;   // Replaced Colors.red
      default:
        return ModernAppColors.disabledColor; // Replaced Colors.grey
    }
  }

  Widget _buildStatusButtons(Map<String, dynamic> booking) { // Context will be available from build method
    final currentStatus = booking['booking_status']?.toLowerCase() ?? '';
    final localizations = AppLocalizations.of(context);
    
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (currentStatus != 'confirmed')
          TextButton(
            onPressed: () => _updateBookingStatus(booking['id'], 'confirmed'),
            child: Text(
              localizations?.confirm ?? 'Confirm',
              style: TextStyle(color: ModernAppColors.success) // Replaced Colors.green
            ),
          ),
        if (currentStatus != 'cancelled')
          TextButton(
            onPressed: () => _updateBookingStatus(booking['id'], 'cancelled'),
            child: Text(
              localizations?.cancel ?? 'Cancel',
              style: TextStyle(color: Theme.of(context).colorScheme.error) // Replaced Colors.red
            ),
          ),
        if (currentStatus != 'pending')
          TextButton(
            onPressed: () => _updateBookingStatus(booking['id'], 'pending'),
            child: Text(
              localizations?.pending ?? 'Pending',
              style: TextStyle(color: ModernAppColors.warning) // Replaced Colors.orange
            ),
          ),
      ],
    );
  }

  Widget _buildSearchBar() {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.all(12.0),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.0)),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
        child: Row(
          children: [
            Expanded(
              child: TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: AppLocalizations.of(context)!.searchCustomers,
                  prefixIcon: const Icon(Icons.search),
                  suffixIcon: _searchQuery.isNotEmpty
                      ? IconButton(
                          icon: const Icon(Icons.clear),
                          onPressed: () {
                            setState(() {
                              _searchController.clear();
                              _searchQuery = '';
                              _loadBookings();
                            });
                          },
                        )
                      : null,
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(vertical: 12.0),
                ),
                onChanged: (value) {
                  setState(() {
                    _searchQuery = value;
                    _filterBookings();
                  });
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _filterBookings() {
    if (_searchQuery.isEmpty) {
      _loadBookings();
      return;
    }

    final query = _searchQuery.toLowerCase();
    setState(() {
      _bookings = _bookings.where((booking) {
        return (
          (booking['full_name']?.toString().toLowerCase() ?? '').contains(query) ||
          (booking['sport_name']?.toString().toLowerCase() ?? '').contains(query) ||
          (booking['trainer_name']?.toString().toLowerCase() ?? '').contains(query) ||
          (booking['email']?.toString().toLowerCase() ?? '').contains(query) ||
          (booking['phone_number']?.toString().toLowerCase() ?? '').contains(query) ||
          (booking['booking_status']?.toString().toLowerCase() ?? '').contains(query)
        );
      }).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    return Scaffold(
      appBar: AppBar(
        title: Text(localizations?.bookings ?? 'Bookings'),
        elevation: 2,
        actions: [
          IconButton(
            icon: const Icon(Icons.file_download),
            tooltip: 'Export to CSV',
            onPressed: _exportToCSV,
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh data',
            onPressed: _loadBookings,
          ),
        ],
      ),
      body: SafeArea(
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Theme.of(context).scaffoldBackgroundColor,
                Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.2),
              ],
            ),
          ),
          child: Column(
            children: [
              _buildSearchBar(),
              Expanded(
                child: _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : _bookings.isEmpty
                        ? Center(child: Text(localizations?.noBookingsFound ?? 'No bookings found'))
                        : SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: SingleChildScrollView(
                              child: DataTable(
                                sortColumnIndex: _sortColumnIndex,
                                sortAscending: _sortAscending,
                                columns: [
                                  DataColumn(
                                    label: Text(localizations?.name ?? 'Name'),
                                    onSort: (columnIndex, ascending) {
                                      setState(() {
                                        _sortColumnIndex = columnIndex;
                                        _sortAscending = ascending;
                                        _sortBookings();
                                      });
                                    },
                                  ),
                                  DataColumn(
                                    label: Text(localizations?.sport ?? 'Sport'),
                                    onSort: (columnIndex, ascending) {
                                      setState(() {
                                        _sortColumnIndex = columnIndex;
                                        _sortAscending = ascending;
                                        _sortBookings();
                                      });
                                    },
                                  ),
                                  DataColumn(
                                    label: Text(localizations?.trainer ?? 'Trainer'),
                                    onSort: (columnIndex, ascending) {
                                      setState(() {
                                        _sortColumnIndex = columnIndex;
                                        _sortAscending = ascending;
                                        _sortBookings();
                                      });
                                    },
                                  ),
                                  DataColumn(
                                    label: Text(localizations?.day ?? 'Day'),
                                    onSort: (columnIndex, ascending) {
                                      setState(() {
                                        _sortColumnIndex = columnIndex;
                                        _sortAscending = ascending;
                                        _sortBookings();
                                      });
                                    },
                                  ),
                                  DataColumn(
                                    label: Text(localizations?.time ?? 'Time'),
                                    onSort: (columnIndex, ascending) {
                                      setState(() {
                                        _sortColumnIndex = columnIndex;
                                        _sortAscending = ascending;
                                        _sortBookings();
                                      });
                                    },
                                  ),
                                  DataColumn(
                                    label: Text(localizations?.status ?? 'Status'),
                                    onSort: (columnIndex, ascending) {
                                      setState(() {
                                        _sortColumnIndex = columnIndex;
                                        _sortAscending = ascending;
                                        _sortBookings();
                                      });
                                    },
                                  ),
                                  DataColumn(label: Text(localizations?.email ?? 'Email')),
                                  DataColumn(label: Text(localizations?.phone ?? 'Phone')),
                                  DataColumn(label: Text(localizations?.actions ?? 'Actions')),
                                ],
                                rows: _bookings.map((booking) {
                                  return DataRow(
                                    cells: [
                                      DataCell(Text(booking['full_name'] ?? '')),
                                      DataCell(Text(booking['sport_name'] ?? '')),
                                      DataCell(Text(booking['trainer_name'] ?? '')),
                                      DataCell(Text(booking['training_day'] ?? '')),
                                      DataCell(Text(booking['training_time'] ?? '')),
                                      DataCell(
                                        Container(
                                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                          decoration: BoxDecoration(
                                            color: _getStatusBackgroundColor(booking['booking_status'] ?? 'unknown', context), // Passed context
                                            borderRadius: BorderRadius.circular(12),
                                          ),
                                          child: Text(
                                            _getStatusText(booking['booking_status'] ?? 'unknown'),
                                            style: TextStyle(
                                              color: _getStatusTextColor(booking['booking_status'] ?? 'unknown', context), // Passed context
                                              fontSize: 12,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                        ),
                                      ),
                                      DataCell(Text(booking['email'] ?? '')),
                                      DataCell(Text(booking['phone_number'] ?? '')),
                                      DataCell(_buildStatusButtons(booking)), // _buildStatusButtons has context via build method
                                    ],
                                  );
                                }).toList(),
                              ),
                            ),
                          ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _updateBookingStatus(String bookingId, String newStatus) async {
    setState(() => _isLoading = true);
    try {
      final response = await http.post(
        Uri.parse('https://backend2.hemmasportacademy.com/booking/update_visit_booking.php'),
        body: {
          'id': bookingId,
          'booking_status': newStatus,
        },
      );

      if (response.statusCode == 200) {
        // Update the local booking status without reloading
        setState(() {
          final bookingIndex = _bookings.indexWhere((b) => b['id'] == bookingId);
          if (bookingIndex != -1) {
            _bookings[bookingIndex]['booking_status'] = newStatus;
          }
          _isLoading = false;
        });
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Booking status updated to $newStatus'))
        );
      } else {
        throw Exception(AppLocalizations.of(context)?.failedToUpdateBookingStatus ?? 'Failed to update booking status');
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(AppLocalizations.of(context)?.errorUpdatingBookingStatus ?? 'Error updating booking status: $e'))
      );
      setState(() => _isLoading = false);
    }
  }

  Widget _buildInfoRow(String label, String? value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: Text(value ?? (AppLocalizations.of(context)?.notAvailable ?? 'N/A')),
          ),
        ],
      ),
    );
  }
}

String _getColumnKey(int index) {
    switch (index) {
      case 0:
        return 'full_name';
      case 1:
        return 'sport_name';
      case 2:
        return 'trainer_name';
      case 3:
        return 'training_day';
      case 4:
        return 'training_time';
      case 5:
        return 'booking_status';
      default:
        return '';
    }
  }

  String _getColumnValue(Map<String, dynamic> booking, int columnIndex) {
    final key = _getColumnKey(columnIndex);
    return (booking[key] ?? '').toString();
  }
