// ignore_for_file: library_private_types_in_public_api, prefer_final_fields, deprecated_member_use, use_build_context_synchronously, avoid_print, unused_element, prefer_interpolation_to_compose_strings, unused_local_variable, unnecessary_string_interpolations, avoid_web_libraries_in_flutter
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:intl/intl.dart';

// Conditional imports
import 'dart:io';
import 'dart:html' if (dart.library.io) 'dart:io' as platform;
import 'package:path_provider/path_provider.dart';
import 'package:open_file/open_file.dart' show OpenFile, ResultType;
import 'package:flutter/services.dart';
import 'package:hemmaerp/models/customers.dart';
import 'package:hemmaerp/services/customer_api_service_new.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:http/http.dart' as http;
import 'customer_registration_multi_screen.dart';
import 'package:hemmaerp/constants/colors.dart'; // Import ModernAppColors

class CustomerListScreenNew extends StatefulWidget {
  const CustomerListScreenNew({Key? key}) : super(key: key);

  @override
  _CustomerListScreenNewState createState() => _CustomerListScreenNewState();
}

class _CustomerListScreenNewState extends State<CustomerListScreenNew> {
  late final CustomerApiServiceNew _apiService;
  late Future<List<Customer>> _customersFuture;
  final TextEditingController _searchController = TextEditingController();
  List<Customer> _filteredCustomers = [];
  String _searchQuery = '';
  bool _isLoading = false;

  // Sorting state
  int _sortColumnIndex = 0;
  bool _sortAscending = true;

  // Selected row index
  int? _selectedRowIndex;

  // Scroll controller for horizontal scrolling
  final ScrollController _horizontalScrollController = ScrollController();
  double _scrollProgress = 0.0;

  // Columns data
  final List<_DataColumnModel> _columns = [
    _DataColumnModel('Name', (c) => c.name),
    _DataColumnModel('Phone', (c) => c.phone),
    _DataColumnModel('Email', (c) => c.email),
    _DataColumnModel('Sport', (c) => c.sportActivity),
    _DataColumnModel('Trainer', (c) => c.trainer),
    _DataColumnModel('Status', (c) => c.status),
    _DataColumnModel('Birthdate', (c) => c.birthdate),
    _DataColumnModel('Address', (c) => c.address),
    _DataColumnModel('National ID', (c) => c.nationalId),
    _DataColumnModel('Gender', (c) => c.gender),
    _DataColumnModel('Nationality', (c) => c.nationality),
    _DataColumnModel('Parent Phone', (c) => c.parentPhone),
    _DataColumnModel('Health Conditions', (c) => c.healthConditions),
    _DataColumnModel('Full Address', (c) => c.fullAddress),
    _DataColumnModel('Sport Level', (c) => c.sportLevel),
    _DataColumnModel('Subscription Type', (c) => c.subscriptionType),
    _DataColumnModel('Sub. Duration', (c) => c.subscriptionDuration),
    _DataColumnModel('# Sessions', (c) => c.numberOfSessions.toString()),
    _DataColumnModel('Training Times', (c) => c.trainingTimes),
    _DataColumnModel('Payment Method', (c) => c.paymentMethod ?? ''),
    _DataColumnModel('Payment Note', (c) => c.paymentNote ?? ''),
    _DataColumnModel('Total Fees', (c) => c.totalFees.toStringAsFixed(2)),
    _DataColumnModel('Actions', (c) => ''), // dummy for actions
  ];

  @override
  void initState() {
    super.initState();
    _apiService = CustomerApiServiceNew(
      baseUrl: 'https://backend2.hemmasportacademy.com',
    );
    _loadCustomers();

    // Add scroll listener
    _horizontalScrollController.addListener(() {
      if (_horizontalScrollController.hasClients) {
        setState(() {
          _scrollProgress =
              _horizontalScrollController.offset /
              _horizontalScrollController.position.maxScrollExtent;
        });
      }
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _horizontalScrollController.dispose();
    super.dispose();
  }

  void _loadCustomers() {
    setState(() {
      _isLoading = true;
    });

    try {
      _customersFuture = _apiService.fetchCustomers().then((customers) {
        _filterAndSort(customers);
        return customers;
      });
    } catch (e) {
      print("Error fetching customers: $e");
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _filterAndSort(List<Customer> customers) {
    if (_searchQuery.isEmpty) {
      _filteredCustomers = List.from(customers);
    } else {
      final query = _searchQuery.toLowerCase();
      _filteredCustomers =
          customers.where((customer) {
            return (customer.name.toLowerCase().contains(query) ||
                customer.phone.toLowerCase().contains(query) ||
                customer.email.toLowerCase().contains(query) ||
                customer.sportActivity.toLowerCase().contains(query) ||
                customer.trainer.toLowerCase().contains(query) ||
                customer.status.toLowerCase().contains(query) ||
                customer.birthdate.toLowerCase().contains(query) ||
                customer.address.toLowerCase().contains(query) ||
                customer.nationalId.toLowerCase().contains(query) ||
                customer.gender.toLowerCase().contains(query) ||
                customer.nationality.toLowerCase().contains(query) ||
                customer.parentPhone.toLowerCase().contains(query) ||
                customer.healthConditions.toLowerCase().contains(query) ||
                customer.fullAddress.toLowerCase().contains(query) ||
                customer.sportLevel.toLowerCase().contains(query) ||
                customer.subscriptionType.toLowerCase().contains(query) ||
                customer.subscriptionDuration.toLowerCase().contains(query) ||
                customer.numberOfSessions.toString().toLowerCase().contains(
                  query,
                ) ||
                customer.trainingTimes.toLowerCase().contains(query) ||
                (customer.paymentMethod ?? '').toLowerCase().contains(query) ||
                (customer.paymentNote ?? '').toLowerCase().contains(query) ||
                customer.totalFees.toString().toLowerCase().contains(query));
          }).toList();
    }

    _sortData(_sortColumnIndex, _sortAscending);
  }

  void _sortData(int columnIndex, bool ascending) {
    _filteredCustomers.sort((a, b) {
      final compareValue = _columns[columnIndex]
          .valueGetter(ascending ? a : b)
          .compareTo(_columns[columnIndex].valueGetter(ascending ? b : a));
      return ascending ? compareValue : -compareValue;
    });
  }

  Widget _buildSearchBar() {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.all(12.0),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.0)),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
        child: Row(
          children: [
            Expanded(
              child: TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: AppLocalizations.of(context)!.searchCustomers,
                  prefixIcon: const Icon(Icons.search),
                  suffixIcon:
                      _searchQuery.isNotEmpty
                          ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              setState(() {
                                _searchController.clear();
                                _searchQuery = '';
                                _loadCustomers();
                              });
                            },
                          )
                          : null,
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(vertical: 12.0),
                ),
                onChanged: (value) {
                  setState(() {
                    _searchQuery = value;
                  });

                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    _filterAndSort(_filteredCustomers);
                  });
                },
              ),
            ),
            const SizedBox(width: 8),
            PopupMenuButton<String>(
              icon: const Icon(Icons.tune),
              tooltip: 'Advanced search',
              itemBuilder:
                  (context) => [
                    const PopupMenuItem(
                      value: 'name',
                      child: Text('Search by Name'),
                    ),
                    const PopupMenuItem(
                      value: 'phone',
                      child: Text('Search by Phone'),
                    ),
                    const PopupMenuItem(
                      value: 'email',
                      child: Text('Search by Email'),
                    ),
                    const PopupMenuItem(
                      value: 'sport',
                      child: Text('Search by Sport'),
                    ),
                    const PopupMenuItem(
                      value: 'trainer',
                      child: Text('Search by Trainer'),
                    ),
                    const PopupMenuItem(
                      value: 'status',
                      child: Text('Search by Status'),
                    ),
                  ],
              onSelected: (value) {
                // Set placeholder text based on selection
                setState(() {
                  _searchController.clear();
                  _searchQuery = '';
                  _searchController.selection = TextSelection.fromPosition(
                    TextPosition(offset: _searchController.text.length),
                  );
                });

                // Focus the search field
                FocusScope.of(context).requestFocus(FocusNode());
                Future.delayed(const Duration(milliseconds: 100), () {
                  _searchController.text = '$value:';
                  _searchController.selection = TextSelection.fromPosition(
                    TextPosition(offset: _searchController.text.length),
                  );
                  FocusScope.of(context).requestFocus(FocusNode());
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showRefundDialog(BuildContext context, Customer customer) {
    final refundAmountController = TextEditingController();
    final refundReasonController = TextEditingController();
    bool suspendAccount = false;

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: Text(AppLocalizations.of(context)!.refundSubscription),
              content: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${AppLocalizations.of(context)!.customer}: ${customer.name}',
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: refundAmountController,
                      keyboardType: TextInputType.number,
                      decoration: InputDecoration(
                        labelText: AppLocalizations.of(context)!.refundAmount,
                      ),
                    ),
                    TextField(
                      controller: refundReasonController,
                      decoration: InputDecoration(
                        labelText: AppLocalizations.of(context)!.refundReason,
                      ),
                    ),
                    Row(
                      children: [
                        Checkbox(
                          value: suspendAccount,
                          onChanged: (value) {
                            setState(() {
                              suspendAccount = value ?? false;
                            });
                          },
                        ),
                        Text(AppLocalizations.of(context)!.suspendAccount),
                      ],
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: Navigator.of(context).pop,
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: () {
                    _processRefund(
                      customer,
                      double.tryParse(refundAmountController.text) ?? 0.0,
                      refundReasonController.text,
                      suspendAccount,
                    );
                    Navigator.of(context).pop();
                  },
                  child: const Text('Process Refund'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Future<void> _processRefund(
    Customer customer,
    double amount,
    String reason,
    bool suspendAccount,
  ) async {
    if (amount <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            AppLocalizations.of(context)!.pleaseEnterValidRefundAmount,
          ),
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final Map<String, dynamic> updateData = {
        'id': customer.id.toString(),
        'status': suspendAccount ? 'Suspended' : 'Inactive',
        'inactivation_date': DateTime.now().toIso8601String().split('T')[0],
        'inactivation_reason': reason,
      };

      final response = await http.post(
        Uri.parse(
          'https://backend2.hemmasportacademy.com/customers/update_state.php',
        ),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(updateData),
      );

      if (response.statusCode != 200) {
        throw Exception('Failed to update customer status');
      }

      final paymentData = {
        'customer_id': customer.id,
        'payment_type': 'Refund',
        'amount': (-amount).toString(),
        'payment_date': DateTime.now().toIso8601String().split('T')[0],
        'reference_number': 'REF${DateTime.now().millisecondsSinceEpoch}',
        'created_at': DateTime.now().toIso8601String(),
        'payment_note': reason,
        'total_fees': (-amount).toString(),
      };

      final paymentResponse = await http.post(
        Uri.parse(
          'https://backend2.hemmasportacademy.com/customers/insert_payment.php',
        ),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(paymentData),
      );

      if (paymentResponse.statusCode != 200) {
        throw Exception('Failed to create payment record');
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Refund processed successfully'),
          backgroundColor: Colors.green,
        ),
      );

      _loadCustomers();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: $e'), backgroundColor: Colors.red),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _navigateToCustomerRegistration({Customer? customer}) async {
    final dialogResult = await Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => CustomerRegistrationMultiScreen(customer: customer),
      ),
    );
    if (dialogResult != null && customer != null) {
      try {
        final DateTime startDate = DateTime.now();
        final DateTime endDate = startDate.add(
          Duration(days: int.parse(dialogResult['duration']) * 30),
        );

        print('Debug - Sending customer_id: ${customer.id.toString()}');
        print('Debug - Customer object: $customer');

        final response = await http.post(
          Uri.parse(
            'https://backend2.hemmasportacademy.com/customers/update_subscription.php',
          ),
          body: {
            'customer_id': customer.id.toString(),
            'customer_name': customer.name,
            'subscription_type': dialogResult['type'],
            'duration': dialogResult['duration'],
            'amount': dialogResult['amount'],
            'payment_date': DateTime.now().toIso8601String(),
            'start_date': startDate.toIso8601String(),
            'end_date': endDate.toIso8601String(),
          },
        );

        print('Response status: ${response.statusCode}');
        print('Response body: ${response.body}');

        if (response.statusCode == 200) {
          final responseData = jsonDecode(response.body);
          if (responseData['success'] == true) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  AppLocalizations.of(context)!.subscriptionUpdated,
                ),
                backgroundColor: Colors.green,
              ),
            );
            _loadCustomers(); // Refresh the customer list
          } else {
            throw Exception(
              responseData['message'] ?? 'Failed to update subscription',
            );
          }
        } else {
          throw Exception(
            'Server returned status code: ${response.statusCode}',
          );
        }
      } catch (e) {
        print('Error updating subscription: $e');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Update failed: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _copyPhoneNumber(String phone) {
    Clipboard.setData(ClipboardData(text: phone));
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Copied')));
  }

  String _calculateAge(String birthdate) {
    if (birthdate.isEmpty) return '';
    try {
      DateTime birthdateObj;
      if (birthdate.contains('-')) {
        birthdateObj = DateTime.parse(birthdate);
      } else if (birthdate.contains('/')) {
        final parts = birthdate.split('/');
        birthdateObj = DateTime(
          int.parse(parts[2]),
          int.parse(parts[1]),
          int.parse(parts[0]),
        );
      } else {
        return '';
      }

      final today = DateTime.now();
      int years = today.year - birthdateObj.year;
      int months = today.month - birthdateObj.month;
      if (today.month < birthdateObj.month ||
          (today.month == birthdateObj.month && today.day < birthdateObj.day)) {
        years--;
        months = 12 + today.month - birthdateObj.month;
      }
      if (today.day < birthdateObj.day) {
        months--;
        if (months < 0) months += 12;
      }
      return '$years years, $months months';
    } catch (e) {
      return '';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(AppLocalizations.of(context)!.customerList),
        elevation: 2,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh data',
            onPressed: _loadCustomers,
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            tooltip: 'Filter options',
            onPressed: _showFilterOptions,
          ),
        ],
      ),
      body: SafeArea(
        child: Stack(
          children: [
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Theme.of(context).scaffoldBackgroundColor,
                    Theme.of(
                      context,
                    ).colorScheme.surfaceVariant.withOpacity(0.2),
                  ],
                ),
              ),
              child: Column(
                children: [
                  _buildSearchBar(),
                  Expanded(
                    child: FutureBuilder<List<Customer>>(
                      future: _customersFuture,
                      builder: (context, snapshot) {
                        if (snapshot.connectionState ==
                            ConnectionState.waiting) {
                          return const Center(
                            child: CircularProgressIndicator(),
                          );
                        }
                        if (snapshot.hasError) {
                          return Center(
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.error_outline,
                                  size: 48,
                                  color: Colors.red.shade300,
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  'Error: ${snapshot.error}',
                                  textAlign: TextAlign.center,
                                ),
                                const SizedBox(height: 16),
                                ElevatedButton.icon(
                                  icon: const Icon(Icons.refresh),
                                  label: const Text('Try Again'),
                                  onPressed: _loadCustomers,
                                ),
                              ],
                            ),
                          );
                        }
                        if (!snapshot.hasData || snapshot.data!.isEmpty) {
                          return Center(
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.person_off,
                                  size: 48,
                                  color: Colors.grey.shade400,
                                ),
                                const SizedBox(height: 16),
                                const Text(
                                  'No customers found',
                                  textAlign: TextAlign.center,
                                ),
                                const SizedBox(height: 16),
                                ElevatedButton.icon(
                                  icon: const Icon(Icons.person_add),
                                  label: const Text('Add Customer'),
                                  onPressed:
                                      () => _navigateToCustomerRegistration(
                                        customer: null,
                                      ),
                                ),
                              ],
                            ),
                          );
                        }
                        return _buildCustomerTable(snapshot.data!);
                      },
                    ),
                  ),
                ],
              ),
            ),
            if (_isLoading)
              Container(
                color: Colors.black.withOpacity(0.3),
                child: const Center(
                  child: Card(
                    elevation: 4,
                    child: Padding(
                      padding: EdgeInsets.all(16.0),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          CircularProgressIndicator(),
                          SizedBox(height: 16),
                          Text('Loading...'),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        heroTag: "btn1",
        icon: const Icon(Icons.add),
        label: const Text('Add Customer'),
        onPressed: _navigateToCustomerRegistration,
      ),
    );
  }

  void _showFilterOptions() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder:
          (context) => Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Text(
                  'Filter Options',
                  style: Theme.of(context).textTheme.titleLarge,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  icon: const Icon(Icons.check_circle),
                  label: const Text('Active Customers'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green.shade100,
                    foregroundColor: Colors.green.shade800,
                  ),
                  onPressed: () {
                    Navigator.pop(context);
                    _filterByStatus('active');
                  },
                ),
                const SizedBox(height: 8),
                ElevatedButton.icon(
                  icon: const Icon(Icons.warning),
                  label: const Text('Inactive Customers'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange.shade100,
                    foregroundColor: Colors.orange.shade800,
                  ),
                  onPressed: () {
                    Navigator.pop(context);
                    _filterByStatus('inactive');
                  },
                ),
                const SizedBox(height: 8),
                ElevatedButton.icon(
                  icon: const Icon(Icons.cancel),
                  label: const Text('Suspended Customers'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red.shade100,
                    foregroundColor: Colors.red.shade800,
                  ),
                  onPressed: () {
                    Navigator.pop(context);
                    _filterByStatus('suspended');
                  },
                ),
                const SizedBox(height: 8),
                OutlinedButton.icon(
                  icon: const Icon(Icons.clear_all),
                  label: const Text('Clear Filters'),
                  onPressed: () {
                    Navigator.pop(context);
                    _clearFilters();
                  },
                ),
              ],
            ),
          ),
    );
  }

  void _filterByStatus(String status) {
    setState(() {
      _searchController.text = status;
      _searchQuery = status;
    });
    _loadCustomers();
  }

  void _clearFilters() {
    setState(() {
      _searchController.clear();
      _searchQuery = '';
    });
    _loadCustomers();
  }

  Widget _buildCustomerTable(List<Customer> customers) {
    _filterAndSort(customers);

    if (_filteredCustomers.isEmpty) {
      return const Center(child: Text('No matching customers'));
    }

    return Card(
      elevation: 4,
      margin: const EdgeInsets.all(8.0),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Customer Data',
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
                Text(
                  '${_filteredCustomers.length} ${_filteredCustomers.length == 1 ? 'customer' : 'customers'}',
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
                ),
              ],
            ),
          ),
          const Divider(height: 0),
          // Horizontal scroll progress slider
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 16.0,
              vertical: 8.0,
            ),
            child: SliderTheme(
              data: SliderTheme.of(context).copyWith(
                trackHeight: 2.0,
                thumbShape: const RoundSliderThumbShape(
                  enabledThumbRadius: 6.0,
                ),
                overlayShape: const RoundSliderOverlayShape(
                  overlayRadius: 14.0,
                ),
                activeTrackColor: Theme.of(context).primaryColor,
                inactiveTrackColor: Colors.grey[300],
                thumbColor: Theme.of(context).primaryColor,
              ),
              child: Slider(
                value: _scrollProgress.clamp(0.0, 1.0),
                onChanged: (value) {
                  setState(() {
                    _scrollProgress = value;
                    if (_horizontalScrollController.hasClients) {
                      _horizontalScrollController.jumpTo(
                        value *
                            _horizontalScrollController
                                .position
                                .maxScrollExtent,
                      );
                    }
                  });
                },
              ),
            ),
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
              child: SingleChildScrollView(
                controller: _horizontalScrollController,
                scrollDirection: Axis.horizontal,
                child: Theme(
                  data: Theme.of(context).copyWith(
                    dataTableTheme: DataTableThemeData(
                      headingTextStyle: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).primaryColor,
                      ),
                      dataTextStyle: const TextStyle(fontSize: 14),
                    ),
                  ),
                  child: DataTable(
                    sortColumnIndex: _sortColumnIndex,
                    sortAscending: _sortAscending,
                    columnSpacing: 150,
                    headingRowColor: MaterialStateProperty.all(
                      Theme.of(
                        context,
                      ).colorScheme.surfaceVariant.withOpacity(0.2),
                    ),
                    dataRowMinHeight: 60,
                    dataRowMaxHeight: 80,
                    showCheckboxColumn: false,
                    dividerThickness: 1,
                    columns:
                        _columns
                            .asMap()
                            .entries
                            .map(
                              (entry) => DataColumn(
                                label: Padding(
                                  padding: const EdgeInsets.symmetric(
                                    vertical: 8.0,
                                  ),
                                  child: Text(entry.value.title),
                                ),
                                onSort:
                                    (columnIndex, ascending) =>
                                        _sortData(columnIndex, ascending),
                              ),
                            )
                            .toList(),
                    rows:
                        _filteredCustomers.asMap().entries.map((entry) {
                          final index = entry.key;
                          final customer = entry.value;
                          final isEvenRow = index % 2 == 0;

                          return DataRow(
                            color: MaterialStateProperty.resolveWith<Color?>((
                              Set<MaterialState> states,
                            ) {
                              if (states.contains(MaterialState.selected)) {
                                return Theme.of(
                                  context,
                                ).colorScheme.primary.withOpacity(0.1);
                              }
                              return isEvenRow
                                  ? Colors.grey.withOpacity(0.05)
                                  : null;
                            }),
                            selected: _selectedRowIndex == index,
                            onSelectChanged: (selected) {
                              setState(() {
                                _selectedRowIndex = selected! ? index : null;
                              });
                            },
                            cells: [
                              DataCell(_buildCustomerNameCell(customer)),
                              DataCell(_buildPhoneCell(customer.phone)),
                              DataCell(Text(customer.email)),
                              DataCell(Text(customer.sportActivity)),
                              DataCell(Text(customer.trainer)),
                              DataCell(_buildStatusCell(customer.status)),
                              DataCell(Text(customer.birthdate)),
                              DataCell(Text(customer.address)),
                              DataCell(Text(customer.nationalId)),
                              DataCell(_buildGenderCell(customer.gender)),
                              DataCell(Text(customer.nationality)),
                              DataCell(Text(customer.parentPhone)),
                              DataCell(Text(customer.healthConditions)),
                              DataCell(Text(customer.fullAddress)),
                              DataCell(Text(customer.sportLevel)),
                              DataCell(
                                _buildSubscriptionTypeCell(
                                  customer.subscriptionType,
                                ),
                              ),
                              DataCell(Text(customer.subscriptionDuration)),
                              DataCell(
                                Text(customer.numberOfSessions.toString()),
                              ),
                              DataCell(Text(customer.trainingTimes)),
                              DataCell(
                                _buildPaymentMethodCell(customer.paymentMethod),
                              ),
                              DataCell(
                                _buildPaymentNoteCell(customer.paymentNote),
                              ),
                              DataCell(_buildFeesCell(customer.totalFees)),
                              DataCell(_buildActionsCell(customer)),
                            ],
                          );
                        }).toList(),
                  ),
                ),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                OutlinedButton.icon(
                  icon: const Icon(Icons.refresh),
                  label: const Text('Refresh'),
                  onPressed: _loadCustomers,
                ),
                const SizedBox(width: 8),
                ElevatedButton.icon(
                  icon: const Icon(Icons.file_download),
                  label: const Text('Export CSV'),
                  onPressed: () => _exportToCSV(_filteredCustomers),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _showSubscriptionHistory(Customer customer) async {
    try {
      final response = await http.get(
        Uri.parse(
          'https://backend2.hemmasportacademy.com/customers/select_subscriptions.php',
        ),
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        if (responseData['status'] == 'success') {
          final subscriptions = responseData['data'] as List;
          final customerSubscriptions =
              subscriptions
                  .where((sub) => sub['customer_id'] == customer.id.toString())
                  .toList();

          if (!mounted) return;

          await showDialog(
            context: context,
            builder:
                (context) => AlertDialog(
                  title: Text('${customer.name}\'s Subscription History'),
                  content: SizedBox(
                    width: double.maxFinite,
                    child: ListView.builder(
                      shrinkWrap: true,
                      itemCount: customerSubscriptions.length,
                      itemBuilder: (context, index) {
                        final sub = customerSubscriptions[index];
                        return Card(
                          child: ListTile(
                            title: Text(
                              '${sub['subscription_type']} - ${sub['duration']} months',
                            ),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text('Amount: \$${sub['amount']}'),
                                Text('Start: ${sub['start_date']}'),
                                Text('End: ${sub['end_date']}'),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: Text(AppLocalizations.of(context)!.close),
                    ),
                  ],
                ),
          );
        }
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to load subscription history'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Widget _buildCustomerNameCell(Customer customer) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  customer.name,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                if (customer.birthdate.isNotEmpty)
                  Text(
                    _calculateAge(customer.birthdate),
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  ),
              ],
            ),
          ),
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: () => _showSubscriptionHistory(customer),
            tooltip: 'View subscription history',
          ),
        ],
      ),
    );
  }

  Widget _buildPhoneCell(String phone) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(phone),
        IconButton(
          icon: const Icon(Icons.content_copy, size: 16),
          constraints: const BoxConstraints(),
          padding: const EdgeInsets.only(left: 4),
          onPressed: () => _copyPhoneNumber(phone),
          tooltip: 'Copy phone number',
        ),
      ],
    );
  }

  Widget _buildStatusCell(String status) {
    Color bgColor;
    Color textColor;
    IconData statusIcon;
    final theme = Theme.of(context); // Ensure theme is available

    switch (status.toLowerCase()) {
      case 'active':
        bgColor = ModernAppColors.success.withOpacity(0.2);
        textColor =
            ModernAppColors.success; // Using the darker base color for text
        statusIcon = Icons.check_circle;
        break;
      case 'inactive':
        bgColor = ModernAppColors.warning.withOpacity(0.2);
        textColor = ModernAppColors.warning;
        statusIcon = Icons.warning;
        break;
      case 'suspended':
        bgColor = theme.colorScheme.error.withOpacity(0.2);
        textColor = theme.colorScheme.error;
        statusIcon = Icons.cancel;
        break;
      default:
        bgColor = ModernAppColors.disabledColor.withOpacity(0.2);
        textColor = ModernAppColors.disabledColor;
        statusIcon = Icons.help;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: bgColor,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(statusIcon, size: 14, color: textColor),
          const SizedBox(width: 4),
          Text(
            status.toUpperCase(),
            style: TextStyle(
              color: textColor,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGenderCell(String gender) {
    IconData genderIcon;
    Color iconColor;

    final theme = Theme.of(context); // Ensure theme is available
    switch (gender.toLowerCase()) {
      case 'male':
        genderIcon = Icons.male;
        iconColor = theme.colorScheme.primary; // Replaced Colors.blue
        break;
      case 'female':
        genderIcon = Icons.female;
        iconColor =
            theme.colorScheme.secondary; // Replaced Colors.pink with secondary
        break;
      default:
        genderIcon = Icons.person;
        iconColor = theme.hintColor; // Replaced Colors.grey
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(genderIcon, size: 16, color: iconColor),
        const SizedBox(width: 4),
        Text(gender),
      ],
    );
  }

  Widget _buildSubscriptionTypeCell(String subscriptionType) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: Theme.of(context).colorScheme.primaryContainer.withOpacity(0.3),
      ),
      child: Text(
        subscriptionType,
        style: TextStyle(
          color: Theme.of(context).colorScheme.primary,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildFeesCell(double fees) {
    return Text(
      fees.toStringAsFixed(2),
      style: TextStyle(
        fontWeight: FontWeight.bold,
        color:
            fees > 0
                ? ModernAppColors.success
                : Theme.of(
                  context,
                ).colorScheme.error, // Replaced Colors.green and Colors.red
      ),
    );
  }

  Widget _buildPaymentMethodCell(String? paymentMethod) {
    if (paymentMethod == null || paymentMethod.isEmpty) {
      return const Text('-');
    }

    // Define colors and icons based on payment method
    IconData icon;
    Color color;

    final theme = Theme.of(context); // Ensure theme is available
    final method = paymentMethod.toLowerCase();

    if (method.contains('cash')) {
      icon = Icons.money;
      color = ModernAppColors.success; // Replaced Colors.green
    } else if (method.contains('card') ||
        method.contains('visa') ||
        method.contains('mastercard')) {
      icon = Icons.credit_card;
      color = ModernAppColors.info; // Replaced Colors.blue
    } else if (method.contains('bank') || method.contains('transfer')) {
      icon = Icons.account_balance;
      color =
          ModernAppColors.accent; // Replaced Colors.purple with Accent (Teal)
    } else if (method.contains('apple') ||
        method.contains('google') ||
        method.contains('pay')) {
      icon = Icons.smartphone;
      color = ModernAppColors.warning; // Replaced Colors.orange
    } else if (method.contains('tap') ||
        method.contains('payfort') ||
        method.contains('online')) {
      icon = Icons.language;
      color = ModernAppColors.accent; // Replaced Colors.teal with Accent (Teal)
    } else {
      icon = Icons.payment;
      color = theme.hintColor; // Replaced Colors.grey
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 16, color: color),
        const SizedBox(width: 4),
        Flexible(
          child: Text(
            paymentMethod,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(color: color),
          ),
        ),
      ],
    );
  }

  Widget _buildPaymentNoteCell(String? note) {
    if (note == null || note.isEmpty) {
      return const Text('-');
    }

    // Determine if this is a refund note
    final isRefund = note.toLowerCase().contains('refund');

    return Container(
      constraints: const BoxConstraints(maxWidth: 200),
      child: Tooltip(
        message: note,
        child: Text(
          note,
          overflow: TextOverflow.ellipsis,
          style: TextStyle(
            fontStyle: FontStyle.italic,
            color:
                isRefund
                    ? Theme.of(context).colorScheme.error
                    : Theme.of(
                      context,
                    ).hintColor, // Replaced Colors.red and Colors.grey
          ),
        ),
      ),
    );
  }

  Widget _buildActionsCell(Customer customer) {
    final theme = Theme.of(context); // Ensure theme is available
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(
          icon: Icon(Icons.edit, color: Theme.of(context).colorScheme.primary),
          tooltip: 'Edit customer',
          onPressed: () => _navigateToCustomerRegistration(customer: customer),
        ),
        IconButton(
          icon: const Icon(Icons.update, color: Colors.green),
          tooltip: AppLocalizations.of(context)!.updateSubscription,
          onPressed: () => _showUpdateSubscriptionDialog(customer),
        ),
        IconButton(
          icon: const Icon(Icons.money_off, color: Colors.orange),
          tooltip: 'Process refund',
          onPressed: () => _showRefundDialog(context, customer),
        ),
        IconButton(
          icon: const Icon(Icons.visibility, color: Colors.purple),
          tooltip: 'View details',
          onPressed: () => _showCustomerDetails(customer),
        ),
      ],
    );
  }

  Future<void> _showUpdateSubscriptionDialog(Customer selectedCustomer) async {
    final TextEditingController typeController = TextEditingController();
    final TextEditingController durationController = TextEditingController();
    final TextEditingController amountController = TextEditingController();
    String selectedType = 'Monthly';
    DateTime startDate = DateTime.now();
    DateTime? endDate;

    final Map<String, dynamic>?
    dialogResult = await showDialog<Map<String, dynamic>>(
      context: context,
      builder:
          (context) => StatefulBuilder(
            builder:
                (context, setState) => AlertDialog(
                  title: Text(AppLocalizations.of(context)!.updateSubscription),
                  content: SingleChildScrollView(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text('Customer: ${selectedCustomer.name}'),
                        const SizedBox(height: 16),
                        Text('CustomerID: ${selectedCustomer.id.toString()}'),
                        const SizedBox(height: 16),
                        DropdownButtonFormField<String>(
                          value: selectedType,
                          decoration: InputDecoration(
                            labelText:
                                AppLocalizations.of(context)!.subscriptionType,
                          ),
                          items:
                              [
                                'Monthly',
                                'Quarterly',
                                'Semi-Annual',
                                'Annual',
                              ].map((String type) {
                                return DropdownMenuItem<String>(
                                  value: type,
                                  child: Text(type),
                                );
                              }).toList(),
                          onChanged: (String? value) {
                            if (value != null) {
                              setState(() {
                                selectedType = value;
                                durationController.clear();
                                endDate = null;
                              });
                            }
                          },
                        ),
                        const SizedBox(height: 16),
                        DropdownButtonFormField<String>(
                          value:
                              durationController.text.isEmpty
                                  ? null
                                  : '${durationController.text} ${durationController.text == "1" ? "Month" : "Months"}',
                          decoration: InputDecoration(
                            labelText: AppLocalizations.of(context)!.duration,
                            hintText:
                                AppLocalizations.of(context)!.enterDuration,
                          ),
                          items:
                              _getDurationOptions(selectedType).map((
                                String duration,
                              ) {
                                return DropdownMenuItem<String>(
                                  value: duration,
                                  child: Text(duration),
                                );
                              }).toList(),
                          onChanged: (String? value) {
                            if (value != null) {
                              setState(() {
                                durationController.text = value.split(' ')[0];
                                endDate = _calculateEndDate(
                                  startDate,
                                  int.tryParse(durationController.text) ?? 1,
                                  selectedType,
                                );
                              });
                            }
                          },
                        ),
                        const SizedBox(height: 16),
                        ListTile(
                          title: Text(
                            'Start Date: ${DateFormat('yyyy-MM-dd').format(startDate)}',
                          ),
                          trailing: IconButton(
                            icon: const Icon(Icons.calendar_today),
                            onPressed: () async {
                              final pickedDate = await showDatePicker(
                                context: context,
                                initialDate: startDate,
                                firstDate: DateTime.now(),
                                lastDate: DateTime(2100),
                              );
                              if (pickedDate != null) {
                                setState(() {
                                  startDate = pickedDate;
                                  if (durationController.text.isNotEmpty) {
                                    endDate = _calculateEndDate(
                                      startDate,
                                      int.tryParse(durationController.text) ??
                                          1,
                                      selectedType,
                                    );
                                  }
                                });
                              }
                            },
                          ),
                        ),
                        if (endDate != null)
                          ListTile(
                            title: Text(
                              'End Date: ${DateFormat('yyyy-MM-dd').format(endDate!)}',
                            ),
                          ),
                        const SizedBox(height: 16),
                        TextField(
                          controller: amountController,
                          decoration: InputDecoration(
                            labelText: AppLocalizations.of(context)!.amount,
                            hintText: AppLocalizations.of(context)!.enterAmount,
                          ),
                          keyboardType: TextInputType.number,
                        ),
                      ],
                    ),
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: Text(AppLocalizations.of(context)!.cancel),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).pop({
                          'type': selectedType,
                          'duration': durationController.text,
                          'amount': amountController.text,
                          'start_date': startDate.toIso8601String(),
                          'end_date': endDate?.toIso8601String() ?? '',
                        });
                      },
                      child: Text(AppLocalizations.of(context)!.update),
                    ),
                  ],
                ),
          ),
    );

    if (dialogResult != null) {
      try {
        print('Sending data:');
        print(
          jsonEncode({
            'customer_id': selectedCustomer.id.toString(),
            'customer_name': selectedCustomer.name,
            'subscription_type': dialogResult['type'],
            'duration': dialogResult['duration'],
            'amount': dialogResult['amount'],
            'payment_date': DateTime.now().toIso8601String(),
            'start_date': dialogResult['start_date'],
            'end_date': dialogResult['end_date'],
          }),
        );
        final response = await http.post(
          Uri.parse(
            'https://backend2.hemmasportacademy.com/customers/update_subscription.php',
          ),
          headers: {'Content-Type': 'application/json'},
          body: jsonEncode({
            'customer_id': selectedCustomer.id, // keep it as int
            'customer_name': selectedCustomer.name,
            'subscription_type': dialogResult['type'],
            'duration': dialogResult['duration'],
            'amount': dialogResult['amount'],
            'payment_date': DateTime.now().toIso8601String(),
            'start_date': dialogResult['start_date'],
            'end_date': dialogResult['end_date'],
          }),
        );

        print('Response status: ${response.statusCode}');
        print('Response body: ${response.body}');

        if (response.statusCode == 200) {
          final responseData = jsonDecode(response.body);
          if (responseData['status'] == 'success') {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  AppLocalizations.of(context)!.subscriptionUpdated,
                ),
                backgroundColor: Colors.green,
              ),
            );
            _loadCustomers(); // Refresh the customer list
          } else {
            throw Exception(
              responseData['message'] ?? 'Failed to update subscription',
            );
          }
        } else {
          throw Exception(
            'Server returned status code: ${response.statusCode}',
          );
        }
      } catch (e) {
        print('Error: $e');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context)!.updateFailed),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  List<String> _getDurationOptions(String subscriptionType) {
    switch (subscriptionType) {
      case 'Monthly':
        return ['1 Month', '2 Months', '3 Months'];
      case 'Quarterly':
        return ['3 Months', '6 Months', '9 Months'];
      case 'Semi-Annual':
        return ['6 Months', '12 Months', '18 Months'];
      case 'Annual':
        return ['12 Months', '24 Months', '36 Months'];
      default:
        return ['1 Month'];
    }
  }

  DateTime _calculateEndDate(
    DateTime startDate,
    int duration,
    String subscriptionType,
  ) {
    int monthsToAdd = 0;
    switch (subscriptionType) {
      case 'Monthly':
        monthsToAdd = duration;
        break;
      case 'Quarterly':
        monthsToAdd = duration; // duration is already in months (3, 6, 9)
        break;
      case 'Semi-Annual':
        monthsToAdd = duration; // duration is already in months (6, 12, 18)
        break;
      case 'Annual':
        monthsToAdd = duration; // duration is already in months (12, 24, 36)
        break;
      default:
        monthsToAdd = duration;
    }

    int year = startDate.year;
    int month = startDate.month + monthsToAdd;
    int day = startDate.day;

    // Handle month overflow
    while (month > 12) {
      month -= 12;
      year++;
    }

    // Handle day overflow (e.g., adding to Jan 31st)
    // Find the last day of the target month
    DateTime lastDayOfTargetMonth = DateTime(year, month + 1, 0);
    if (day > lastDayOfTargetMonth.day) {
      day = lastDayOfTargetMonth.day;
    }

    return DateTime(year, month, day);
  }

  void _showCustomerDetails(Customer customer) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(customer.name),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildDetailItem('Phone', customer.phone),
                  _buildDetailItem('Email', customer.email),
                  _buildDetailItem('Sport', customer.sportActivity),
                  _buildDetailItem('Trainer', customer.trainer),
                  _buildDetailItem('Status', customer.status),
                  _buildDetailItem('Birthdate', customer.birthdate),
                  if (customer.birthdate.isNotEmpty)
                    _buildDetailItem('Age', _calculateAge(customer.birthdate)),
                  _buildDetailItem(
                    'Address',
                    customer.fullAddress.isNotEmpty
                        ? customer.fullAddress
                        : customer.address,
                  ),
                  _buildDetailItem('National ID', customer.nationalId),
                  _buildDetailItem('Gender', customer.gender),
                  _buildDetailItem('Nationality', customer.nationality),
                  _buildDetailItem('Parent Phone', customer.parentPhone),
                  _buildDetailItem(
                    'Health Conditions',
                    customer.healthConditions,
                  ),
                  _buildDetailItem('Sport Level', customer.sportLevel),
                  _buildDetailItem(
                    'Subscription',
                    '${customer.subscriptionType} (${customer.subscriptionDuration})',
                  ),
                  _buildDetailItem(
                    'Sessions',
                    customer.numberOfSessions.toString(),
                  ),
                  _buildDetailItem('Training Times', customer.trainingTimes),
                  _buildDetailSection('Payment Information'),
                  _buildPaymentDetailItem(
                    'Payment Method',
                    customer.paymentMethod,
                  ),
                  _buildDetailItem('Payment Note', customer.paymentNote ?? '-'),
                  _buildDetailItem(
                    'Total Fees',
                    '${customer.totalFees.toStringAsFixed(2)}',
                    valueColor:
                        customer.totalFees > 0 ? Colors.green : Colors.red,
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Close'),
              ),
              ElevatedButton.icon(
                icon: const Icon(Icons.edit),
                label: const Text('Edit'),
                onPressed: () {
                  Navigator.of(context).pop();
                  _navigateToCustomerRegistration(customer: customer);
                },
              ),
            ],
          ),
    );
  }

  Widget _buildDetailItem(String label, String value, {Color? valueColor}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(
              value.isEmpty ? '-' : value,
              style:
                  valueColor != null
                      ? TextStyle(
                        color: valueColor,
                        fontWeight: FontWeight.bold,
                      )
                      : null,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailSection(String title) {
    return Padding(
      padding: const EdgeInsets.only(top: 16.0, bottom: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).primaryColor,
            ),
          ),
          const Divider(),
        ],
      ),
    );
  }

  Widget _buildPaymentDetailItem(String label, String? paymentMethod) {
    if (paymentMethod == null || paymentMethod.isEmpty) {
      return _buildDetailItem(label, '-');
    }

    // Define colors and icons based on payment method
    IconData icon;
    Color color;

    final method = paymentMethod.toLowerCase();

    if (method.contains('cash')) {
      icon = Icons.money;
      color = Colors.green;
    } else if (method.contains('card') ||
        method.contains('visa') ||
        method.contains('mastercard')) {
      icon = Icons.credit_card;
      color = Colors.blue;
    } else if (method.contains('bank') || method.contains('transfer')) {
      icon = Icons.account_balance;
      color = Colors.purple;
    } else if (method.contains('apple') ||
        method.contains('google') ||
        method.contains('pay')) {
      icon = Icons.smartphone;
      color = Colors.orange;
    } else if (method.contains('tap') ||
        method.contains('payfort') ||
        method.contains('online')) {
      icon = Icons.language;
      color = Colors.teal;
    } else {
      icon = Icons.payment;
      color = Colors.grey;
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Row(
              children: [
                Icon(icon, size: 16, color: color),
                const SizedBox(width: 4),
                Text(paymentMethod, style: TextStyle(color: color)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _exportToCSV(List<Customer> customers) async {
    try {
      final buffer = StringBuffer();
      // Add UTF-8 BOM
      buffer.write('\uFEFF');

      // Write headers
      buffer.writeln(
        [
          '"Name"',
          '"Phone"',
          '"Email"',
          '"Sport"',
          '"Trainer"',
          '"Status"',
          '"Birthdate"',
          '"Address"',
          '"National ID"',
          '"Gender"',
          '"Nationality"',
          '"Parent Phone"',
          '"Health Conditions"',
          '"Full Address"',
          '"Sport Level"',
          '"Subscription Type"',
          '"Subscription Duration"',
          '"Number of Sessions"',
          '"Training Times"',
          '"Payment Method"',
          '"Payment Note"',
          '"Total Fees"',
        ].join(','),
      );

      // Write data rows
      for (var c in customers) {
        buffer.writeln(
          [
            '"${_escapeCSV(c.name)}"',
            '"${_escapeCSV(c.phone)}"',
            '"${_escapeCSV(c.email)}"',
            '"${_escapeCSV(c.sportActivity)}"',
            '"${_escapeCSV(c.trainer)}"',
            '"${_escapeCSV(c.status)}"',
            '"${_escapeCSV(c.birthdate)}"',
            '"${_escapeCSV(c.address)}"',
            '"${_escapeCSV(c.nationalId)}"',
            '"${_escapeCSV(c.gender)}"',
            '"${_escapeCSV(c.nationality)}"',
            '"${_escapeCSV(c.parentPhone)}"',
            '"${_escapeCSV(c.healthConditions)}"',
            '"${_escapeCSV(c.fullAddress)}"',
            '"${_escapeCSV(c.sportLevel)}"',
            '"${_escapeCSV(c.subscriptionType)}"',
            '"${_escapeCSV(c.subscriptionDuration)}"',
            '"${c.numberOfSessions}"',
            '"${_escapeCSV(c.trainingTimes)}"',
            '"${_escapeCSV(c.paymentMethod ?? '')}"',
            '"${_escapeCSV(c.paymentNote ?? '')}"',
            '"${c.totalFees.toString() ?? ''}"',
          ].join(','),
        );
      }

      final now = DateTime.now();
      final timestamp =
          '${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}_${now.hour.toString().padLeft(2, '0')}}${now.minute.toString().padLeft(2, '0')}';
      final fileName = 'customers_export_$timestamp.csv';
      final csvData = buffer.toString();

      if (kIsWeb) {
        // Web platform: Use HTML download
        final bytes = utf8.encode(csvData);
        final blob = platform.Blob([bytes]);
        final url = platform.Url.createObjectUrlFromBlob(blob);

        final anchor =
            platform.AnchorElement(href: url)
              ..setAttribute('download', fileName)
              ..style.display = 'none';
        platform.document.body!.children.add(anchor);

        anchor.click();
        platform.document.body!.children.remove(anchor);
        platform.Url.revokeObjectUrl(url);
      } else {
        // Native platform: Use path_provider and open_file
        final directory = await getApplicationDocumentsDirectory();
        final file = File('${directory.path}/$fileName');
        await file.writeAsString(csvData, encoding: utf8);

        final result = await OpenFile.open(file.path);
        if (result.type != ResultType.done) {
          throw Exception('Failed to open file: ${result.message}');
        }
      }

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('CSV file exported successfully')),
      );
    } catch (e) {
      print('Error exporting CSV: $e');
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Failed to export CSV: $e')));
    }
  }

  String _escapeCSV(String value) {
    return value.replaceAll('"', '""');
  }
}

class _DataColumnModel {
  final String title;
  final String Function(Customer) valueGetter;
  _DataColumnModel(this.title, this.valueGetter);
}
