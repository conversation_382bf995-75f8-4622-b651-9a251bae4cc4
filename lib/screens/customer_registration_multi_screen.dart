// ignore_for_file: library_private_types_in_public_api, use_build_context_synchronously, prefer_final_fields, avoid_print, unused_element

import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:hemmaerp/models/customers.dart';
import 'package:hemmaerp/models/customer_sport.dart';
import 'package:hemmaerp/models/sport.dart';
import 'package:hemmaerp/screens/registration/personal_info_screen.dart';
import 'package:hemmaerp/screens/registration/health_info_screen.dart';
import 'package:hemmaerp/screens/registration/sports_selection_screen.dart';
import 'package:hemmaerp/screens/registration/payment_details_screen.dart';
import 'package:hemmaerp/screens/registration/registration_summary_screen.dart';
import 'package:http/http.dart' as http;
import 'package:hemmaerp/screens/customer_list_screen_new.dart';
import 'package:hemmaerp/services/trainer_service.dart';
import 'package:hemmaerp/constants/colors.dart';
// import 'package:hemmaerp/constants/colors.dart'; // AppColors will be replaced by Theme.of(context)
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hemmaerp/screens/registration/account_type_screen.dart';
import 'package:hemmaerp/screens/registration/family_member_screen.dart';

class CustomerRegistrationMultiScreen extends StatefulWidget {
  final Customer? customer;
  final Function(Customer, List<Customer>)? onSubmit;

  const CustomerRegistrationMultiScreen({
    Key? key,
    this.customer,
    this.onSubmit,
  }) : super(key: key);

  @override
  State<CustomerRegistrationMultiScreen> createState() =>
      _CustomerRegistrationMultiScreenState();
}

class _CustomerRegistrationMultiScreenState
    extends State<CustomerRegistrationMultiScreen> {
  final PageController _pageController = PageController();
  int _currentPage = 0;
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final GlobalKey<PersonalInfoScreenState> _personalInfoKey =
      GlobalKey<PersonalInfoScreenState>();

  Customer _customer = Customer(
    id: '',
    name: '',
    email: '',
    phone: '',
    address: '',
    nationalId: '',
    birthdate: '',
    gender: '',
    nationality: '',
    fullAddress: '',
    healthConditions: '',
    sportActivity: '',
    sportLevel: '',
    trainer: '',
    subscriptionType: '',
    subscriptionDuration: '',
    numberOfSessions: 0,
    trainingTimes: '',
    paymentMethod: '',
    totalFees: 0.0,
    howDidHearAboutUs: '',
    accountType: 'Individual',
    taxEnabled: false,
    status: 'active',
  );

  List<Customer> _familyMembers = [];

  // Customer data
  List<CustomerSport> _selectedSports = [];
  List<Sport> _availableSports = [];
  List<Map<String, dynamic>> _trainers = [];
  String _paymentType = 'Cash';
  double _totalAmount = 0.0;

  // Services
  final TrainerService _trainerService = TrainerService();

  // List of subscription types
  final List<String> _subscriptionTypes = [
    'Monthly',
    'Quarterly',
    'Semi-Annual',
    'Annual',
  ];

  // List of subscription durations
  final Map<String, List<String>> _subscriptionDurations = {
    'Monthly': ['1 Month', '2 Months', '3 Months'],
    'Quarterly': ['3 Months', '6 Months', '9 Months'],
    'Semi-Annual': ['6 Months', '12 Months', '18 Months'],
    'Annual': ['12 Months', '24 Months', '36 Months'],
  };

  // List of payment types for Saudi Arabia
  final List<String> _paymentTypes = [
    // Traditional Payment Methods
    "Cash",
    // Card Payments
    "Visa",
    // Fintech & BNPL Services
    "Tabby",
    // Online Payment Gateways
    "Transfer",
  ];

  @override
  void initState() {
    super.initState();
    _customer =
        widget.customer ??
        Customer(
          id: '',
          name: '',
          email: '',
          phone: '',
          address: '',
          nationalId: '',
          birthdate: '',
          gender: '',
          nationality: '',
          fullAddress: '',
          healthConditions: '',
          sportActivity: '',
          sportLevel: '',
          trainer: '',
          subscriptionType: '',
          subscriptionDuration: '',
          numberOfSessions: 0,
          trainingTimes: '',
          paymentMethod: '',
          totalFees: 0.0,
          howDidHearAboutUs: '',
          accountType: 'Individual',
          taxEnabled: false,
          status: 'active',
        );

    // Load available sports and trainers from API
    _loadSports();
    _loadTrainers();
  }

  Future<void> _loadSports() async {
    try {
      final response = await http.get(
        Uri.parse('https://backend2.hemmasportacademy.com/fetch_sports.php'),
      );

      if (response.statusCode == 200) {
        final List<dynamic> sportsJson = json.decode(response.body);
        setState(() {
          _availableSports =
              sportsJson
                  .map(
                    (json) => Sport(
                      id: int.parse(json['id'].toString()),
                      name: json['name'] ?? '',
                      trainers: json['trainers'] ?? '',
                      fees: double.parse(json['fees'].toString()),
                      number_of_sessions: int.parse(
                        json['number_of_sessions'].toString(),
                      ),
                      training_time: json['training_time'] ?? '',
                      training_days:
                          json['training_time'] ??
                          '', // Use training_time value
                    ),
                  )
                  .toList();
        });
      } else {
        throw Exception(
          AppLocalizations.of(
            context,
          )!.failedToLoadSportsWithStatus(response.statusCode.toString()),
        );
      }
    } catch (e) {
      print('Error fetching sports: $e');
      // Fallback to dummy data if API fails
      _loadDummySports();

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            AppLocalizations.of(
              context,
            )!.errorLoadingSportsWithDetails(e.toString()),
          ),
        ),
      );
    }
  }

  // Fallback method if API fails
  void _loadDummySports() {
    _availableSports = [
      Sport(
        id: 1,
        name: AppLocalizations.of(context)!.football,
        trainers: 'Ahmed, Mohammed, Ali',
        fees: 100.0,
        number_of_sessions: 12,
        training_time: 'Morning 9-11, Afternoon 2-4, Evening 6-8',
        training_days: 'Monday, Wednesday, Friday',
      ),
      Sport(
        id: 2,
        name: AppLocalizations.of(context)!.swimming,
        trainers: 'Khalid, Omar',
        fees: 150.0,
        number_of_sessions: 8,
        training_time: 'Morning 10-12, Evening 5-7',
        training_days: 'Tuesday, Thursday, Saturday',
      ),
      Sport(
        id: 3,
        name: AppLocalizations.of(context)!.basketball,
        trainers: 'Fahad, Saad',
        fees: 120.0,
        number_of_sessions: 10,
        training_time: 'Afternoon 3-5, Evening 7-9',
        training_days: 'Sunday, Tuesday, Thursday',
      ),
    ];
  }

  Future<void> _loadTrainers() async {
    try {
      final trainers = await _trainerService.getTrainers();
      setState(() {
        _trainers = trainers;
      });
    } catch (e) {
      print('Error loading trainers: $e');
      // Fallback to empty list if API fails
      setState(() {
        _trainers = [];
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            AppLocalizations.of(
              context,
            )!.errorLoadingTrainersWithDetails(e.toString()),
          ),
        ),
      );
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _updateCustomer(Customer customer) {
    setState(() {
      _customer = customer;
      if (customer.accountType == 'Family' &&
          customer.numberOfChildren != null) {
        while (_familyMembers.length < customer.numberOfChildren!) {
          _familyMembers.add(
            Customer(
              id: '',
              name: '',
              email: '',
              phone: '',
              address: '',
              nationalId: '',
              birthdate: '',
              gender: '',
              nationality: '',
              fullAddress: '',
              healthConditions: '',
              sportActivity: '',
              sportLevel: '',
              trainer: '',
              subscriptionType: '',
              subscriptionDuration: '',
              numberOfSessions: 0,
              trainingTimes: '',
              paymentMethod: '',
              totalFees: 0.0,
              howDidHearAboutUs: '',
              accountType: 'Individual',
              taxEnabled: false,
              status: 'active',
            ),
          );
        }
        while (_familyMembers.length > customer.numberOfChildren!) {
          _familyMembers.removeLast();
        }
      }
    });
  }

  void _updateFamilyMember(int index, Customer member) {
    setState(() {
      _familyMembers[index] = member;
    });
  }

  void _updateSportsData(List<CustomerSport> sports) {
    setState(() {
      _selectedSports = sports;
      _totalAmount = _calculateTotalAmount(
        _customer,
      ); // Change to use return value instead of side effect
    });
  }

  double _calculateTotalAmount(Customer updatedCustomer) {
    double total = 0.0;

    for (var sport in _selectedSports) {
      // Add base fees and additional fees
      double baseFees = sport.fees;
      double additionalFees = sport.additionalFees ?? 0.0;
      total += baseFees + additionalFees;

      // Add uniform price if included
      if (sport.uniformIncluded ?? false) {
        if (sport.uniformPrice != null) {
          total += sport.uniformPrice!;
        }
      }
    }

    // Apply tax only if enabled
    if (updatedCustomer.taxEnabled == true) {
      total = total * 1.15; // Add 15% tax to total amount
    }

    return total;
  }

  void _updatePaymentType(String paymentType) {
    setState(() {
      _paymentType = paymentType;
    });
  }

  void _nextPage() {
    if (_currentPage == 0) {
      // Validate account type selection
      if (_customer.accountType.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Please select an account type.')),
        );
        return;
      }
      if (_customer.accountType == 'Family' &&
          (_customer.numberOfChildren == null ||
              _customer.numberOfChildren! <= 0)) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Please enter the number of children.')),
        );
        return;
      }
    }
    if (_currentPage < _getTotalPages() - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _previousPage() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  int _getTotalPages() {
    int basePages =
        6; // Account Type, Personal Info, Health Info, Sports Selection, Payment Details, Summary
    if (_customer.accountType == 'Family') {
      basePages +=
          _customer.numberOfChildren ?? 0; // Add pages for each family member
    }
    return basePages;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Registration')),
      body: Column(
        children: [
          Expanded(
            child: PageView(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentPage = index;
                });
              },
              children: [
                AccountTypeScreen(
                  customer: _customer,
                  onUpdate: _updateCustomer,
                ),
                PersonalInfoScreen(
                  key: _personalInfoKey,
                  customer: _customer,
                  onUpdate: _updateCustomer,
                ),
                HealthInfoScreen(
                  customer: _customer,
                  onUpdate: _updateCustomer,
                ),
                SportsSelectionScreen(
                  customer: _customer,
                  selectedSports: _selectedSports,
                  availableSports: _availableSports,
                  subscriptionTypes: _subscriptionTypes,
                  subscriptionDurations: _subscriptionDurations,
                  trainers: _trainers,
                  onUpdate: _updateSportsData,
                ),
                PaymentDetailsScreen(
                  customer: _customer,
                  onUpdate: _updateCustomer,
                ),
                if (_customer.accountType == 'Family')
                  ..._familyMembers.asMap().entries.map((entry) {
                    final index = entry.key;
                    final member = entry.value;
                    return FamilyMemberScreen(
                      customer: member,
                      memberIndex: index,
                      onUpdate:
                          (updatedMember) =>
                              _updateFamilyMember(index, updatedMember),
                    );
                  }),
                RegistrationSummaryScreen(
                  customer: _customer,
                  selectedSports: _selectedSports,
                  totalAmount: _totalAmount,
                  paymentType: _paymentType,
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                if (_currentPage > 0)
                  ElevatedButton(
                    onPressed: _previousPage,
                    child: Text(AppLocalizations.of(context)!.previous),
                  )
                else
                  const SizedBox.shrink(),
                if (_currentPage < _getTotalPages() - 1)
                  ElevatedButton(
                    onPressed: _nextPage,
                    child: Text(AppLocalizations.of(context)!.next),
                  )
                else
                  ElevatedButton(
                    onPressed: () {
                      // Submit the form
                      widget.onSubmit!(_customer, _familyMembers);
                    },
                    child: Text(AppLocalizations.of(context)!.submit),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
