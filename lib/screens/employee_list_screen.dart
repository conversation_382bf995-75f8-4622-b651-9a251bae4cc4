// ignore_for_file: library_private_types_in_public_api, use_build_context_synchronously, deprecated_member_use, prefer_final_fields, unused_element, unnecessary_import, unnecessary_brace_in_string_interps

import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'employee_management_screen.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:cross_file/cross_file.dart';
import 'package:hemmaerp/constants/colors.dart'; // Import ModernAppColors

class EmployeeListScreen extends StatefulWidget {
  const EmployeeListScreen({Key? key}) : super(key: key);

  @override
  _EmployeeListScreenState createState() => _EmployeeListScreenState();
}

class _EmployeeListScreenState extends State<EmployeeListScreen> {
  List<Map<String, dynamic>> _employees = [];
  List<Map<String, dynamic>> _filteredEmployees = [];
  bool _isLoading = false;
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _horizontalScrollController = ScrollController();
  double _scrollProgress = 0.0;

  @override
  void dispose() {
    _searchController.dispose();
    _horizontalScrollController.dispose();
    super.dispose();
  }
  
  // Sorting state
  String _sortColumn = 'name';
  bool _sortAscending = true;
  
  // Pagination state
  int _currentPage = 0;
  List<int> _availableRowsPerPage = [5, 10, 20, 50];
  
  // Localization strings for missing translations
  String get _showHideColumns => 'Show/Hide Columns';
  String get _export => 'Export';
  String get _salary => 'Salary';
  String get _rowsPerPageText => 'Rows per page';
  int _rowsPerPage = 10;
  String get _of => 'of';
  String get _refresh => 'Refresh';
  String get _actions => 'Actions';
  String get _email => 'Email';
  String get _phone => 'Phone';
  String get _position => 'Position';
  String get _department => 'Department';
  String get _name => 'Name';
  String get _noName => 'No Name';
  String get _noPosition => 'No Position';
  String get _noDepartment => 'No Department';
  String get _noEmployeesFound => 'No employees found';
  String get _noMatchingEmployeesFound => 'No matching employees found';
  String get _searchEmployees => 'Search employees';
  String get _addEmployee => 'Add Employee';
  String get _errorLoadingEmployees => 'Error loading employees';
  
  // Column visibility
  Map<String, bool> _columnVisibility = {
    'name': true,
    'position': true,
    'department': true,
    'email': true,
    'phone': true,
    'salary': true,
  };

  @override
  void initState() {
    super.initState();
    _loadEmployees();
    _horizontalScrollController.addListener(() {
      if (_horizontalScrollController.hasClients) {
        setState(() {
          _scrollProgress = _horizontalScrollController.position.pixels /
              _horizontalScrollController.position.maxScrollExtent;
        });
      }
    });
  }

  Future<void> _loadEmployees() async {
    setState(() => _isLoading = true);
    try {
      final response = await http.get(
        Uri.parse('https://backend2.hemmasportacademy.com/employees/select_employee.php'),
      );

      if (response.statusCode == 200) {
        final List<Map<String, dynamic>> loadedEmployees = 
            List<Map<String, dynamic>>.from(json.decode(response.body));
        
        setState(() {
          _employees = loadedEmployees;
          _filterAndSortEmployees();
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('${_errorLoadingEmployees}: ${e.toString()}')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _filterAndSortEmployees() {
    // Apply search filter
    List<Map<String, dynamic>> filtered = _employees;
    if (_searchQuery.isNotEmpty) {
      final searchLower = _searchQuery.toLowerCase();
      filtered = _employees.where((employee) {
        return _columnVisibility.entries.any((entry) {
          final key = entry.key;
          final isVisible = entry.value;
          if (!isVisible) return false;
          
          final value = employee[key]?.toString().toLowerCase() ?? '';
          return value.contains(searchLower);
        });
      }).toList();
    }
    
    // Apply sorting
    filtered.sort((a, b) {
      final aValue = a[_sortColumn]?.toString().toLowerCase() ?? '';
      final bValue = b[_sortColumn]?.toString().toLowerCase() ?? '';
      
      return _sortAscending 
          ? aValue.compareTo(bValue) 
          : bValue.compareTo(aValue);
    });
    
    setState(() {
      _filteredEmployees = filtered;
    });
  }

  void _sort(String column) {
    setState(() {
      if (_sortColumn == column) {
        _sortAscending = !_sortAscending;
      } else {
        _sortColumn = column;
        _sortAscending = true;
      }
      _filterAndSortEmployees();
    });
  }
  
  List<Map<String, dynamic>> get _paginatedEmployees {
    final startIndex = _currentPage * _rowsPerPage;
    final endIndex = startIndex + _rowsPerPage > _filteredEmployees.length 
        ? _filteredEmployees.length 
        : startIndex + _rowsPerPage;
    
    if (startIndex >= _filteredEmployees.length) {
      return [];
    }
    
    return _filteredEmployees.sublist(startIndex, endIndex);
  }
  
  int get _pageCount {
    return (_filteredEmployees.length / _rowsPerPage).ceil();
  }
  
  Future<void> _exportToPdf() async {
    final pdf = pw.Document();
    
    // Add a page to the PDF document
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(32),
        header: (pw.Context context) {
          return pw.Text(
            'Employee List',
            style: pw.TextStyle(fontSize: 24, fontWeight: pw.FontWeight.bold),
          );
        },
        build: (pw.Context context) {
          return [
            pw.Table.fromTextArray(
              headers: [
                'Name',
                'Position',
                'Department',
                'Email',
                'Phone',
                'Salary',
              ].where((header) => _columnVisibility[header.toLowerCase()] ?? false).toList(),
              data: _filteredEmployees.map((employee) {
                final allCells = [
                  employee['name'] ?? 'N/A',
                  employee['position'] ?? 'N/A',
                  employee['department'] ?? 'N/A',
                  employee['email'] ?? 'N/A',
                  employee['phone'] ?? 'N/A',
                  employee['salary'] ?? 'N/A',
                ];
                final visibleCells = <String>[];
                final columnKeys = ['name', 'position', 'department', 'email', 'phone', 'salary'];
                for (int i = 0; i < allCells.length; i++) {
                  if (_columnVisibility[columnKeys[i]] ?? false) {
                    visibleCells.add(allCells[i].toString());
                  }
                }
                return visibleCells;
              }).toList(),
              border: pw.TableBorder.all(),
              headerStyle: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              headerDecoration: const pw.BoxDecoration(
                color: PdfColors.grey300,
              ),
              cellAlignments: {
                0: pw.Alignment.centerLeft,
                1: pw.Alignment.centerLeft,
                2: pw.Alignment.centerLeft,
                3: pw.Alignment.centerLeft,
                4: pw.Alignment.centerLeft,
                5: pw.Alignment.centerRight,
              },
            ),
          ];
        },
      ),
    );
    
    await Printing.sharePdf(bytes: await pdf.save(), filename: 'employees.pdf');
  }
  
  Future<void> _exportToCsv() async {
    // Create CSV content
    final StringBuffer csvContent = StringBuffer();
    
    // Add headers
    final headers = [
      'Name',
      'Position',
      'Department',
      'Email',
      'Phone',
      'Salary',
    ].where((header) => _columnVisibility[header.toLowerCase()] ?? false).toList();
    
    csvContent.writeln(headers.join(','));
    
    // Add rows
    for (final employee in _filteredEmployees) {
      final allCells = [
        '"${employee['name'] ?? 'N/A'}"',
        '"${employee['position'] ?? 'N/A'}"',
        '"${employee['department'] ?? 'N/A'}"',
        '"${employee['email'] ?? 'N/A'}"',
        '"${employee['phone'] ?? 'N/A'}"',
        '"${employee['salary'] ?? 'N/A'}"',
      ];
      final visibleCells = <String>[];
      final columnKeys = ['name', 'position', 'department', 'email', 'phone', 'salary'];
      for (int i = 0; i < allCells.length; i++) {
        if (_columnVisibility[columnKeys[i]] ?? false) {
          visibleCells.add(allCells[i].toString());
        }
      }
      csvContent.writeln(visibleCells.join(','));
    }
    
    // Save to temporary file and share
    final directory = await getTemporaryDirectory();
    final file = File('${directory.path}/employees.csv');
    await file.writeAsString(csvContent.toString());
    
    await Share.shareXFiles([XFile(file.path)], text: 'Employee Data');
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.employees),
        actions: [
          // Column visibility menu
          PopupMenuButton<String>(
            icon: const Icon(Icons.view_column),
            tooltip: _showHideColumns,
            onSelected: (String column) {
              setState(() {
                _columnVisibility[column] = !(_columnVisibility[column] ?? true);
              });
            },
            itemBuilder: (BuildContext context) {
              return [
                'name',
                'position',
                'department',
                'email',
                'phone',
                'salary',
              ].map((String column) {
                return PopupMenuItem<String>(
                  value: column,
                  child: CheckboxListTile(
                    value: _columnVisibility[column] ?? true,
                    title: Text(_getColumnDisplayName(column, l10n)),
                    onChanged: (bool? value) {
                      setState(() {
                        _columnVisibility[column] = value ?? true;
                        Navigator.pop(context);
                      });
                    },
                    controlAffinity: ListTileControlAffinity.leading,
                  ),
                );
              }).toList();
            },
          ),
          // Export menu
          PopupMenuButton<String>(
            icon: const Icon(Icons.download),
            tooltip: _export,
            onSelected: (String action) {
              if (action == 'pdf') {
                _exportToPdf();
              } else if (action == 'csv') {
                _exportToCsv();
              }
            },
            itemBuilder: (BuildContext context) {
              return [
                PopupMenuItem<String>(
                  value: 'pdf',
                  child: Row(
                    children: [
                      Icon(Icons.picture_as_pdf, color: Theme.of(context).colorScheme.error), // Replaced Colors.red
                      const SizedBox(width: 8),
                      Text('PDF'),
                    ],
                  ),
                ),
                PopupMenuItem<String>(
                  value: 'csv',
                  child: Row(
                    children: [
                      Icon(Icons.table_chart, color: ModernAppColors.success), // Replaced Colors.green
                      const SizedBox(width: 8),
                      Text('CSV'),
                    ],
                  ),
                ),
              ];
            },
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: _refresh,
            onPressed: _loadEmployees,
          ),
        ],
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(60),
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: _searchEmployees,
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          setState(() {
                            _searchQuery = '';
                            _filterAndSortEmployees();
                          });
                        },
                      )
                    : null,
                filled: true,
                fillColor: Theme.of(context).colorScheme.surface, // Replaced Colors.white
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide: BorderSide.none,
                ),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                  _filterAndSortEmployees();
                  _currentPage = 0; // Reset to first page when searching
                });
              },
            ),
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _filteredEmployees.isEmpty
              ? Center(
                  child: Text(
                    _searchQuery.isEmpty
                        ? _noEmployeesFound
                        : _noMatchingEmployeesFound,
                    style: const TextStyle(fontSize: 16),
                  ),
                )
              : Column(
                  children: <Widget>[
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                      child: SliderTheme(
                        data: SliderTheme.of(context).copyWith(
                          trackHeight: 2.0,
                          thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 6.0),
                          overlayShape: const RoundSliderOverlayShape(overlayRadius: 14.0),
                          activeTrackColor: Theme.of(context).primaryColor,
                          inactiveTrackColor: Theme.of(context).disabledColor.withOpacity(0.3), // Replaced Colors.grey[300]
                          thumbColor: Theme.of(context).primaryColor,
                        ),
                        child: Slider(
                          value: _scrollProgress.clamp(0.0, 1.0),
                          onChanged: (value) {
                            if (_horizontalScrollController.hasClients) {
                              _horizontalScrollController.jumpTo(
                                value * _horizontalScrollController.position.maxScrollExtent,
                              );
                            }
                          },
                        ),
                      ),
                    ),
                    Expanded(
                      child: SingleChildScrollView(
                        controller: _horizontalScrollController,
                        scrollDirection: Axis.horizontal,
                        child: SizedBox(
                          width: 1200,
                          child: DataTable2(
                          columnSpacing: 12,
                          horizontalMargin: 12,
                          minWidth: 600,
                          sortColumnIndex: [
                            'name',
                            'position',
                            'department',
                            'email',
                            'phone',
                            'salary',
                          ].indexWhere((col) => col == _sortColumn),
                          sortAscending: _sortAscending,
                          columns: [
                          if (_columnVisibility['name'] ?? true)
                            DataColumn2(
                              label: Text(_name),
                              size: ColumnSize.L,
                              onSort: (_, __) => _sort('name'),
                            ),
                          if (_columnVisibility['position'] ?? true)
                            DataColumn2(
                              label: Text(_position),
                              size: ColumnSize.M,
                              onSort: (_, __) => _sort('position'),
                            ),
                          if (_columnVisibility['department'] ?? true)
                            DataColumn2(
                              label: Text(_department),
                              size: ColumnSize.M,
                              onSort: (_, __) => _sort('department'),
                            ),
                          if (_columnVisibility['email'] ?? true)
                            DataColumn2(
                              label: Text(_email),
                              size: ColumnSize.L,
                              onSort: (_, __) => _sort('email'),
                            ),
                          if (_columnVisibility['phone'] ?? true)
                            DataColumn2(
                              label: Text(_phone),
                              size: ColumnSize.M,
                              onSort: (_, __) => _sort('phone'),
                            ),
                          if (_columnVisibility['salary'] ?? true)
                            DataColumn2(
                              label: Text(_salary),
                              numeric: true,
                              size: ColumnSize.S,
                              onSort: (_, __) => _sort('salary'),
                            ),
                          const DataColumn2(
                            label: Text('Actions'),
                            size: ColumnSize.S,
                          ),
                        ],
                        rows: _paginatedEmployees.map((employee) {
                          return DataRow2(
                            cells: [
                              if (_columnVisibility['name'] ?? true)
                                DataCell(Text(employee['name'] ?? _noName)),
                              if (_columnVisibility['position'] ?? true)
                                DataCell(Text(employee['position'] ?? _noPosition)),
                              if (_columnVisibility['department'] ?? true)
                                DataCell(Text(employee['department'] ?? _noDepartment)),
                              if (_columnVisibility['email'] ?? true)
                                DataCell(Text(employee['email'] ?? 'N/A')),
                              if (_columnVisibility['phone'] ?? true)
                                DataCell(Text(employee['phone'] ?? 'N/A')),
                              if (_columnVisibility['salary'] ?? true)
                                DataCell(Text(employee['salary']?.toString() ?? 'N/A')),
                              DataCell(
                                Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    IconButton(
                                      icon: Icon(Icons.edit, color: Theme.of(context).colorScheme.primary, size: 20), // Replaced Colors.blue
                                      onPressed: () async {
                                        await Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                            builder: (context) => EmployeeManagementScreen(
                                              employee: employee,
                                            ),
                                          ),
                                        );
                                        _loadEmployees();
                                      },
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          );
                        }).toList(),
                      ),
                    ),
                  ),
                ),
                // Pagination controls
                    Container(
                      padding: const EdgeInsets.all(8.0),
                      color: Theme.of(context).colorScheme.surfaceVariant, // Replaced Colors.grey[200]
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              Text('${_rowsPerPageText}: '),
                              DropdownButton<int>(
                                value: _rowsPerPage,
                                items: _availableRowsPerPage.map((int value) {
                                  return DropdownMenuItem<int>(
                                    value: value,
                                    child: Text(value.toString()),
                                  );
                                }).toList(),
                                onChanged: (int? value) {
                                  if (value != null) {
                                    setState(() {
                                      _rowsPerPage = value;
                                      _currentPage = 0; // Reset to first page
                                    });
                                  }
                                },
                              ),
                            ],
                          ),
                          Text(
                            '${_currentPage * _rowsPerPage + 1}-${(_currentPage + 1) * _rowsPerPage > _filteredEmployees.length ? _filteredEmployees.length : (_currentPage + 1) * _rowsPerPage} ${_of} ${_filteredEmployees.length}',
                          ),
                          Row(
                            children: [
                              IconButton(
                                icon: const Icon(Icons.first_page),
                                onPressed: _currentPage > 0
                                    ? () => setState(() => _currentPage = 0)
                                    : null,
                              ),
                              IconButton(
                                icon: const Icon(Icons.chevron_left),
                                onPressed: _currentPage > 0
                                    ? () => setState(() => _currentPage--)
                                    : null,
                              ),
                              IconButton(
                                icon: const Icon(Icons.chevron_right),
                                onPressed: _currentPage < _pageCount - 1
                                    ? () => setState(() => _currentPage++)
                                    : null,
                              ),
                              IconButton(
                                icon: const Icon(Icons.last_page),
                                onPressed: _currentPage < _pageCount - 1
                                    ? () => setState(() => _currentPage = _pageCount - 1)
                                    : null,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () async {
          await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const EmployeeManagementScreen(),
            ),
          );
          _loadEmployees();
        },
        icon: const Icon(Icons.add),
        label: Text(_addEmployee),
      ),
    );
  }
  
  String _getColumnDisplayName(String column, AppLocalizations l10n) {
    switch (column) {
      case 'name':
        return _name;
      case 'position':
        return _position;
      case 'department':
        return _department;
      case 'email':
        return _email;
      case 'phone':
        return _phone;
      case 'salary':
        return _salary;
      default:
        return column;
    }
  }
}