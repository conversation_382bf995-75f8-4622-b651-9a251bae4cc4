// ignore_for_file: library_private_types_in_public_api, use_build_context_synchronously, unused_element, deprecated_member_use, curly_braces_in_flow_control_structures
import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class EmployeeManagementScreen extends StatefulWidget {
  final String? id;
  final Map<String, dynamic>? employee;

  const EmployeeManagementScreen({Key? key, this.id, this.employee}) : super(key: key);

  @override
  _EmployeeManagementScreenState createState() => _EmployeeManagementScreenState();
}

class _EmployeeManagementScreenState extends State<EmployeeManagementScreen> {
  final _formKey = GlobalKey<FormState>();

  // Personal Information
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _mobileController = TextEditingController();
  final TextEditingController _childrenController = TextEditingController();
  String _gender = 'Male';
  DateTime? _dateOfBirth;

  // Employment Information
  final TextEditingController _employeeCodeController = TextEditingController();
  final TextEditingController _positionController = TextEditingController();
  final TextEditingController _jobTitleController = TextEditingController();
  final TextEditingController _departmentController = TextEditingController();
  String _contractType = 'Permanent';
  DateTime? _startDate;
  DateTime? _contractDate;
  DateTime? _probationEndDate;
  DateTime? _contractEndDate;

  // Education Information
  final TextEditingController _universityController = TextEditingController();
  final TextEditingController _collegeController = TextEditingController();
  final TextEditingController _majorController = TextEditingController();
  final TextEditingController _graduationYearController = TextEditingController();
  final TextEditingController _gpaGradeController = TextEditingController();

  // Financial Information
  final TextEditingController _salaryAmountController = TextEditingController();
  final TextEditingController _accountNumberController = TextEditingController();
  final TextEditingController _ibanNumberController = TextEditingController();

  // Contact Information
  final TextEditingController _contactInfoController = TextEditingController();
  final TextEditingController _cityController = TextEditingController();
  final TextEditingController _streetController = TextEditingController();

  // Additional Information
  final TextEditingController _notesController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();

  bool _isLoading = false;
  bool _isEditing = false;

  @override
  void initState() {
    super.initState();
    if (widget.employee != null) {
      _isEditing = true;
      // Personal Information
      _nameController.text = widget.employee!['name'] ?? '';
      _emailController.text = widget.employee!['email_address'] ?? '';
      _mobileController.text = widget.employee!['mobile_number'] ?? '';
      _childrenController.text = widget.employee!['children']?.toString() ?? '';
      _gender = widget.employee!['gender'] ?? 'Male';
      _dateOfBirth = widget.employee!['date_of_birth'] != null
          ? DateTime.parse(widget.employee!['date_of_birth'])
          : null;

      // Employment Information
      _employeeCodeController.text = widget.employee!['employee_code'] ?? '';
      _positionController.text = widget.employee!['position'] ?? '';
      _jobTitleController.text = widget.employee!['job_title'] ?? '';
      _departmentController.text = widget.employee!['department'] ?? '';
      _contractType = widget.employee!['contract_type'] ?? 'Permanent';

      _startDate = widget.employee!['start_date'] != null
          ? DateTime.parse(widget.employee!['start_date'])
          : null;
      _contractDate = widget.employee!['contract_date'] != null
          ? DateTime.parse(widget.employee!['contract_date'])
          : null;
      _probationEndDate = widget.employee!['probation_end_date'] != null
          ? DateTime.parse(widget.employee!['probation_end_date'])
          : null;
      _contractEndDate = widget.employee!['contract_end_date'] != null
          ? DateTime.parse(widget.employee!['contract_end_date'])
          : null;

      // Education Information
      _universityController.text = widget.employee!['university'] ?? '';
      _collegeController.text = widget.employee!['college'] ?? '';
      _majorController.text = widget.employee!['major'] ?? '';
      _graduationYearController.text =
          widget.employee!['graduation_year']?.toString() ?? '';
      _gpaGradeController.text = widget.employee!['gpa_grade'] ?? '';

      // Financial Information
      _salaryAmountController.text =
          widget.employee!['salary_amount']?.toString() ?? '';
      _accountNumberController.text = widget.employee!['account_number'] ?? '';
      _ibanNumberController.text = widget.employee!['iban_number'] ?? '';

      // Contact Information
      _contactInfoController.text = widget.employee!['contact_info'] ?? '';
      _cityController.text = widget.employee!['city'] ?? '';
      _streetController.text = widget.employee!['street'] ?? '';

      // Additional Information
      _notesController.text = widget.employee!['notes'] ?? '';
    }
  }

  @override
  void dispose() {
    // Dispose all controllers
    _nameController.dispose();
    _emailController.dispose();
    _mobileController.dispose();
    _childrenController.dispose();
    _employeeCodeController.dispose();
    _positionController.dispose();
    _jobTitleController.dispose();
    _departmentController.dispose();
    _universityController.dispose();
    _collegeController.dispose();
    _majorController.dispose();
    _graduationYearController.dispose();
    _gpaGradeController.dispose();
    _salaryAmountController.dispose();
    _accountNumberController.dispose();
    _ibanNumberController.dispose();
    _contactInfoController.dispose();
    _cityController.dispose();
    _streetController.dispose();
    _notesController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _selectDate(BuildContext context, DateTime? initialDate,
      void Function(DateTime) onSelect) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: initialDate ?? DateTime.now(),
      firstDate: DateTime(1900),
      lastDate: DateTime(2100),
    );
    if (picked != null) {
      onSelect(picked);
    }
  }

  Future<void> _saveEmployee() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);
    try {
      final Map<String, dynamic> employeeData = {
        'name': _nameController.text.trim(),
        'email_address': _emailController.text.trim(),
        'mobile_number': _mobileController.text.trim(),
        'children': int.tryParse(_childrenController.text.trim()) ?? 0,
        'gender': _gender,
        'date_of_birth': _dateOfBirth?.toIso8601String(),
        'employee_code': _employeeCodeController.text.trim(),
        'position': _positionController.text.trim(),
        'job_title': _jobTitleController.text.trim(),
        'department': _departmentController.text.trim(),
        'contract_type': _contractType,
        'start_date': _startDate?.toIso8601String(),
        'contract_date': _contractDate?.toIso8601String(),
        'probation_end_date': _probationEndDate?.toIso8601String(),
        'contract_end_date': _contractEndDate?.toIso8601String(),
        'university': _universityController.text.trim(),
        'college': _collegeController.text.trim(),
        'major': _majorController.text.trim(),
        'graduation_year': int.tryParse(_graduationYearController.text.trim()),
        'gpa_grade': _gpaGradeController.text.trim(),
        'salary_amount': double.tryParse(_salaryAmountController.text.trim()),
        'account_number': _accountNumberController.text.trim(),
        'iban_number': _ibanNumberController.text.trim(),
        'contact_info': _contactInfoController.text.trim(),
        'city': _cityController.text.trim(),
        'street': _streetController.text.trim(),
        'notes': _notesController.text.trim(),
      };

      if (_isEditing && widget.employee != null) {
        employeeData['id'] = widget.employee!['id'].toString();
      }

      if (_passwordController.text.isNotEmpty) {
        employeeData['password_hash'] = _passwordController.text;
      }

      final response = await http.post(
        Uri.parse(_isEditing
            ? 'https://backend2.hemmasportacademy.com/employees/update_employee.php '
            : 'https://backend2.hemmasportacademy.com/employees/insert_employee.php '),
        headers: {'Content-Type': 'application/json'},
        body: json.encode(employeeData),
      );

      final responseData = json.decode(response.body);
      if (response.statusCode != 200 ||
          (responseData is Map && responseData['error'] != null)) {
        throw Exception(
            responseData is Map ? responseData['error'] : 'Failed to save employee');
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content:
            Text(_isEditing ? 'Employee updated successfully' : 'Employee created successfully')),
      );
      Navigator.pop(context, true);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: ${e.toString()}')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _deleteEmployee() async {
    if (!_isEditing) return;

    setState(() => _isLoading = true);
    try {
      final response = await http.post(
        Uri.parse('https://backend2.hemmasportacademy.com/employees/delete_employee.php '),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({'id': widget.employee!['id']}),
      );

      final responseData = json.decode(response.body);
      if (response.statusCode != 200 ||
          (responseData is Map && responseData['error'] != null)) {
        throw Exception(
            responseData is Map ? responseData['error'] : 'Failed to delete employee');
      }

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Employee deleted successfully')),
      );
      Navigator.pop(context, true);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: ${e.toString()}')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
    bool obscureText = false,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFFF8FAFC),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFE2E8F0)),
      ),
      child: TextFormField(
        controller: controller,
        keyboardType: keyboardType,
        validator: validator,
        obscureText: obscureText,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: Color(0xFF1F2937),
        ),
        decoration: InputDecoration(
          labelText: label,
          labelStyle: const TextStyle(
            color: Color(0xFF6B7280),
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
          prefixIcon: Container(
            margin: const EdgeInsets.all(12),
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color(0xFF5E72E4).withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: const Color(0xFF5E72E4),
              size: 20,
            ),
          ),
          border: InputBorder.none,
          enabledBorder: InputBorder.none,
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(
              color: Color(0xFF5E72E4),
              width: 2,
            ),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(
              color: Color(0xFFEF4444),
              width: 2,
            ),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(
              color: Color(0xFFEF4444),
              width: 2,
            ),
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 16,
          ),
          errorStyle: const TextStyle(
            color: Color(0xFFEF4444),
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Widget _buildDropdownField({
    required String label,
    required IconData icon,
    required String? value,
    required List<String> items,
    required void Function(String?) onChanged,
    String? Function(String?)? validator,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFFF8FAFC),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFE2E8F0)),
      ),
      child: DropdownButtonFormField<String>(
        value: value,
        onChanged: onChanged,
        validator: validator,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: Color(0xFF1F2937),
        ),
        decoration: InputDecoration(
          labelText: label,
          labelStyle: const TextStyle(
            color: Color(0xFF6B7280),
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
          prefixIcon: Container(
            margin: const EdgeInsets.all(12),
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color(0xFF5E72E4).withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: const Color(0xFF5E72E4),
              size: 20,
            ),
          ),
          border: InputBorder.none,
          enabledBorder: InputBorder.none,
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(
              color: Color(0xFF5E72E4),
              width: 2,
            ),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(
              color: Color(0xFFEF4444),
              width: 2,
            ),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(
              color: Color(0xFFEF4444),
              width: 2,
            ),
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 16,
          ),
          errorStyle: const TextStyle(
            color: Color(0xFFEF4444),
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
        items: items.map((String item) {
          return DropdownMenuItem<String>(
            value: item,
            child: Text(item),
          );
        }).toList(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      appBar: AppBar(
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                _isEditing ? Icons.edit_outlined : Icons.person_add_outlined,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            Text(
              localizations.employeeManagement,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                letterSpacing: 0.5,
              ),
            ),
          ],
        ),
        backgroundColor: const Color(0xFF5E72E4),
        foregroundColor: Colors.white,
        elevation: 0,
        toolbarHeight: 70,
        actions: [
          if (_isEditing)
            Container(
              margin: const EdgeInsets.only(right: 8),
              child: IconButton(
                icon: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: const Color(0xFFEF4444).withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(Icons.delete_outline, size: 20, color: Colors.white),
                ),
                onPressed: _isLoading
                    ? null
                    : () {
                        showDialog(
                          context: context,
                          builder: (context) => AlertDialog(
                            title: Text(localizations.confirmDelete),
                            content:
                                Text(localizations.deleteEmployeeConfirmation),
                            actions: [
                              TextButton(
                                onPressed: () => Navigator.pop(context),
                                child: Text(localizations.cancel),
                              ),
                              TextButton(
                                onPressed: () {
                                  Navigator.pop(context);
                                  _deleteEmployee();
                                },
                                child: Text(
                                  localizations.delete,
                                  style: TextStyle(
                                      color: Theme.of(context).colorScheme.error),
                                ),
                              ),
                            ],
                          ),
                        );
                      },
              ),
            ),
          Container(
            margin: const EdgeInsets.only(right: 16),
            child: IconButton(
              icon: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(Icons.save_outlined, size: 20),
              ),
              onPressed: _isLoading ? null : _saveEmployee,
            ),
          ),
        ],
      ),
      body: Stack(
        children: [
          Form(
            key: _formKey,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Card(
                    elevation: 2,
                    margin: const EdgeInsets.only(bottom: 16),
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12)),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            padding: const EdgeInsets.only(bottom: 16),
                            decoration: BoxDecoration(
                              border: Border(
                                bottom: BorderSide(
                                  color: Theme.of(context)
                                      .dividerColor
                                      .withOpacity(0.3),
                                ),
                              ),
                            ),
                            child: Row(
                              children: [
                                Icon(Icons.person_outline,
                                    color: Theme.of(context).primaryColor),
                                const SizedBox(width: 8),
                                Text(localizations.personalInfo,
                                    style: Theme.of(context).textTheme.titleLarge),
                              ],
                            ),
                          ),
                          // Personal Information fields
                          Row(
                            children: [
                              Expanded(
                                flex: 2,
                                child: TextFormField(
                                  controller: _nameController,
                                  decoration: InputDecoration(
                                    labelText: localizations.name,
                                    border: OutlineInputBorder(),
                                    contentPadding: EdgeInsets.symmetric(
                                        horizontal: 10, vertical: 15),
                                  ),
                                  validator: (value) =>
                                      value?.isEmpty ?? true
                                          ? localizations.required
                                          : null,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: DropdownButtonFormField<String>(
                                  value: _gender,
                                  decoration: InputDecoration(
                                    labelText: localizations.gender,
                                    border: OutlineInputBorder(),
                                    contentPadding: EdgeInsets.symmetric(
                                        horizontal: 10, vertical: 15),
                                  ),
                                  items: [localizations.male,
                                      localizations.female,
                                      localizations.other]
                                      .map<DropdownMenuItem<String>>(
                                          (String value) {
                                    return DropdownMenuItem<String>(
                                      value: value,
                                      child: Text(value),
                                    );
                                  }).toList(),
                                  onChanged: (value) =>
                                      setState(() => _gender = value!),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: TextFormField(
                                  controller: _mobileController,
                                  decoration: InputDecoration(
                                    labelText: '${localizations.mobileNumber}*',
                                    border: const OutlineInputBorder(),
                                    contentPadding: const EdgeInsets.symmetric(
                                        horizontal: 10, vertical: 15),
                                  ),
                                  keyboardType: TextInputType.phone,
                                  validator: (value) =>
                                      value?.isEmpty ?? true
                                          ? localizations.mobileNumberRequired
                                          : null,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),
                          Row(
                            children: [
                              Expanded(
                                child: TextFormField(
                                  controller: _emailController,
                                  decoration: InputDecoration(
                                    labelText: '${localizations.emailAddress}*',
                                    border: OutlineInputBorder(),
                                    contentPadding: EdgeInsets.symmetric(
                                        horizontal: 10, vertical: 15),
                                  ),
                                  keyboardType: TextInputType.emailAddress,
                                  validator: (value) {
                                    if (value?.isEmpty ?? true)
                                      return localizations.emailRequired;
                                    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
                                        .hasMatch(value!)) {
                                      return localizations.invalidEmail;
                                    }
                                    return null;
                                  },
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: InkWell(
                                  onTap: () => _selectDate(
                                    context,
                                    _dateOfBirth,
                                    (date) =>
                                        setState(() => _dateOfBirth = date),
                                  ),
                                  child: InputDecorator(
                                    decoration: InputDecoration(
                                      labelText: localizations.dateOfBirth,
                                      border: OutlineInputBorder(),
                                      contentPadding: EdgeInsets.symmetric(
                                          horizontal: 10, vertical: 15),
                                    ),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(_dateOfBirth
                                            ?.toString()
                                            .split(' ')[0] ??
                                            localizations.notSet),
                                        const Icon(Icons.calendar_today,
                                            size: 20),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: TextFormField(
                                  controller: _childrenController,
                                  decoration: InputDecoration(
                                    labelText: localizations.numberOfChildren,
                                    border: OutlineInputBorder(),
                                    contentPadding: EdgeInsets.symmetric(
                                        horizontal: 10, vertical: 15),
                                  ),
                                  keyboardType: TextInputType.number,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                  Card(
                    elevation: 2,
                    margin: const EdgeInsets.only(bottom: 16),
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12)),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            padding: const EdgeInsets.only(bottom: 16),
                            decoration: BoxDecoration(
                              border: Border(
                                bottom: BorderSide(
                                  color: Theme.of(context)
                                      .dividerColor
                                      .withOpacity(0.3),
                                ),
                              ),
                            ),
                            child: Row(
                              children: [
                                Icon(Icons.work_outline,
                                    color: Theme.of(context).primaryColor),
                                const SizedBox(width: 8),
                                Text(localizations.employmentInfo,
                                    style: Theme.of(context).textTheme.titleLarge),
                              ],
                            ),
                          ),
                          const SizedBox(height: 12),
                          // Employment Information fields
                          Row(
                            children: [
                              Expanded(
                                child: TextFormField(
                                  controller: _employeeCodeController,
                                  decoration: InputDecoration(
                                    labelText: localizations.employeeCode,
                                    border: OutlineInputBorder(),
                                    contentPadding: EdgeInsets.symmetric(
                                        horizontal: 10, vertical: 15),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: TextFormField(
                                  controller: _positionController,
                                  decoration: InputDecoration(
                                    labelText: localizations.position,
                                    border: OutlineInputBorder(),
                                    contentPadding: EdgeInsets.symmetric(
                                        horizontal: 10, vertical: 15),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: TextFormField(
                                  controller: _jobTitleController,
                                  decoration: InputDecoration(
                                    labelText: localizations.jobTitle,
                                    border: OutlineInputBorder(),
                                    contentPadding: EdgeInsets.symmetric(
                                        horizontal: 10, vertical: 15),
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),
                          Row(
                            children: [
                              Expanded(
                                child: TextFormField(
                                  controller: _departmentController,
                                  decoration: InputDecoration(
                                    labelText: localizations.department,
                                    border: OutlineInputBorder(),
                                    contentPadding: EdgeInsets.symmetric(
                                        horizontal: 10, vertical: 15),
                                  ),
                                )),
                              const SizedBox(width: 8),
                              Expanded(
                                child: DropdownButtonFormField<String>(
                                  value: _contractType,
                                  decoration: InputDecoration(
                                    labelText: localizations.contractType,
                                    border: OutlineInputBorder(),
                                    contentPadding: EdgeInsets.symmetric(
                                        horizontal: 10, vertical: 15),
                                  ),
                                  items: [localizations.permanent,
                                      localizations.temporary,
                                      localizations.intern,
                                      localizations.other]
                                      .map<DropdownMenuItem<String>>(
                                          (String value) {
                                    return DropdownMenuItem<String>(
                                      value: value,
                                      child: Text(value),
                                    );
                                  }).toList(),
                                  onChanged: (value) =>
                                      setState(() => _contractType = value!),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: InkWell(
                                  onTap: () => _selectDate(context, _startDate,
                                      (date) =>
                                          setState(() => _startDate = date)),
                                  child: InputDecorator(
                                    decoration: InputDecoration(
                                      labelText: localizations.startDate,
                                      border: OutlineInputBorder(),
                                      contentPadding: EdgeInsets.symmetric(
                                          horizontal: 10, vertical: 15),
                                    ),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(_startDate
                                                ?.toString()
                                                .split(' ')[0] ??
                                            localizations.notSet),
                                        const Icon(Icons.calendar_today,
                                            size: 20),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),
                          Row(children: [
                            Expanded(
                              child: InkWell(
                                onTap: () => _selectDate(
                                    context, _contractDate, (date) => setState(() => _contractDate = date)),
                                child: InputDecorator(
                                  decoration: InputDecoration(
                                    labelText: localizations.contractDate,
                                    border: OutlineInputBorder(),
                                    contentPadding: EdgeInsets.symmetric(horizontal: 10, vertical: 15),
                                  ),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(_contractDate?.toString().split(' ')[0] ?? localizations.notSet),
                                      const Icon(Icons.calendar_today, size: 20),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: InkWell(
                                onTap: () => _selectDate(context, _probationEndDate, (date) => setState(() => _probationEndDate = date)),
                                child: InputDecorator(
                                  decoration: InputDecoration(
                                    labelText: localizations.probationEndDate,
                                    border: OutlineInputBorder(),
                                    contentPadding: EdgeInsets.symmetric(horizontal: 10, vertical: 15),
                                  ),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(_probationEndDate?.toString().split(' ')[0] ?? localizations.notSet),
                                      const Icon(Icons.calendar_today, size: 20),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: InkWell(
                                onTap: () => _selectDate(context, _contractEndDate, (date) => setState(() => _contractEndDate = date)),
                                child: InputDecorator(
                                  decoration: InputDecoration(
                                    labelText: localizations.contractEndDate,
                                    border: OutlineInputBorder(),
                                    contentPadding: EdgeInsets.symmetric(horizontal: 10, vertical: 15),
                                  ),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(_contractEndDate?.toString().split(' ')[0] ?? localizations.notSet),
                                      const Icon(Icons.calendar_today, size: 20),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ]),
                          const SizedBox(height: 12),
                        ],
                      ),
                    ),
                  ),
                  Card(
                    elevation: 2,
                    margin: const EdgeInsets.only(bottom: 16),
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            padding: const EdgeInsets.only(bottom: 16),
                            decoration: BoxDecoration(
                              border: Border(
                                bottom: BorderSide(
                                  color: Theme.of(context).dividerColor.withOpacity(0.3),
                                ),
                              ),
                            ),
                            child: Row(
                              children: [
                                Icon(Icons.school_outlined, color: Theme.of(context).primaryColor),
                                const SizedBox(width: 8),
                                Text(localizations.educationInfo, style: Theme.of(context).textTheme.titleLarge),
                              ],
                            ),
                          ),
                          const SizedBox(height: 12),
                          Row(
                            children: [
                              Expanded(
                                flex: 2,
                                child: TextFormField(
                                  controller: _universityController,
                                  decoration: InputDecoration(
                                    labelText: localizations.university,
                                    border: OutlineInputBorder(),
                                    contentPadding: EdgeInsets.symmetric(horizontal: 10, vertical: 15),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                flex: 2,
                                child: TextFormField(
                                  controller: _collegeController,
                                  decoration: InputDecoration(
                                    labelText: localizations.college,
                                    border: OutlineInputBorder(),
                                    contentPadding: EdgeInsets.symmetric(horizontal: 10, vertical: 15),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: TextFormField(
                                  controller: _majorController,
                                  decoration: InputDecoration(
                                    labelText: localizations.major,
                                    border: OutlineInputBorder(),
                                    contentPadding: EdgeInsets.symmetric(horizontal: 10, vertical: 15),
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),
                          Row(
                            children: [
                              Expanded(
                                child: TextFormField(
                                  controller: _graduationYearController,
                                  decoration: InputDecoration(
                                    labelText: localizations.graduationYear,
                                    border: OutlineInputBorder(),
                                    contentPadding: EdgeInsets.symmetric(horizontal: 10, vertical: 15),
                                  ),
                                  keyboardType: TextInputType.number,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: TextFormField(
                                  controller: _gpaGradeController,
                                  decoration: InputDecoration(
                                    labelText: localizations.gpaGrade,
                                    border: OutlineInputBorder(),
                                    contentPadding: EdgeInsets.symmetric(horizontal: 10, vertical: 15),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              const Expanded(child: SizedBox()),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                  Card(
                    elevation: 2,
                    margin: const EdgeInsets.only(bottom: 16),
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            padding: const EdgeInsets.only(bottom: 16),
                            decoration: BoxDecoration(
                              border: Border(
                                bottom: BorderSide(
                                  color: Theme.of(context).dividerColor.withOpacity(0.3),
                                ),
                              ),
                            ),
                            child: Row(
                              children: [
                                Icon(Icons.account_balance_outlined, color: Theme.of(context).primaryColor),
                                const SizedBox(width: 8),
                                Text(localizations.financialInfo, style: Theme.of(context).textTheme.titleLarge),
                              ],
                            ),
                          ),
                          const SizedBox(height: 12),
                          Row(
                            children: [
                              Expanded(
                                child: TextFormField(
                                  controller: _salaryAmountController,
                                  decoration: InputDecoration(
                                    labelText: localizations.salaryAmount,
                                    border: OutlineInputBorder(),
                                    contentPadding: EdgeInsets.symmetric(horizontal: 10, vertical: 15),
                                  ),
                                  keyboardType: TextInputType.number,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                flex: 2,
                                child: TextFormField(
                                  controller: _accountNumberController,
                                  decoration: InputDecoration(
                                    labelText: localizations.accountNumber,
                                    border: OutlineInputBorder(),
                                    contentPadding: EdgeInsets.symmetric(horizontal: 10, vertical: 15),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                flex: 2,
                                child: TextFormField(
                                  controller: _ibanNumberController,
                                  decoration: InputDecoration(
                                    labelText: localizations.ibanNumber,
                                    border: OutlineInputBorder(),
                                    contentPadding: EdgeInsets.symmetric(horizontal: 10, vertical: 15),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                  Card(
                    elevation: 2,
                    margin: const EdgeInsets.only(bottom: 16),
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            padding: const EdgeInsets.only(bottom: 16),
                            decoration: BoxDecoration(
                              border: Border(
                                bottom: BorderSide(
                                  color: Theme.of(context).dividerColor.withOpacity(0.3),
                                ),
                              ),
                            ),
                            child: Row(
                              children: [
                                Icon(Icons.contact_mail_outlined, color: Theme.of(context).primaryColor),
                                const SizedBox(width: 8),
                                Text(localizations.contactInfo, style: Theme.of(context).textTheme.titleLarge),
                              ],
                            ),
                          ),
                          const SizedBox(height: 12),
                          Row(
                            children: [
                              Expanded(
                                child: TextFormField(
                                  controller: _cityController,
                                  decoration: InputDecoration(
                                    labelText: localizations.city,
                                    border: OutlineInputBorder(),
                                    contentPadding: EdgeInsets.symmetric(horizontal: 10, vertical: 15),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: TextFormField(
                                  controller: _streetController,
                                  decoration: InputDecoration(
                                    labelText: localizations.street,
                                    border: OutlineInputBorder(),
                                    contentPadding: EdgeInsets.symmetric(horizontal: 10, vertical: 15),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              const Expanded(child: SizedBox()),
                            ],
                          ),
                          const SizedBox(height: 16),
                          TextFormField(
                            controller: _contactInfoController,
                            decoration: InputDecoration(
                              labelText: localizations.additionalContactInfo,
                              border: OutlineInputBorder(),
                            ),
                            maxLines: 2,
                          ),
                        ],
                      ),
                    ),
                  ),
                  Card(
                    elevation: 2,
                    margin: const EdgeInsets.only(bottom: 16),
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            padding: const EdgeInsets.only(bottom: 16),
                            decoration: BoxDecoration(
                              border: Border(
                                bottom: BorderSide(
                                  color: Theme.of(context).dividerColor.withOpacity(0.3),
                                ),
                              ),
                            ),
                            child: Row(
                              children: [
                                Icon(Icons.note_outlined, color: Theme.of(context).primaryColor),
                                const SizedBox(width: 8),
                                Text(localizations.additionalInfo, style: Theme.of(context).textTheme.titleLarge),
                              ],
                            ),
                          ),
                          const SizedBox(height: 16),
                          TextFormField(
                            controller: _notesController,
                            decoration: InputDecoration(
                              labelText: localizations.notes,
                              border: OutlineInputBorder(),
                            ),
                            maxLines: 3,
                          ),
                          const SizedBox(height: 16),
                          if (!_isEditing)
                            TextFormField(
                              controller: _passwordController,
                              decoration: InputDecoration(
                                labelText: localizations.password,
                                border: OutlineInputBorder(),
                              ),
                              obscureText: true,
                              validator: (value) => !_isEditing && (value?.isEmpty ?? true)
                                  ? localizations.passwordRequired
                                  : null,
                            ),
                          const SizedBox(height: 32),
                          ElevatedButton(
                            onPressed: _isLoading ? null : _saveEmployee,
                            style: ElevatedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 16),
                            ),
                            child: _isLoading
                                ? const CircularProgressIndicator()
                                : Text(_isEditing
                                    ? localizations.updateEmployee
                                    : localizations.createEmployee),
                          ),
                          const SizedBox(height: 16),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          if (_isLoading)
            Container(
              color: Colors.black26,
              width: double.infinity,
              height: double.infinity,
              child: const Center(
                child: CircularProgressIndicator(),
              ),
            ),
        ],
      ),
    );
  }
}