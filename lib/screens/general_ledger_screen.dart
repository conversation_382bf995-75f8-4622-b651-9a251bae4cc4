// ignore_for_file: library_private_types_in_public_api, use_build_context_synchronously, unused_field, prefer_final_fields, deprecated_member_use, unused_local_variable, avoid_web_libraries_in_flutter

import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:share_plus/share_plus.dart';
import 'dart:html' as html; // For web CSV download
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:pdf/pdf.dart';

class GeneralLedgerScreen extends StatefulWidget {
  const GeneralLedgerScreen({Key? key}) : super(key: key);

  @override
  _GeneralLedgerScreenState createState() => _GeneralLedgerScreenState();
}

class _GeneralLedgerScreenState extends State<GeneralLedgerScreen> {
  bool _isLoading = false;
  List<Map<String, dynamic>> _allTransactions = [];
  List<Map<String, dynamic>> _filteredTransactions = [];
  final _currencyFormat = NumberFormat.currency(symbol: ' ﷼ ');
  String _searchText = '';

  // Sorting
  int? _sortColumnIndex;
  bool _sortAscending = true;

  // Pagination
  int _rowsPerPage = 10;
  final List<int> _availableRowsPerPage = [5, 10, 20, 50];
  int _currentPage = 0;

  // Column Visibility
  Map<String, bool> _columnVisibility = {
    'date': true,
    'description': true,
    'debit': true,
    'credit': true,
    'balance': true,
    'type': true,
  };

  @override
  void initState() {
    super.initState();
    _loadAllData();
  }

  Future<void> _loadAllData() async {
    setState(() => _isLoading = true);
    List<Map<String, dynamic>> bookingsData = await _loadStadiumBookings();
    List<Map<String, dynamic>> subscriptionsData = await _loadSubscriptions();

    List<Map<String, dynamic>> combinedData = [];
    double currentBalance = 0;

    // Process stadium bookings
    for (var booking in bookingsData) {
      if (booking['booking_status'] == 'confirmed') {
        final date = booking['booking_date'];
        final amount = double.tryParse(booking['booking_fee'].toString()) ?? 0.0;
        currentBalance += amount;
        combinedData.add({
          'date': date,
          'description': 'Stadium Booking - ${booking['stadium_name']}',
          'debit': amount,
          'credit': 0.0,
          'balance': currentBalance,
          'type': 'Booking',
        });
      }
    }

    // Process subscriptions and uniforms
    for (var customer in subscriptionsData) {
      final date = customer['payment_created_at'].split(' ')[0]; // Get just the date part
      final subscriptionAmount = double.tryParse(customer['amount'].toString()) ?? 0.0;
      final uniformAmount = double.tryParse(customer['uniform_price'].toString()) ?? 0.0;

      if (subscriptionAmount > 0) {
        currentBalance += subscriptionAmount;
        combinedData.add({
          'date': date,
          'description': 'Subscription - ${customer['customer_name']}',
          'debit': subscriptionAmount,
          'credit': 0.0,
          'balance': currentBalance,
          'type': 'Subscription',
        });
      }
      if (uniformAmount > 0) {
        currentBalance += uniformAmount;
        combinedData.add({
          'date': date,
          'description': 'Uniform - ${customer['customer_name']}',
          'debit': uniformAmount,
          'credit': 0.0,
          'balance': currentBalance,
          'type': 'Uniform',
        });
      }
    }

    // Sort by date descending initially
    combinedData.sort((a, b) => b['date'].compareTo(a['date']));

    // Recalculate balance based on sorted order (chronological for balance calculation)
    // For a true general ledger, debits and credits would affect balance differently.
    // This example assumes all are income for simplicity of initial balance calculation.
    // For a real GL, you'd have accounts and proper debit/credit rules.
    double runningBalance = 0;
    List<Map<String, dynamic>> finalDataWithCorrectBalance = [];
    // Sort by date ascending to calculate running balance correctly
    combinedData.sort((a, b) => a['date'].compareTo(b['date']));
    for (var transaction in combinedData) {
        if (transaction['debit'] != null && transaction['debit'] > 0) {
            runningBalance += transaction['debit'];
        }
        // Assuming credits reduce balance, or represent expenses/payments out
        // if (transaction['credit'] != null && transaction['credit'] > 0) {
        //     runningBalance -= transaction['credit'];
        // }
        finalDataWithCorrectBalance.add({...transaction, 'balance': runningBalance});
    }
    // Sort back to descending for display
    finalDataWithCorrectBalance.sort((a, b) => b['date'].compareTo(a['date']));


    setState(() {
      _allTransactions = finalDataWithCorrectBalance;
      _filteredTransactions = finalDataWithCorrectBalance;
      _isLoading = false;
    });
  }

  Future<List<Map<String, dynamic>>> _loadStadiumBookings() async {
    try {
      final response = await http.get(
        Uri.parse('https://backend2.hemmasportacademy.com/stadium_bookings/select_stadium_bookings.php'),
      );
      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((item) => Map<String, dynamic>.from(item)).toList();
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(AppLocalizations.of(context)!.errorLoadingStadiumBookings(e.toString()))),
      );
    }
    return [];
  }

  Future<List<Map<String, dynamic>>> _loadSubscriptions() async {
    try {
      final response = await http.get(
        Uri.parse('https://backend2.hemmasportacademy.com/gledger/select_customer.php'),
      );
      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((item) => Map<String, dynamic>.from(item)).toList();
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(AppLocalizations.of(context)!.errorLoadingSubscriptions(e.toString()))),
      );
    }
    return [];
  }

  void _filterTransactions(String query) {
    setState(() {
      _searchText = query.toLowerCase();
      _filteredTransactions = _allTransactions.where((transaction) {
        final date = transaction['date']?.toString().toLowerCase() ?? '';
        final description = transaction['description']?.toString().toLowerCase() ?? '';
        final type = transaction['type']?.toString().toLowerCase() ?? '';
        return date.contains(_searchText) ||
               description.contains(_searchText) ||
               type.contains(_searchText);
      }).toList();
      _currentPage = 0; // Reset to first page after filtering
    });
  }

  void _sort<T>(Comparable<T> Function(Map<String, dynamic> transaction) getField, int columnIndex, bool ascending) {
    _filteredTransactions.sort((a, b) {
      final aValue = getField(a);
      final bValue = getField(b);
      return ascending ? Comparable.compare(aValue, bValue) : Comparable.compare(bValue, aValue);
    });
    setState(() {
      _sortColumnIndex = columnIndex;
      _sortAscending = ascending;
    });
  }

  DataColumn _buildSortableColumn(String label, String key, {bool numeric = false}) {
    return DataColumn(
      label: Text(label),
      numeric: numeric,
      onSort: (columnIndex, ascending) {
        _sort<dynamic>((transaction) => transaction[key], columnIndex, ascending);
      },
    );
  }

  List<DataColumn> _getColumns(AppLocalizations localizations) {
    List<DataColumn> columns = [];
    if (_columnVisibility['date']!) columns.add(_buildSortableColumn(localizations.date, 'date'));
    if (_columnVisibility['description']!) columns.add(_buildSortableColumn(localizations.description, 'description'));
    if (_columnVisibility['debit']!) columns.add(_buildSortableColumn(localizations.debitText, 'debit', numeric: true));
    if (_columnVisibility['credit']!) columns.add(_buildSortableColumn(localizations.creditText, 'credit', numeric: true));
    if (_columnVisibility['balance']!) columns.add(_buildSortableColumn(localizations.balance, 'balance', numeric: true));
    if (_columnVisibility['type']!) columns.add(_buildSortableColumn(localizations.typeText, 'type'));
    return columns;
  }

  List<DataRow> _getRows() {
    final int firstRowIndex = _currentPage * _rowsPerPage;
    final int lastRowIndex = (_currentPage + 1) * _rowsPerPage;
    final List<Map<String, dynamic>> paginatedTransactions =
        _filteredTransactions.sublist(firstRowIndex, lastRowIndex > _filteredTransactions.length ? _filteredTransactions.length : lastRowIndex);

    return paginatedTransactions.map((transaction) {
      List<DataCell> cells = [];
      if (_columnVisibility['date']!) cells.add(DataCell(Text(transaction['date']?.toString() ?? '')));
      if (_columnVisibility['description']!) cells.add(DataCell(Text(transaction['description']?.toString() ?? '')));
      if (_columnVisibility['debit']!) cells.add(DataCell(Text(_currencyFormat.format(transaction['debit'] ?? 0.0))));
      if (_columnVisibility['credit']!) cells.add(DataCell(Text(_currencyFormat.format(transaction['credit'] ?? 0.0))));
      if (_columnVisibility['balance']!) cells.add(DataCell(Text(_currencyFormat.format(transaction['balance'] ?? 0.0))));
      if (_columnVisibility['type']!) cells.add(DataCell(Text(transaction['type']?.toString() ?? '')));
      return DataRow(cells: cells);
    }).toList();
  }

  void _showColumnVisibilityDialog(AppLocalizations localizations) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(localizations.selectColumns),
          content: StatefulBuilder(
            builder: (BuildContext context, StateSetter setStateDialog) {
              return SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: _columnVisibility.keys.map((String key) {
                    return CheckboxListTile(
                      title: Text(localizations.columnName(key)), // Assumes you have a way to localize column names
                      value: _columnVisibility[key],
                      onChanged: (bool? value) {
                        setStateDialog(() {
                          _columnVisibility[key] = value!;
                        });
                        setState(() {}); // Update the main table
                      },
                    );
                  }).toList(),
                ),
              );
            },
          ),
          actions: <Widget>[
            TextButton(
              child: Text(localizations.okText),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  Future<void> _exportToCsv(AppLocalizations localizations) async {
    List<List<dynamic>> rows = [];

    // Header Row
    List<dynamic> headerRow = [];
    if (_columnVisibility['date']!) headerRow.add(localizations.date);
    if (_columnVisibility['description']!) headerRow.add(localizations.description);
    if (_columnVisibility['debit']!) headerRow.add(localizations.debitText);
    if (_columnVisibility['credit']!) headerRow.add(localizations.creditText);
    if (_columnVisibility['balance']!) headerRow.add(localizations.balance);
    if (_columnVisibility['type']!) headerRow.add(localizations.typeText);
    rows.add(headerRow);

    // Data Rows
    for (var transaction in _filteredTransactions) {
      List<dynamic> row = [];
      if (_columnVisibility['date']!) row.add(transaction['date']?.toString() ?? '');
      if (_columnVisibility['description']!) row.add(transaction['description']?.toString() ?? '');
      if (_columnVisibility['debit']!) row.add(transaction['debit'] ?? 0.0);
      if (_columnVisibility['credit']!) row.add(transaction['credit'] ?? 0.0);
      if (_columnVisibility['balance']!) row.add(transaction['balance'] ?? 0.0);
      if (_columnVisibility['type']!) row.add(transaction['type']?.toString() ?? '');
      rows.add(row);
    }

    String csv = const ListToCsvConverter().convert(rows);

    // For web, trigger download
    if (kIsWeb) {
      final bytes = utf8.encode(csv);
      final blob = html.Blob([bytes]);
      final url = html.Url.createObjectUrlFromBlob(blob);
      final anchor = html.AnchorElement(href: url)
        ..setAttribute("download", "general_ledger.csv")
        ..click();
      html.Url.revokeObjectUrl(url);
    } else {
      // For mobile, use share_plus
      final XFile file = XFile.fromData(utf8.encode(csv), mimeType: 'text/csv', name: 'general_ledger.csv');
      await Share.shareXFiles([file], subject: localizations.generalLedgerExport);
    }
  }

  Future<void> _exportToPdf(AppLocalizations localizations) async {
    final pdf = pw.Document();

    List<String> headers = [];
    if (_columnVisibility['date']!) headers.add(localizations.date);
    if (_columnVisibility['description']!) headers.add(localizations.description);
    if (_columnVisibility['debit']!) headers.add(localizations.debitText);
    if (_columnVisibility['credit']!) headers.add(localizations.creditText);
    if (_columnVisibility['balance']!) headers.add(localizations.balance);
    if (_columnVisibility['type']!) headers.add(localizations.typeText);

    List<List<String>> data = _filteredTransactions.map((transaction) {
      List<String> row = [];
      if (_columnVisibility['date']!) row.add(transaction['date']?.toString() ?? '');
      if (_columnVisibility['description']!) row.add(transaction['description']?.toString() ?? '');
      if (_columnVisibility['debit']!) row.add(_currencyFormat.format(transaction['debit'] ?? 0.0));
      if (_columnVisibility['credit']!) row.add(_currencyFormat.format(transaction['credit'] ?? 0.0));
      if (_columnVisibility['balance']!) row.add(_currencyFormat.format(transaction['balance'] ?? 0.0));
      if (_columnVisibility['type']!) row.add(transaction['type']?.toString() ?? '');
      return row;
    }).toList();

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4.landscape,
        header: (pw.Context context) {
          return pw.Container(
            alignment: pw.Alignment.centerRight,
            margin: const pw.EdgeInsets.only(bottom: 3.0 * PdfPageFormat.mm),
            padding: const pw.EdgeInsets.only(bottom: 3.0 * PdfPageFormat.mm),
            decoration: const pw.BoxDecoration(
              border: pw.Border(bottom: pw.BorderSide(width: 0.5, color: PdfColors.grey)),
            ),
            child: pw.Text(localizations.generalLedger, style: pw.Theme.of(context).defaultTextStyle.copyWith(color: PdfColors.grey))
          );
        },
        build: (pw.Context context) => [
          pw.Header(level: 0, child: pw.Text(localizations.generalLedger, style: pw.TextStyle(fontSize: 20, fontWeight: pw.FontWeight.bold))),
          pw.Table.fromTextArray(
            headers: headers,
            data: data,
            border: pw.TableBorder.all(color: PdfColors.black, width: 0.5),
            headerStyle: pw.TextStyle(fontWeight: pw.FontWeight.bold),
            cellAlignment: pw.Alignment.centerLeft,
            headerDecoration: const pw.BoxDecoration(color: PdfColors.grey300),
            cellStyle: const pw.TextStyle(fontSize: 10),
            columnWidths: {
              for (var i = 0; i < headers.length; i++) i: const pw.IntrinsicColumnWidth(),
            }
          ),
        ],
      ),
    );

    await Printing.sharePdf(bytes: await pdf.save(), filename: 'general_ledger.pdf');
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    final int totalEntries = _filteredTransactions.length;
    final int totalPages = (totalEntries / _rowsPerPage).ceil();

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.generalLedger),
        actions: [
          IconButton(
            icon: const Icon(Icons.visibility),
            tooltip: localizations.selectColumns,
            onPressed: () => _showColumnVisibilityDialog(localizations),
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.file_download),
            tooltip: localizations.exportData,
            onSelected: (value) {
              if (value == 'pdf') _exportToPdf(localizations);
              if (value == 'csv') _exportToCsv(localizations);
            },
            itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
              PopupMenuItem<String>(
                value: 'pdf',
                child: Text(localizations.exportToPDF),
              ),
              PopupMenuItem<String>(
                value: 'csv',
                child: Text(localizations.exportToCSV),
              ),
            ],
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: localizations.refreshData,
            onPressed: _loadAllData,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: TextField(
                      decoration: InputDecoration(
                        labelText: localizations.search,
                        hintText: localizations.searchHintGL,
                        prefixIcon: const Icon(Icons.search),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8.0),
                        ),
                      ),
                      onChanged: _filterTransactions,
                    ),
                  ),
                  Expanded(
                    child: DataTable2(
                      columnSpacing: 12,
                      horizontalMargin: 12,
                      minWidth: 600,
                      sortColumnIndex: _sortColumnIndex,
                      sortAscending: _sortAscending,
                      columns: _getColumns(localizations),
                      rows: _getRows(),
                      empty: Center(
                        child: Text(localizations.noDataAvailable)
                      ),
                      headingRowHeight: 40,
                      dataRowHeight: 40,
                    ),
                  ),
                  if (totalEntries > 0)
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 16.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(localizations.rowsPerPage(_rowsPerPage.toString())),
                          DropdownButton<int>(
                            value: _rowsPerPage,
                            items: _availableRowsPerPage.map((int value) {
                              return DropdownMenuItem<int>(
                                value: value,
                                child: Text(value.toString()),
                              );
                            }).toList(),
                            onChanged: (int? newValue) {
                              if (newValue != null) {
                                setState(() {
                                  _rowsPerPage = newValue;
                                  _currentPage = 0; // Reset to first page
                                });
                              }
                            },
                          ),
                          Text(localizations.pageOutOfPages((_currentPage + 1).toString(), totalPages.toString())),
                          Row(
                            children: [
                              IconButton(
                                icon: const Icon(Icons.first_page),
                                onPressed: _currentPage > 0
                                    ? () => setState(() => _currentPage = 0)
                                    : null,
                              ),
                              IconButton(
                                icon: const Icon(Icons.chevron_left),
                                onPressed: _currentPage > 0
                                    ? () => setState(() => _currentPage--)
                                    : null,
                              ),
                              IconButton(
                                icon: const Icon(Icons.chevron_right),
                                onPressed: _currentPage < totalPages - 1
                                    ? () => setState(() => _currentPage++)
                                    : null,
                              ),
                              IconButton(
                                icon: const Icon(Icons.last_page),
                                onPressed: _currentPage < totalPages - 1
                                    ? () => setState(() => _currentPage = totalPages - 1)
                                    : null,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
    );
  }
}

// Helper for localization (you'll need to define these in your .arb files)
extension LocalizationHelper on AppLocalizations {
  String columnName(String key) {
    switch (key) {
      case 'date':
        return date;
      case 'description':
        return description;
      case 'debit':
        return debitText; // Use a new getter
      case 'credit':
        return creditText; // Use a new getter
      case 'balance':
        return balance;
      case 'type':
        return typeText; // Use a new getter
      default:
        return key;
    }
  }

  // Define the missing getters
  String get debitText => "Debit"; // Provide default or use actual localization key if available
  String get creditText => "Credit"; // Provide default or use actual localization key if available
  String get typeText => "Type"; // Provide default or use actual localization key if available
  String get okText => "OK"; // Provide default or use actual localization key if available

  // Define the missing methods
  String rowsPerPage(String count) => "Rows per page: $count"; // Provide default or use actual localization key
  String pageOutOfPages(String currentPage, String totalPages) => "Page $currentPage of $totalPages"; // Provide default or use actual localization key

  String get searchHintGL => 'Search by date, description, type...';
  String get generalLedgerExport => 'General Ledger Export';
  String get selectColumns => 'Select Columns';
  String get exportData => 'Export Data';
  String get exportToPDF => 'Export to PDF';
  String get exportToCSV => 'Export to CSV';
  String get refreshData => 'Refresh Data';
  String get noDataAvailable => 'No data available to display.';
  // Add other GL specific strings if needed
}

// Dummy ListToCsvConverter for the sake of example if not using a package
// In a real app, use a package like `csv`
class ListToCsvConverter {
  const ListToCsvConverter();
  String convert(List<List<dynamic>> list) {
    return list.map((row) => row.map((item) => '"${item.toString().replaceAll('"', '""')}"').join(',')).join('\n');
  }
}

// Add kIsWeb if not already globally available (Flutter web check)
const bool kIsWeb = identical(0, 0.0);