// ignore_for_file: library_private_types_in_public_api, unused_import, avoid_print, prefer_final_fields, unused_element

import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:hemmaerp/screens/stadium_booking_screen.dart';
import 'package:http/http.dart' as http;
import 'package:provider/provider.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:hemmaerp/providers/theme_provider.dart'; // Import ThemeProvider
import 'package:hemmaerp/widgets/controls/custom_switch.dart'; // Import CustomSwitch
import 'package:hemmaerp/layouts/dashboard_layout.dart';
import 'package:hemmaerp/providers/language_provider.dart';
import 'package:hemmaerp/screens/booking_list_screen.dart';
import 'package:hemmaerp/screens/employee_list_screen.dart';
import 'package:hemmaerp/screens/general_ledger_screen.dart';
import 'package:hemmaerp/screens/payroll_screen.dart';
import 'package:hemmaerp/screens/promocodes/promocode_screen.dart';
import 'package:hemmaerp/screens/sports_list_screen.dart';
import 'package:hemmaerp/screens/trainer_list_screen.dart';
import 'package:hemmaerp/widgets/cards/metric_card.dart';
import 'package:hemmaerp/widgets/cards/custom_card.dart';
import 'package:hemmaerp/widgets/cards/customer_card.dart';
import 'package:hemmaerp/widgets/cards/data_card.dart';
import 'package:hemmaerp/widgets/cards/employee_card.dart';
import 'package:hemmaerp/widgets/cards/info_card.dart';
import 'package:hemmaerp/widgets/cards/sport_card.dart';
import 'package:hemmaerp/widgets/cards/stat_card.dart';
import 'package:hemmaerp/widgets/cards/trainee_card.dart';
import 'package:hemmaerp/widgets/cards/transaction_card.dart';
import 'package:hemmaerp/widgets/charts/bar_chart.dart';
import 'package:hemmaerp/models/transaction.dart'; // Import Transaction model
import 'package:hemmaerp/models/trainee.dart'; // Import Trainee model
import 'package:hemmaerp/models/sport.dart'; // Import Sport model
import 'package:hemmaerp/models/employee.dart'; // Import Employee model
import 'package:hemmaerp/models/customers.dart'; // Import Customer model

import 'customer_list_screen_new.dart';
import 'manage_bookings_screen.dart';
import 'sports_schedule_screen.dart';
import 'stadium_registration_screen.dart';
import 'package:hemmaerp/screens/trainer_trainee_screen.dart';
import 'package:hemmaerp/screens/visit_booking_screen.dart';
import '../services/api_status_service.dart'; // Added for API Status
import '../services/api_service.dart';
import '../theme/modern_theme.dart';
import '../widgets/modern/modern_drawer.dart';
import 'modern_dashboard_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  String _currentScreen = 'Home';
  Widget _activeScreenWidget = const Center(
    child: Text('Welcome to Hemma ERP', style: TextStyle(fontSize: 24)),
  );

  late ApiStatusService _apiStatusService;
  final ApiService _apiService = ApiService();
  Map<ScreenIdentifier, ApiHealthStatus> _currentApiStatuses = {};

  // Track metrics over time
  Map<String, double> _previousMetrics = {};
  Map<String, double> _currentMetrics = {};

  @override
  void initState() {
    super.initState();
    _apiStatusService = ApiStatusService();
    _apiStatusService.addListener(_updateApiStatuses);
    _apiStatusService
        .forceRefreshAll(); // Corrected method call for initial fetch/refresh
  }

  @override
  void dispose() {
    _apiStatusService.removeListener(_updateApiStatuses);
    _apiStatusService.dispose();
    super.dispose();
  }

  void _updateApiStatuses() {
    if (mounted) {
      setState(() {
        _currentApiStatuses = Map.from(_apiStatusService.apiStatuses);
      });
    }
  }

  void _navigateToScreen(String screenName, Widget screen) {
    setState(() {
      _currentScreen = screenName;
      _activeScreenWidget = screen;
    });
  }

  // Helper to get screen data (name and widget) from ScreenIdentifier
  ({String name, Widget widget})? _getScreenData(
    ScreenIdentifier identifier,
    AppLocalizations localizations,
  ) {
    switch (identifier) {
      case ScreenIdentifier.manageBookings: // Corrected to camelCase
        return (
          name: localizations.manageBookings,
          widget: const ManageBookingsScreen(),
        );
      case ScreenIdentifier.customerList: // Corrected to camelCase
        return (
          name: localizations.customers,
          widget: const CustomerListScreenNew(),
        );
      case ScreenIdentifier.trainerList: // Corrected to camelCase
        return (
          name: localizations.trainers,
          widget: const TrainerListScreen(),
        );
      // Assuming SportsList and PromoCodes were intended to be monitored, adding them here with camelCase
      // Note: SportsList and PromoCodes were not in the original error list from user, but were in the previous version of _getScreenData
      // And they are not in the ScreenIdentifier enum in api_status_service.dart. They will be filtered out by _buildHomeScreenWidgets if not in screenApiEndpoints
      // For now, I will keep them as they were in the previous version of _getScreenData, but using camelCase if they were to exist in ScreenIdentifier.
      // However, since they are not in ScreenIdentifier enum, these cases will effectively be dead code unless ScreenIdentifier is updated.
      // The current ScreenIdentifier enum does not have sportsList or promoCodes.
      // Let's remove them to align with the provided ScreenIdentifier enum in api_status_service.dart
      case ScreenIdentifier.bookingList: // Corrected to camelCase
        return (
          name: localizations.bookings,
          widget: const BookingListScreen(),
        );
      case ScreenIdentifier.employeeList: // Corrected to camelCase
        return (
          name: localizations.employees,
          widget: const EmployeeListScreen(),
        );
      case ScreenIdentifier.payroll: // Corrected to camelCase
        return (name: localizations.payroll, widget: const PayrollScreen());
      case ScreenIdentifier.generalLedger: // Corrected to camelCase
        return (
          name: localizations.generalLedger,
          widget: const GeneralLedgerScreen(),
        );
      case ScreenIdentifier.visitBooking: // Corrected to camelCase
        return (
          name: localizations.visitBooking,
          widget: const VisitBookingScreen(),
        );
      case ScreenIdentifier.trainerTrainee: // Corrected to camelCase
        return (
          name: localizations.trainerTrainee,
          widget: const TrainerTraineeScreen(),
        );
      case ScreenIdentifier.sportsSchedule: // Corrected to camelCase
        return (
          name: localizations.sportsSchedule,
          widget: const SportsScheduleScreen(),
        );
      case ScreenIdentifier.stadiumRegistration: // Corrected to camelCase
        return (
          name: localizations.stadiumRegistration,
          widget: const StadiumRegistrationScreen(),
        );
      case ScreenIdentifier.stadiumBooking: // Added for StadiumBookingScreen
        return (
          name: localizations.stadiumBooking,
          widget: const StadiumBookingScreen(),
        );
      case ScreenIdentifier.allCardsAndCharts:
        return (
          name: localizations.allCardsAndCharts,
          widget: _buildAllCardsSection(context),
        );
    }
  }

  Future<Map<String, int>> _fetchScreenSummary(
    ScreenIdentifier screenIdentifier,
  ) async {
    try {
      if (screenIdentifier != ScreenIdentifier.customerList) return {};

      final responseData = await _apiService.fetchAllCustomerData();
      final List<dynamic> customers = responseData['customers'];
      final List<dynamic> payments = responseData['payments'];

      final activeCustomers =
          customers.where((c) => c['status'] == 'Active').length;
      final totalIncome = payments.fold(
        0.0,
        (sum, payment) =>
            sum + (double.tryParse(payment['amount']?.toString() ?? '0') ?? 0),
      );

      return {
        'Total': customers.length,
        'Active': activeCustomers,
        'Income': totalIncome.round(),
      };
    } catch (e) {
      print('Error fetching summary for $screenIdentifier: $e');
      return {};
    }
  }

  Widget _buildMetricCard(
    BuildContext context,
    ScreenIdentifier screenIdentifier,
  ) {
    final appLocalizations = AppLocalizations.of(context)!;
    final screenData = _getScreenData(screenIdentifier, appLocalizations);

    if (screenData == null) return const SizedBox.shrink();

    return FutureBuilder<Map<String, int>>(
      future: _fetchScreenSummary(screenIdentifier),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator(strokeWidth: 2));
        }

        final data = snapshot.data ?? {};
        final total = data['Total'] ?? 0;
        final active = data['Active'] ?? 0;
        final income = data['Income'] ?? 0;

        String subtitle = '';
        if (active > 0) subtitle += '$active Active';
        if (income > 0) {
          subtitle +=
              '${subtitle.isNotEmpty ? ' - ' : ''}\$${income.toString()}';
        }

        // Calculate percentage change
        final previousTotal =
            _previousMetrics[screenIdentifier.toString()] ?? 0;
        final percentageChange =
            previousTotal != 0
                ? ((total - previousTotal) / previousTotal) * 100
                : 0.0;

        // Update metrics
        _previousMetrics[screenIdentifier.toString()] =
            _currentMetrics[screenIdentifier.toString()] ?? 0;
        _currentMetrics[screenIdentifier.toString()] = total.toDouble();

        // Define card properties based on screen type with theme-aware colors
        final theme = Theme.of(context);
        IconData icon;
        Color color;
        switch (screenIdentifier) {
          case ScreenIdentifier.customerList:
            icon = Icons.people;
            color = theme.colorScheme.primary;
            break;
          case ScreenIdentifier.bookingList:
            icon = Icons.calendar_today;
            color = theme.colorScheme.secondary;
            break;
          case ScreenIdentifier.trainerList:
            icon = Icons.sports;
            color = theme.colorScheme.tertiary;
            break;
          case ScreenIdentifier.employeeList:
            icon = Icons.badge;
            color =
                theme.brightness == Brightness.dark
                    ? const Color(0xFF4ADE80) // Success green for dark theme
                    : const Color(0xFF4CAF50); // Success green for light theme
            break;
          default:
            icon = Icons.analytics;
            color = theme.colorScheme.onSurfaceVariant;
        }

        return MetricCard(
          title: screenData.name,
          value: total.toString(),
          icon: icon,
          iconColor: color,
          backgroundColor: color,
          percentageChange: percentageChange,
          subtitle: subtitle.isNotEmpty ? subtitle : null,
          onTap: () => _navigateToScreen(screenData.name, screenData.widget),
        );
      },
    );
  }

  Widget _buildHomeScreenWidgets() {
    final appLocalizations = AppLocalizations.of(context)!;
    final displayableScreenIdentifiers =
        ScreenIdentifier.values
            .where(
              (id) =>
                  _getScreenData(id, appLocalizations) != null
                  // Corrected access to screenApiEndpoints (it's a top-level const)
                  &&
                  screenApiEndpoints.containsKey(
                    id,
                  ), // Only show screens monitored by the service
            )
            .toList();

    if (displayableScreenIdentifiers.isEmpty) {
      // Corrected localization key usage
      return Center(child: Text(appLocalizations.noScreensToDisplayStatusFor));
    }
    if (_currentApiStatuses.isEmpty && _apiStatusService.apiStatuses.isEmpty) {
      return const Center(
        child: CircularProgressIndicator(),
      ); // Show loading while statuses are fetched
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        final width = constraints.maxWidth;
        final crossAxisCount =
            width > 1200
                ? 6
                : width > 800
                ? 4
                : width > 600
                ? 3
                : 2;

        return GridView.builder(
          padding: const EdgeInsets.all(8.0),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: crossAxisCount,
            childAspectRatio: 1.2,
            crossAxisSpacing: 8,
            mainAxisSpacing: 8,
          ),
          itemCount: displayableScreenIdentifiers.length,
          itemBuilder: (context, index) {
            final screenId = displayableScreenIdentifiers[index];
            return _buildMetricCard(context, screenId);
          },
        );
      },
    );
  }

  Widget _buildAllCardsSection(BuildContext context) {
    final appLocalizations = AppLocalizations.of(context)!;
    return FutureBuilder<List<Customer>>(
      future: _fetchCustomers(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        } else if (snapshot.hasError) {
          return Center(child: Text('Error: ${snapshot.error}'));
        } else {
          final customers = snapshot.data!;
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  appLocalizations.allCardsAndCharts,
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: 16.0),
                ...customers.map(
                  (customer) => Column(
                    children: [
                      CustomerCard(
                        customer: customer,
                        onTap: () {},
                        onEdit: () {},
                        onDelete: () {},
                      ),
                      const SizedBox(height: 16.0),
                    ],
                  ),
                ),
              ],
            ),
          );
        }
      },
    );
  }

  Future<List<Customer>> _fetchCustomers() async {
    try {
      final data = await _apiService.fetchAllCustomerData();
      return (data['customers'] as List)
          .map((json) => Customer.fromJson(json))
          .toList();
    } catch (e) {
      throw Exception('Failed to load customers: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final appLocalizations = AppLocalizations.of(context);

    return Scaffold(
      body:
          _currentScreen == 'Home'
              ? const ModernDashboardScreen()
              : _activeScreenWidget,
      appBar: AppBar(
        title: Text(
          _currentScreen == 'Home' && appLocalizations != null
              ? (appLocalizations.home)
              : _currentScreen,
        ),
        actions: [
          Consumer<ThemeProvider>(
            builder: (context, themeProvider, child) {
              return CustomSwitch(
                value: themeProvider.themeMode == ThemeMode.dark,
                onChanged: (value) {
                  themeProvider.toggleTheme(value);
                },
              );
            },
          ),
          PopupMenuButton<Locale>(
            icon: const Icon(Icons.language),
            onSelected: (Locale locale) {
              Provider.of<LanguageProvider>(
                context,
                listen: false,
              ).setLocale(locale);
            },
            itemBuilder:
                (BuildContext context) => <PopupMenuEntry<Locale>>[
                  PopupMenuItem<Locale>(
                    value: const Locale('en'),
                    child: Text(appLocalizations?.english ?? 'English'),
                  ),
                  PopupMenuItem<Locale>(
                    value: const Locale('ar'),
                    child: Text(appLocalizations?.arabic ?? 'العربية'),
                  ),
                ],
          ),
        ],
      ),
      drawer: ModernDrawer(
        userName: 'Hemma Sports Academy',
        userEmail: '<EMAIL>',
        isDarkMode: Theme.of(context).brightness == Brightness.dark,
        onThemeToggle: () {
          final themeProvider = Provider.of<ThemeProvider>(
            context,
            listen: false,
          );
          themeProvider.toggleTheme(themeProvider.themeMode == ThemeMode.light);
        },
        items: [
          ModernDrawerItem(
            icon: Icons.dashboard,
            title: appLocalizations?.home ?? 'Dashboard',
            onTap: () {
              Navigator.pop(context);
              setState(() {
                _currentScreen = 'Home';
                _activeScreenWidget = const ModernDashboardScreen();
              });
            },
            isSelected: _currentScreen == 'Home',
          ),
          ModernDrawerItem(
            icon: Icons.people,
            title: appLocalizations?.customers ?? 'Customers',
            onTap: () {
              Navigator.pop(context);
              _navigateToScreen('Customers', const CustomerListScreenNew());
            },
            isSelected: _currentScreen == 'Customers',
          ),
          ModernDrawerItem(
            icon: Icons.sports,
            title: appLocalizations?.trainers ?? 'Trainers',
            onTap: () {
              Navigator.pop(context);
              _navigateToScreen('Trainers', const TrainerListScreen());
            },
            isSelected: _currentScreen == 'Trainers',
          ),
          ModernDrawerItem(
            icon: Icons.calendar_today,
            title: appLocalizations?.bookings ?? 'Bookings',
            onTap: () {
              Navigator.pop(context);
              _navigateToScreen('Bookings', const BookingListScreen());
            },
            isSelected: _currentScreen == 'Bookings',
          ),
          ModernDrawerItem(
            icon: Icons.badge,
            title: appLocalizations?.employees ?? 'Employees',
            onTap: () {
              Navigator.pop(context);
              _navigateToScreen('Employees', const EmployeeListScreen());
            },
            isSelected: _currentScreen == 'Employees',
          ),
          ModernDrawerItem(
            icon: Icons.account_balance_wallet,
            title: appLocalizations?.payroll ?? 'Payroll',
            onTap: () {
              Navigator.pop(context);
              _navigateToScreen('Payroll', const PayrollScreen());
            },
            isSelected: _currentScreen == 'Payroll',
          ),
        ],
      ),
    );
  }
}
