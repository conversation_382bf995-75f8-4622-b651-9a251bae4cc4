// ignore_for_file: library_private_types_in_public_api, use_build_context_synchronously, avoid_print

import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:file_saver/file_saver.dart';
import 'package:csv/csv.dart';
import 'dart:typed_data'; // Required for Uint8List
import '../services/api_service.dart';

class ManageBookingsScreen extends StatefulWidget {
  const ManageBookingsScreen({Key? key}) : super(key: key);

  @override
  _ManageBookingsScreenState createState() => _ManageBookingsScreenState();
}

class _ManageBookingsScreenState extends State<ManageBookingsScreen> {
  bool _isLoading = false;
  List<Map<String, dynamic>> _bookings = [];
  List<Map<String, dynamic>> _filteredBookings = [];
  int _sortColumnIndex = 0;
  bool _sortAscending = true;
  final TextEditingController _searchController = TextEditingController();
  final ApiService _apiService = ApiService();

  @override
  void initState() {
    super.initState();
    _loadBookings();
    _searchController.addListener(_filterBookings);
  }

  @override
  void dispose() {
    _searchController.removeListener(_filterBookings);
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadBookings() async {
    setState(() => _isLoading = true);
    try {
      final data = await _apiService.getStadiumBookings();
      print('Loaded ${data.length} bookings from API');

      setState(() {
        _bookings =
            data.map((item) => Map<String, dynamic>.from(item)).toList();
        _filteredBookings = List.from(_bookings);
      });
    } catch (e) {
      print('Error loading bookings: $e');
      _loadSampleData(); // Load sample data if API call fails
    } finally {
      setState(() => _isLoading = false);
    }
  }

  // Load sample data for testing UI
  void _loadSampleData() {
    print('Loading sample data');
    setState(() {
      _bookings = [
        {
          'id': '1',
          'stadium_name': 'Main Stadium',
          'name': 'John Doe',
          'email': '<EMAIL>',
          'phone': '+1234567890',
          'booking_date': '2023-10-15',
          'time_slot': '18:00-19:00',
          'booking_status': 'confirmed',
        },
        {
          'id': '2',
          'stadium_name': 'Training Field',
          'name': 'Jane Smith',
          'email': '<EMAIL>',
          'phone': '+9876543210',
          'booking_date': '2023-10-16',
          'time_slot': '17:00-18:00',
          'booking_status': 'pending',
        },
        {
          'id': '3',
          'stadium_name': 'Indoor Court',
          'name': 'Ahmed Ali',
          'email': '<EMAIL>',
          'phone': '+2013456789',
          'booking_date': '2023-10-17',
          'time_slot': '19:00-20:00',
          'booking_status': 'cancelled',
        },
      ];
      _filteredBookings = List.from(_bookings);
    });
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(AppLocalizations.of(context)!.loadingSampleData)),
    );
  }

  void _filterBookings() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      _filteredBookings =
          _bookings.where((booking) {
            return (booking['stadium_name']?.toString().toLowerCase().contains(
                      query,
                    ) ??
                    false) ||
                (booking['name']?.toString().toLowerCase().contains(query) ??
                    false) ||
                (booking['email']?.toString().toLowerCase().contains(query) ??
                    false) ||
                (booking['phone']?.toString().toLowerCase().contains(query) ??
                    false) ||
                (booking['booking_date']?.toString().toLowerCase().contains(
                      query,
                    ) ??
                    false) ||
                (booking['time_slot']?.toString().toLowerCase().contains(
                      query,
                    ) ??
                    false) ||
                (booking['booking_status']?.toString().toLowerCase().contains(
                      query,
                    ) ??
                    false);
          }).toList();
    });
  }

  Future<void> _updateBookingStatus(String bookingId, String status) async {
    try {
      print('Sending update with ID: $bookingId and status: $status');

      final responseData = await _apiService.updateStadiumBooking({
        'id': bookingId,
        'booking_status': status,
      });

      if (responseData['success'] == true) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              '${AppLocalizations.of(context)!.bookings} ${status.toLowerCase()}',
            ),
          ),
        );
        await _loadBookings(); // Refresh the list
      } else {
        throw Exception(
          responseData['error'] ??
              '${AppLocalizations.of(context)!.bookings} ${AppLocalizations.of(context)!.updateFailed}',
        );
      }
    } catch (e) {
      print('Error updating booking: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            '${AppLocalizations.of(context)!.updateFailed}: ${e.toString()}',
          ),
        ),
      );
    }
  }

  void _sort<T>(
    Comparable<T> Function(Map<String, dynamic> booking) getField,
    int columnIndex,
    bool ascending,
  ) {
    _filteredBookings.sort((a, b) {
      final aValue = getField(a);
      final bValue = getField(b);
      return ascending
          ? Comparable.compare(aValue, bValue)
          : Comparable.compare(bValue, aValue);
    });
    setState(() {
      _sortColumnIndex = columnIndex;
      _sortAscending = ascending;
    });
  }

  Future<void> _exportBookingsToCsv() async {
    final l10n = AppLocalizations.of(context)!;
    if (_filteredBookings.isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text(l10n.noDataToExport)));
      return;
    }

    List<List<dynamic>> rows = [];
    // Add header row
    rows.add([
      l10n.stadiumNameColumn,
      l10n.customer,
      l10n.email,
      l10n.phone,
      l10n.date,
      l10n.time,
      l10n.status,
    ]);

    // Add data rows
    for (var booking in _filteredBookings) {
      rows.add([
        booking['stadium_name']?.toString() ?? '',
        booking['name']?.toString() ?? '',
        booking['email']?.toString() ?? '',
        booking['phone']?.toString() ?? '',
        booking['booking_date']?.toString() ?? '',
        booking['time_slot']?.toString() ?? '',
        booking['booking_status']?.toString() ?? '',
      ]);
    }

    String csvData = const ListToCsvConverter().convert(rows);
    final Uint8List bytes = Uint8List.fromList(
      utf8.encode(csvData),
    ); // Encode to UTF-8 bytes

    try {
      String? path = await FileSaver.instance.saveFile(
        name:
            "bookings_export_${DateTime.now().toIso8601String().split('T').first}", // e.g., bookings_export_2023-10-27
        bytes: bytes,
        ext: "csv",
        mimeType: MimeType.csv,
      );
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(l10n.exportedSuccessfullyTo(path))),
      );
    } catch (e) {
      print('Error exporting CSV: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(l10n.errorExportingCsv(e.toString()))),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final bookingDataSource = _BookingDataSource(
      bookings: _filteredBookings,
      l10n: l10n,
      onUpdateStatus: _updateBookingStatus,
      context: context,
    );

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.manageBookingsTitle),
        elevation: 2,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadBookings,
            tooltip: l10n.refreshBookings,
          ),
          IconButton(
            icon: const Icon(Icons.download_outlined),
            onPressed: _exportBookingsToCsv,
            tooltip: l10n.exportToCsv,
          ),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: TextField(
                      controller: _searchController,
                      decoration: InputDecoration(
                        labelText: l10n.searchBookings,
                        hintText: l10n.searchBookingsHint,
                        prefixIcon: const Icon(Icons.search),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8.0),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(
                    height: MediaQuery.of(context).size.height - 200,
                    child:
                        _filteredBookings.isEmpty &&
                                _searchController.text.isEmpty
                            ? Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.calendar_today,
                                    size: 64,
                                    color: Colors.grey[400],
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    l10n.noBookingsAvailable,
                                    style: TextStyle(
                                      fontSize: 18,
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                ],
                              ),
                            )
                            : SingleChildScrollView(
                              scrollDirection: Axis.horizontal,
                              child: SizedBox(
                                width: MediaQuery.of(context).size.width,
                                child: PaginatedDataTable(
                                  header: Text(
                                    l10n.manageBookingsTitle,
                                    style:
                                        Theme.of(context).textTheme.titleLarge,
                                  ),
                                  rowsPerPage:
                                      5, // Reduce rows per page to fit better
                                  availableRowsPerPage: const [5, 10, 20],
                                  sortAscending: _sortAscending,
                                  sortColumnIndex: _sortColumnIndex,
                                  columns: [
                                    DataColumn(
                                      label: Text(l10n.stadiumNameColumn),
                                      onSort:
                                          (columnIndex, ascending) =>
                                              _sort<String>(
                                                (b) =>
                                                    b['stadium_name']
                                                        ?.toString() ??
                                                    '',
                                                columnIndex,
                                                ascending,
                                              ),
                                    ),
                                    DataColumn(
                                      label: Text(l10n.customer),
                                      onSort:
                                          (columnIndex, ascending) =>
                                              _sort<String>(
                                                (b) =>
                                                    b['name']?.toString() ?? '',
                                                columnIndex,
                                                ascending,
                                              ),
                                    ),
                                    DataColumn(
                                      label: Text(l10n.email),
                                      onSort:
                                          (
                                            columnIndex,
                                            ascending,
                                          ) => _sort<String>(
                                            (b) => b['email']?.toString() ?? '',
                                            columnIndex,
                                            ascending,
                                          ),
                                    ),
                                    DataColumn(
                                      label: Text(l10n.phone),
                                      onSort:
                                          (
                                            columnIndex,
                                            ascending,
                                          ) => _sort<String>(
                                            (b) => b['phone']?.toString() ?? '',
                                            columnIndex,
                                            ascending,
                                          ),
                                    ),
                                    DataColumn(
                                      label: Text(l10n.date),
                                      onSort:
                                          (columnIndex, ascending) =>
                                              _sort<String>(
                                                (b) =>
                                                    b['booking_date']
                                                        ?.toString() ??
                                                    '',
                                                columnIndex,
                                                ascending,
                                              ),
                                    ),
                                    DataColumn(
                                      label: Text(l10n.time),
                                      onSort:
                                          (columnIndex, ascending) =>
                                              _sort<String>(
                                                (b) =>
                                                    b['time_slot']
                                                        ?.toString() ??
                                                    '',
                                                columnIndex,
                                                ascending,
                                              ),
                                    ),
                                    DataColumn(
                                      label: Text(l10n.status),
                                      onSort:
                                          (columnIndex, ascending) =>
                                              _sort<String>(
                                                (b) =>
                                                    b['booking_status']
                                                        ?.toString() ??
                                                    '',
                                                columnIndex,
                                                ascending,
                                              ),
                                    ),
                                    DataColumn(label: Text(l10n.actionsColumn)),
                                  ],
                                  source: bookingDataSource,
                                ),
                              ),
                            ),
                  ),
                ],
              ),
    );
  }
}

class _BookingDataSource extends DataTableSource {
  final List<Map<String, dynamic>> bookings;
  final AppLocalizations l10n;
  final void Function(String bookingId, String status) onUpdateStatus;
  final BuildContext context; // Added context for _getStatusColor

  _BookingDataSource({
    required this.bookings,
    required this.l10n,
    required this.onUpdateStatus,
    required this.context, // Added context
  });

  Color _getStatusColor(String? status) {
    // Re-defined here or passed from _ManageBookingsScreenState
    switch (status?.toLowerCase()) {
      case 'pending':
        return Colors.orange.shade700;
      case 'confirmed':
        return Colors.green.shade700;
      case 'cancelled':
        return Colors.red.shade700;
      default:
        return Colors.grey.shade700;
    }
  }

  @override
  DataRow? getRow(int index) {
    if (index >= bookings.length) return null;
    final booking = bookings[index];
    final bookingId = booking['id']?.toString() ?? '';
    final currentStatus =
        booking['booking_status']?.toString().toLowerCase() ?? 'unknown';

    return DataRow.byIndex(
      index: index,
      cells: [
        DataCell(
          Text(booking['stadium_name']?.toString() ?? l10n.notAvailable),
        ),
        DataCell(Text(booking['name']?.toString() ?? l10n.notAvailable)),
        DataCell(Text(booking['email']?.toString() ?? l10n.notAvailable)),
        DataCell(Text(booking['phone']?.toString() ?? l10n.notAvailable)),
        DataCell(
          Text(booking['booking_date']?.toString() ?? l10n.notAvailable),
        ),
        DataCell(Text(booking['time_slot']?.toString() ?? l10n.notAvailable)),
        DataCell(
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _getStatusColor(booking['booking_status']?.toString()),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              booking['booking_status']?.toString() ?? l10n.unknown,
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
        DataCell(
          PopupMenuButton<String>(
            onSelected: (String value) {
              if (value == 'confirm') {
                onUpdateStatus(bookingId, 'confirmed');
              } else if (value == 'cancel') {
                onUpdateStatus(bookingId, 'cancelled');
              }
            },
            itemBuilder:
                (BuildContext context) => <PopupMenuEntry<String>>[
                  if (currentStatus == 'pending')
                    PopupMenuItem<String>(
                      value: 'confirm',
                      child: ListTile(
                        leading: const Icon(
                          Icons.check_circle,
                          color: Colors.green,
                        ),
                        title: Text(l10n.confirm),
                      ),
                    ),
                  if (currentStatus == 'pending')
                    PopupMenuItem<String>(
                      value: 'cancel',
                      child: ListTile(
                        leading: const Icon(Icons.cancel, color: Colors.red),
                        title: Text(l10n.cancel),
                      ),
                    ),
                  // Add more actions if needed, e.g., view details
                  if (currentStatus != 'pending')
                    PopupMenuItem<String>(
                      value: 'none',
                      enabled: false,
                      child: ListTile(
                        leading: const Icon(Icons.info_outline),
                        title: Text(l10n.noActionsAvailable),
                      ),
                    ),
                ],
            icon: const Icon(Icons.more_vert),
          ),
        ),
      ],
    );
  }

  @override
  bool get isRowCountApproximate => false;

  @override
  int get rowCount => bookings.length;

  @override
  int get selectedRowCount => 0;
}

// Helper widget for info rows, if needed elsewhere or for a detail view
// Widget _buildInfoRow(IconData icon, String label, String value) {
//   return Padding(
//     padding: const EdgeInsets.symmetric(vertical: 4),
//     child: Row(
//       children: [
//         Icon(icon, size: 18, color: Colors.grey[600]),
//         const SizedBox(width: 8),
//         Text(
//           '$label: ',
//           style: TextStyle(
//             color: Colors.grey[600],
//             fontWeight: FontWeight.w500,
//           ),
//         ),
//         Expanded(
//           child: Text(
//             value,
//             style: const TextStyle(
//               color: Colors.black87,
//             ),
//           ),
//         ),
//       ],
//     ),
//   );
// }
