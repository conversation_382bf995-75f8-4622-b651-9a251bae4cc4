// ignore_for_file: unused_import

import 'package:flutter/material.dart';
import '../theme/modern_theme.dart';
import '../widgets/modern/modern_app_bar.dart';
import '../widgets/modern/modern_button.dart';
import '../widgets/modern/modern_card.dart';
import '../widgets/modern/modern_status.dart';

class ModernDashboardScreen extends StatefulWidget {
  const ModernDashboardScreen({Key? key}) : super(key: key);

  @override
  State<ModernDashboardScreen> createState() => _ModernDashboardScreenState();
}

class _ModernDashboardScreenState extends State<ModernDashboardScreen> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: const ModernAppBar(
        title: 'Hemma Sports Academy',
        subtitle: 'Dashboard Overview',
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(ModernTheme.spacingMd),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Welcome Section
            ModernCard(
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(ModernTheme.spacingMd),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(ModernTheme.radiusXl),
                    ),
                    child: Icon(
                      Icons.sports_soccer,
                      size: 32,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                  const SizedBox(width: ModernTheme.spacingMd),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Welcome back!',
                          style: theme.textTheme.headlineSmall?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: ModernTheme.spacingXs),
                        Text(
                          'Manage your sports academy efficiently',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: ModernTheme.spacingLg),

            // Stats Overview
            Text(
              'Overview',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: ModernTheme.spacingMd),

            // Stats Grid
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithMaxCrossAxisExtent(
                maxCrossAxisExtent: 300, // Maximum width of 300px
                childAspectRatio: 300 / 200, // Width 300px / Height 200px = 1.5
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
              ),
              itemCount: 4,
              itemBuilder: (context, index) {
                switch (index) {
                  case 0:
                    return ModernStatsCard(
                      title: 'Total Customers',
                      value: '1,247',
                      subtitle: '+12% from last month',
                      icon: Icons.people,
                      iconColor: ModernTheme.primaryBlue,
                      trend: const ModernStatusBadge(
                        text: '+12%',
                        type: StatusType.success,
                      ),
                    );
                  case 1:
                    return ModernStatsCard(
                      title: 'Active Subscriptions',
                      value: '892',
                      subtitle: '+8% from last month',
                      icon: Icons.card_membership,
                      iconColor: ModernTheme.successGreen,
                      trend: const ModernStatusBadge(
                        text: '+8%',
                        type: StatusType.success,
                      ),
                    );
                  case 2:
                    return ModernStatsCard(
                      title: 'Monthly Revenue',
                      value: '\$24,580',
                      subtitle: '+15% from last month',
                      icon: Icons.attach_money,
                      iconColor: ModernTheme.warningAmber,
                      trend: const ModernStatusBadge(
                        text: '+15%',
                        type: StatusType.success,
                      ),
                    );
                  case 3:
                    return ModernStatsCard(
                      title: 'Sports Programs',
                      value: '12',
                      subtitle: '3 new this month',
                      icon: Icons.sports,
                      iconColor: ModernTheme.infoBlue,
                      trend: const ModernStatusBadge(
                        text: 'NEW',
                        type: StatusType.info,
                      ),
                    );
                  default:
                    return const SizedBox.shrink();
                }
              },
            ),

            const SizedBox(height: ModernTheme.spacingXl),

            // Quick Actions
            Text(
              'Quick Actions',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: ModernTheme.spacingMd),

            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithMaxCrossAxisExtent(
                maxCrossAxisExtent: 300, // Maximum width of 300px
                childAspectRatio: 300 / 200, // Width 300px / Height 200px = 1.5
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
              ),
              itemCount: 4,
              itemBuilder: (context, index) {
                switch (index) {
                  case 0:
                    return ModernActionCard(
                      title: 'Add Customer',
                      description: 'Register new customer',
                      icon: Icons.person_add,
                      iconColor: ModernTheme.primaryBlue,
                      onTap: () {
                        // Navigate to customer registration
                      },
                    );
                  case 1:
                    return ModernActionCard(
                      title: 'View Customers',
                      description: 'Manage customer list',
                      icon: Icons.people,
                      iconColor: ModernTheme.successGreen,
                      onTap: () {
                        Navigator.pushNamed(context, '/customers');
                      },
                    );
                  case 2:
                    return ModernActionCard(
                      title: 'Sports Programs',
                      description: 'Manage sports & trainers',
                      icon: Icons.sports_soccer,
                      iconColor: ModernTheme.warningAmber,
                      onTap: () {
                        // Navigate to sports management
                      },
                    );
                  case 3:
                    return ModernActionCard(
                      title: 'Reports',
                      description: 'View analytics & reports',
                      icon: Icons.analytics,
                      iconColor: ModernTheme.infoBlue,
                      onTap: () {
                        // Navigate to reports
                      },
                    );
                  default:
                    return const SizedBox.shrink();
                }
              },
            ),

            const SizedBox(height: ModernTheme.spacingXl),

            // Recent Activity
            Text(
              'Recent Activity',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: ModernTheme.spacingMd),

            ModernCard(
              child: Column(
                children: [
                  ModernInfoCard(
                    title: 'New Customer Registration',
                    subtitle: 'Ahmed Ali joined Football program',
                    leading: Container(
                      padding: const EdgeInsets.all(ModernTheme.spacingSm),
                      decoration: BoxDecoration(
                        color: ModernTheme.successGreen.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(
                          ModernTheme.radiusMd,
                        ),
                      ),
                      child: Icon(
                        Icons.person_add,
                        color: ModernTheme.successGreen,
                        size: 20,
                      ),
                    ),
                    trailing: Text(
                      '2 hours ago',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ),
                  const Divider(height: 1),
                  ModernInfoCard(
                    title: 'Payment Received',
                    subtitle: 'Sara Mohamed paid monthly subscription',
                    leading: Container(
                      padding: const EdgeInsets.all(ModernTheme.spacingSm),
                      decoration: BoxDecoration(
                        color: ModernTheme.warningAmber.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(
                          ModernTheme.radiusMd,
                        ),
                      ),
                      child: Icon(
                        Icons.payment,
                        color: ModernTheme.warningAmber,
                        size: 20,
                      ),
                    ),
                    trailing: Text(
                      '4 hours ago',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ),
                  const Divider(height: 1),
                  ModernInfoCard(
                    title: 'New Trainer Added',
                    subtitle: 'Coach Hassan joined Basketball team',
                    leading: Container(
                      padding: const EdgeInsets.all(ModernTheme.spacingSm),
                      decoration: BoxDecoration(
                        color: ModernTheme.infoBlue.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(
                          ModernTheme.radiusMd,
                        ),
                      ),
                      child: Icon(
                        Icons.sports,
                        color: ModernTheme.infoBlue,
                        size: 20,
                      ),
                    ),
                    trailing: Text(
                      '1 day ago',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: ModernTheme.spacingXl),

            // Progress Section
            Text(
              'Monthly Progress',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: ModernTheme.spacingMd),

            ModernCard(
              child: Column(
                children: [
                  ModernProgressIndicator(
                    label: 'Customer Registration Goal',
                    value: 0.75,
                    color: ModernTheme.primaryBlue,
                  ),
                  const SizedBox(height: ModernTheme.spacingMd),
                  ModernProgressIndicator(
                    label: 'Revenue Target',
                    value: 0.85,
                    color: ModernTheme.successGreen,
                  ),
                  const SizedBox(height: ModernTheme.spacingMd),
                  ModernProgressIndicator(
                    label: 'Program Enrollment',
                    value: 0.60,
                    color: ModernTheme.warningAmber,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
