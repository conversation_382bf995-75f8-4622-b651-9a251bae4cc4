// ignore_for_file: unused_import, unused_element, avoid_print

import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;
import '../theme/modern_theme.dart';
import '../widgets/modern/modern_app_bar.dart';
import '../widgets/modern/modern_button.dart';
import '../widgets/modern/modern_card.dart';
import '../widgets/modern/modern_status.dart';
import '../services/api_service.dart';

class ModernDashboardScreen extends StatefulWidget {
  const ModernDashboardScreen({Key? key}) : super(key: key);

  @override
  State<ModernDashboardScreen> createState() => _ModernDashboardScreenState();
}

class _ModernDashboardScreenState extends State<ModernDashboardScreen> {
  final ApiService _apiService = ApiService();

  // Dashboard data
  Map<String, dynamic> _dashboardData = {
    'totalCustomers': 0,
    'activeSubscriptions': 0,
    'monthlyRevenue': 0.0,
    'sportsPrograms': 0,
    'isLoading': true,
  };

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }

  Future<void> _loadDashboardData() async {
    try {
      setState(() {
        _dashboardData['isLoading'] = true;
      });

      // Fetch all customer data
      final customerData = await _apiService.fetchAllCustomerData();
      final customers = customerData['customers'] as List<dynamic>;
      final payments = customerData['payments'] as List<dynamic>;

      // Fetch sports data
      final sports = await _apiService.getSports();

      // Calculate metrics
      final totalCustomers = customers.length;
      final activeSubscriptions =
          customers.where((c) => c['status'] == 'Active').length;
      final monthlyRevenue = payments.fold<double>(
        0.0,
        (sum, payment) =>
            sum + (double.tryParse(payment['amount']?.toString() ?? '0') ?? 0),
      );
      final sportsPrograms = sports.length;

      setState(() {
        _dashboardData = {
          'totalCustomers': totalCustomers,
          'activeSubscriptions': activeSubscriptions,
          'monthlyRevenue': monthlyRevenue,
          'sportsPrograms': sportsPrograms,
          'isLoading': false,
        };
      });
    } catch (e) {
      debugPrint('Error loading dashboard data: $e');
      setState(() {
        _dashboardData['isLoading'] = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: const ModernAppBar(
        title: 'Hemma Sports Academy',
        subtitle: 'Dashboard Overview',
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(ModernTheme.spacingSm),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Welcome Section
            ModernCard(
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(ModernTheme.spacingMd),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(ModernTheme.radiusXl),
                    ),
                    child: Icon(
                      Icons.sports_soccer,
                      size: 32,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                  const SizedBox(width: ModernTheme.spacingMd),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Welcome back!',
                          style: theme.textTheme.headlineSmall?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: ModernTheme.spacingXs),
                        Text(
                          'Manage your sports academy efficiently',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: ModernTheme.spacingSm),

            // Stats Overview
            Text(
              'Overview',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: ModernTheme.spacingXs),

            // Stats Grid
            _dashboardData['isLoading'] == true
                ? const Center(
                  child: Padding(
                    padding: EdgeInsets.all(50.0),
                    child: CircularProgressIndicator(),
                  ),
                )
                : GridView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  gridDelegate: const SliverGridDelegateWithMaxCrossAxisExtent(
                    maxCrossAxisExtent: 300, // Maximum width of 300px
                    childAspectRatio:
                        300 / 200, // Width 300px / Height 200px = 1.5
                    crossAxisSpacing: 4,
                    mainAxisSpacing: 4,
                  ),
                  itemCount: 4,
                  itemBuilder: (context, index) {
                    switch (index) {
                      case 0:
                        return ModernStatsCard(
                          title: 'Total Customers',
                          value: _dashboardData['totalCustomers'].toString(),
                          subtitle: 'Registered customers',
                          icon: Icons.people,
                          iconColor: ModernTheme.primaryBlue,
                          trend: const ModernStatusBadge(
                            text: '+12%',
                            type: StatusType.success,
                          ),
                        );
                      case 1:
                        return ModernStatsCard(
                          title: 'Active Subscriptions',
                          value:
                              _dashboardData['activeSubscriptions'].toString(),
                          subtitle: 'Active memberships',
                          icon: Icons.card_membership,
                          iconColor: ModernTheme.successGreen,
                          trend: const ModernStatusBadge(
                            text: '+8%',
                            type: StatusType.success,
                          ),
                        );
                      case 2:
                        return ModernStatsCard(
                          title: 'Monthly Revenue',
                          value:
                              '${_dashboardData['monthlyRevenue'].toStringAsFixed(0)} ر.س',
                          subtitle: 'Total revenue',
                          icon: Icons.attach_money,
                          iconColor: ModernTheme.warningAmber,
                          trend: const ModernStatusBadge(
                            text: '+15%',
                            type: StatusType.success,
                          ),
                        );
                      case 3:
                        return ModernStatsCard(
                          title: 'Sports Programs',
                          value: _dashboardData['sportsPrograms'].toString(),
                          subtitle: 'Available programs',
                          icon: Icons.sports,
                          iconColor: ModernTheme.infoBlue,
                          trend: const ModernStatusBadge(
                            text: 'NEW',
                            type: StatusType.info,
                          ),
                        );
                      default:
                        return const SizedBox.shrink();
                    }
                  },
                ),

            const SizedBox(height: ModernTheme.spacingSm),

            // Quick Actions
            Text(
              'Quick Actions',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: ModernTheme.spacingXs),

            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithMaxCrossAxisExtent(
                maxCrossAxisExtent: 300, // Maximum width of 300px
                childAspectRatio: 300 / 200, // Width 300px / Height 200px = 1.5
                crossAxisSpacing: 4,
                mainAxisSpacing: 4,
              ),
              itemCount: 4,
              itemBuilder: (context, index) {
                switch (index) {
                  case 0:
                    return ModernActionCard(
                      title: 'Add Customer',
                      description: 'Register new customer',
                      icon: Icons.person_add,
                      iconColor: ModernTheme.primaryBlue,
                      onTap: () {
                        // Navigate to customer registration
                      },
                    );
                  case 1:
                    return ModernActionCard(
                      title: 'View Customers',
                      description: 'Manage customer list',
                      icon: Icons.people,
                      iconColor: ModernTheme.successGreen,
                      onTap: () {
                        Navigator.pushNamed(context, '/customers');
                      },
                    );
                  case 2:
                    return ModernActionCard(
                      title: 'Sports Programs',
                      description: 'Manage sports & trainers',
                      icon: Icons.sports_soccer,
                      iconColor: ModernTheme.warningAmber,
                      onTap: () {
                        // Navigate to sports management
                      },
                    );
                  case 3:
                    return ModernActionCard(
                      title: 'Reports',
                      description: 'View analytics & reports',
                      icon: Icons.analytics,
                      iconColor: ModernTheme.infoBlue,
                      onTap: () {
                        // Navigate to reports
                      },
                    );
                  default:
                    return const SizedBox.shrink();
                }
              },
            ),

            const SizedBox(height: ModernTheme.spacingSm),

            // Recent Activity
            Text(
              'Recent Activity',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: ModernTheme.spacingXs),

            ModernCard(
              child: Column(
                children: [
                  ModernInfoCard(
                    title: 'New Customer Registration',
                    subtitle: 'Ahmed Ali joined Football program',
                    leading: Container(
                      padding: const EdgeInsets.all(ModernTheme.spacingSm),
                      decoration: BoxDecoration(
                        color: ModernTheme.successGreen.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(
                          ModernTheme.radiusMd,
                        ),
                      ),
                      child: Icon(
                        Icons.person_add,
                        color: ModernTheme.successGreen,
                        size: 20,
                      ),
                    ),
                    trailing: Text(
                      '2 hours ago',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ),
                  const Divider(height: 1),
                  ModernInfoCard(
                    title: 'Payment Received',
                    subtitle: 'Sara Mohamed paid monthly subscription',
                    leading: Container(
                      padding: const EdgeInsets.all(ModernTheme.spacingSm),
                      decoration: BoxDecoration(
                        color: ModernTheme.warningAmber.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(
                          ModernTheme.radiusMd,
                        ),
                      ),
                      child: Icon(
                        Icons.payment,
                        color: ModernTheme.warningAmber,
                        size: 20,
                      ),
                    ),
                    trailing: Text(
                      '4 hours ago',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ),
                  const Divider(height: 1),
                  ModernInfoCard(
                    title: 'New Trainer Added',
                    subtitle: 'Coach Hassan joined Basketball team',
                    leading: Container(
                      padding: const EdgeInsets.all(ModernTheme.spacingSm),
                      decoration: BoxDecoration(
                        color: ModernTheme.infoBlue.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(
                          ModernTheme.radiusMd,
                        ),
                      ),
                      child: Icon(
                        Icons.sports,
                        color: ModernTheme.infoBlue,
                        size: 20,
                      ),
                    ),
                    trailing: Text(
                      '1 day ago',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: ModernTheme.spacingSm),

            // Progress Section
            Text(
              'Monthly Progress',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: ModernTheme.spacingXs),

            ModernCard(
              child: Column(
                children: [
                  ModernProgressIndicator(
                    label: 'Customer Registration Goal',
                    value: 0.75,
                    color: ModernTheme.primaryBlue,
                  ),
                  const SizedBox(height: ModernTheme.spacingXs),
                  ModernProgressIndicator(
                    label: 'Revenue Target',
                    value: 0.85,
                    color: ModernTheme.successGreen,
                  ),
                  const SizedBox(height: ModernTheme.spacingXs),
                  ModernProgressIndicator(
                    label: 'Program Enrollment',
                    value: 0.60,
                    color: ModernTheme.warningAmber,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
