// ignore_for_file: library_private_types_in_public_api, use_build_context_synchronously, unused_element, override_on_non_overriding_member, avoid_print

import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class PayrollScreen extends StatefulWidget {
  const PayrollScreen({Key? key}) : super(key: key);

  @override
  _PayrollScreenState createState() => _PayrollScreenState();
}

class _PayrollScreenState extends State<PayrollScreen> {
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;
  List<Map<String, dynamic>> _employees = [];
  List<Map<String, dynamic>> _payrollRecords = [];
  
  // Form controllers
  final _basicSalaryController = TextEditingController();
  final _deductionsController = TextEditingController();
  final _incentivesController = TextEditingController();
  String? _selectedEmployeeId;

  @override
  void initState() {
    super.initState();
    _loadEmployees();
    _loadPayrollRecords();
  }

  @override
  void dispose() {
    _basicSalaryController.dispose();
    _deductionsController.dispose();
    _incentivesController.dispose();
    super.dispose();
  }

  Future<void> _loadEmployees() async {
    setState(() => _isLoading = true);
    try {
      final response = await http.get(
        Uri.parse('https://backend2.hemmasportacademy.com/employees/select_employee.php'),
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        setState(() {
          _employees = data.map((item) => Map<String, dynamic>.from(item)).toList();
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading employees: $e')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _loadPayrollRecords() async {
    setState(() => _isLoading = true);
    try {
      final response = await http.get(
        Uri.parse('https://backend2.hemmasportacademy.com/payroll/select_payroll.php'),
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        setState(() {
          _payrollRecords = data.map((item) => Map<String, dynamic>.from(item)).toList();
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading payroll records: $e')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _deletePayrollRecord(String id) async {
    try {
      // Format the data as JSON to match the API expectation
      final Map<String, dynamic> data = {
        'id': int.parse(id),
      };

      final response = await http.post(
        Uri.parse('https://backend2.hemmasportacademy.com/payroll/delete_payroll.php'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: json.encode(data),
      );

      print('Delete Response status: ${response.statusCode}');
      print('Delete Response body: ${response.body}');

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        if (responseData.containsKey('message')) {
          await _loadPayrollRecords(); // Refresh the list
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(responseData['message'])),
          );
        } else {
          throw Exception('Failed to delete payroll record');
        }
      } else {
        throw Exception('Server returned status code: ${response.statusCode}');
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error deleting payroll record: $e')),
      );
    }
  }

  Future<void> _updatePayrollRecord(Map<String, dynamic> record) async {
    try {
      // Format the data as JSON to match the API expectation
      final Map<String, dynamic> data = {
        'id': int.parse(record['id'].toString()),
        'employee_id': int.parse(record['employee_id'].toString()),
        'basic_salary': double.parse(record['basic_salary'].toString()),
        'deductions': double.parse(record['deductions'].toString()),
        'incentives': double.parse(record['incentives'].toString()),
      };

      final response = await http.post(
        Uri.parse('https://backend2.hemmasportacademy.com/payroll/update_payroll.php'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: json.encode(data),
      );

      print('Update Response status: ${response.statusCode}');
      print('Update Response body: ${response.body}');

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        if (responseData.containsKey('message')) {
          await _loadPayrollRecords(); // Refresh the list
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(responseData['message'])),
          );
        } else {
          throw Exception('Failed to update payroll record');
        }
      } else {
        throw Exception('Server returned status code: ${response.statusCode}');
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error updating payroll record: $e')),
      );
    }
  }

  @override
  Future<void> submitPayroll() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);
    try {
      // Format the data as JSON to match the curl command
      final Map<String, dynamic> data = {
        'employee_id': int.parse(_selectedEmployeeId ?? '0'),
        'basic_salary': double.parse(_basicSalaryController.text.trim()),
        'deductions': double.parse(_deductionsController.text.trim()),
        'incentives': double.parse(_incentivesController.text.trim()),
      };

      print('Sending data: $data');

      final response = await http.post(
        Uri.parse('https://backend2.hemmasportacademy.com/payroll/insert_payroll.php'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: json.encode(data),
      );

      print('Response status: ${response.statusCode}');
      print('Response body: ${response.body}');

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        if (responseData.containsKey('message')) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(responseData['message'])),
          );
          await _loadPayrollRecords();
          _clearForm();
        } else if (responseData.containsKey('error')) {
          throw Exception(responseData['error']);
        }
      } else {
        throw Exception('Server returned status code: ${response.statusCode}');
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error saving payroll information: $e')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _clearForm() {
    _selectedEmployeeId = null;
    _basicSalaryController.clear();
    _deductionsController.clear();
    _incentivesController.clear();
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.payrollManagement),
        elevation: 2,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Form(
                    key: _formKey,
                    child: Column(
                      children: [
                        DropdownButtonFormField<String>(
                          value: _selectedEmployeeId,
                          decoration: InputDecoration(
                            labelText: localizations.selectEmployee,
                            border: const OutlineInputBorder(),
                          ),
                          items: _employees.map((employee) {
                            return DropdownMenuItem(
                              value: employee['id'].toString(),
                              child: Text(employee['name']),
                            );
                          }).toList(),
                          onChanged: (value) {
                            setState(() => _selectedEmployeeId = value);
                          },
                          validator: (value) => value == null ? localizations.employeeRequired : null,
                        ),
                        const SizedBox(height: 16),
                        TextFormField(
                          controller: _basicSalaryController,
                          decoration: InputDecoration(
                            labelText: localizations.basicSalary,
                            border: const OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                          validator: (value) => value?.isEmpty ?? true ? localizations.basicSalaryRequired : null,
                        ),
                        const SizedBox(height: 16),
                        TextFormField(
                          controller: _deductionsController,
                          decoration: InputDecoration(
                            labelText: localizations.deductions,
                            border: const OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                          validator: (value) => value?.isEmpty ?? true ? localizations.deductionsRequired : null,
                        ),
                        const SizedBox(height: 16),
                        TextFormField(
                          controller: _incentivesController,
                          decoration: InputDecoration(
                            labelText: localizations.incentives,
                            border: const OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                          validator: (value) => value?.isEmpty ?? true ? localizations.incentivesRequired : null,
                        ),
                        const SizedBox(height: 24),
                        ElevatedButton(
                          onPressed: _isLoading ? null : submitPayroll,
                          child: Text(localizations.save),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 32),
                  Text(
                    localizations.payrollRecords,
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 16),
                  ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: _payrollRecords.length,
                    itemBuilder: (context, index) {
                      final record = _payrollRecords[index];
                      final employee = _employees.firstWhere(
                        (e) => e['id'].toString() == record['employee_id'].toString(),
                        orElse: () => {'name': 'Unknown Employee'},
                      );
                      
                      return Card(
                        margin: const EdgeInsets.only(bottom: 8),
                        child: ListTile(
                          title: Text(employee['name']),
                          subtitle: Text(
                            'Basic: \$${record['basic_salary']} | '
                            'Deductions: \$${record['deductions']} | '
                            'Incentives: \$${record['incentives']}',
                          ),
                          trailing: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              IconButton(
                                icon: const Icon(Icons.edit),
                                onPressed: () {
                                  // Populate form with record data for editing
                                  setState(() {
                                    _selectedEmployeeId = record['employee_id'].toString();
                                    _basicSalaryController.text = record['basic_salary'].toString();
                                    _deductionsController.text = record['deductions'].toString();
                                    _incentivesController.text = record['incentives'].toString();
                                  });
                                },
                              ),
                              IconButton(
                                icon: const Icon(Icons.delete),
                                onPressed: () => _deletePayrollRecord(record['id'].toString()),
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
    );
  }
}