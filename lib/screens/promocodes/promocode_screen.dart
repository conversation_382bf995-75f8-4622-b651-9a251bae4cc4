// ignore_for_file: use_build_context_synchronously, library_private_types_in_public_api, sort_child_properties_last, unnecessary_to_list_in_spreads, unused_element, unnecessary_null_comparison, unrelated_type_equality_checks

import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class PromoCode {
  final String? id;
  final String code;
  final String description;
  final String discountType;
  final double discountValue;
  final int maxUses;
  final int usesCount;
  final DateTime startDate;
  final DateTime endDate;
  final bool isActive;
  final DateTime createdAt;

  PromoCode({
    this.id,
    required this.code,
    required this.description,
    required this.discountType,
    required this.discountValue,
    required this.maxUses,
    required this.usesCount,
    required this.startDate,
    required this.endDate,
    required this.isActive,
    required this.createdAt,
  });

  factory PromoCode.fromJson(Map<String, dynamic> json) {
    return PromoCode(
      id: json['id'],
      code: json['code'],
      description: json['description'],
      discountType: json['discount_type'],
      discountValue: double.parse(json['discount_value'].toString()),
      maxUses: int.parse(json['max_uses'].toString()),
      usesCount: int.parse(json['uses_count'].toString()),
      startDate: DateTime.parse(json['start_date']),
      endDate: DateTime.parse(json['end_date']),
      isActive: json['is_active'].toString() == '1',
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      'code': code,
      'description': description,
      'discount_type': discountType,
      'discount_value': discountValue,
      'max_uses': maxUses,
      'uses_count': usesCount,
      'start_date': DateFormat('yyyy-MM-dd').format(startDate),
      'end_date': DateFormat('yyyy-MM-dd').format(endDate),
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

class PromoCodeScreen extends StatefulWidget {
  const PromoCodeScreen({Key? key}) : super(key: key);

  @override
  _PromoCodeScreenState createState() => _PromoCodeScreenState();
}

class _PromoCodeScreenState extends State<PromoCodeScreen> {
  List<PromoCode> _promoCodes = [];
  bool _isLoading = false;
  final String _baseUrl = 'https://backend2.hemmasportacademy.com/promocodes';

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.green),
    );
  }

  @override
  void initState() {
    super.initState();
    _fetchPromoCodes();
  }

  Future<void> _fetchPromoCodes() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/get/promocode_select.php'),
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        setState(() {
          _promoCodes = data.map((json) => PromoCode.fromJson(json)).toList();
        });
      } else {
        _showErrorSnackBar('Failed to load promo codes');
      }
    } catch (e) {
      _showErrorSnackBar('Error: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _addPromoCode(PromoCode promoCode) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/insert/promocode_insert.php'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode(promoCode.toJson()),
      );

      if (response.statusCode == 200) {
        _showSuccessSnackBar('Promo code added successfully');
        _fetchPromoCodes();
      } else {
        _showErrorSnackBar('Failed to add promo code: ${response.body}');
      }
    } catch (e) {
      _showErrorSnackBar('Error: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _updatePromoCode(PromoCode promoCode) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/update/promocode_update.php'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode(promoCode.toJson()),
      );

      if (response.statusCode == 200) {
        _showSuccessSnackBar('Promo code updated successfully');
        _fetchPromoCodes();
      } else {
        _showErrorSnackBar('Failed to update promo code: ${response.body}');
      }
    } catch (e) {
      _showErrorSnackBar('Error: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _deletePromoCode(String id) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/delete/promocodes_delete.php'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({'id': id}),
      );

      if (response.statusCode == 200) {
        _showSuccessSnackBar('Promo code deleted successfully');
        _fetchPromoCodes();
      } else {
        _showErrorSnackBar('Failed to delete promo code: ${response.body}');
      }
    } catch (e) {
      _showErrorSnackBar('Error: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showPromoCodeDialog({PromoCode? promoCode}) {
    final bool isEditing = promoCode != null;
    final TextEditingController codeController = TextEditingController(
      text: isEditing ? promoCode.code : '',
    );
    final TextEditingController descriptionController = TextEditingController(
      text: isEditing ? promoCode.description : '',
    );
    final TextEditingController discountValueController = TextEditingController(
      text: isEditing ? promoCode.discountValue.toString() : '',
    );
    final TextEditingController maxUsesController = TextEditingController(
      text: isEditing ? promoCode.maxUses.toString() : '',
    );

    String discountType = isEditing ? promoCode.discountType : 'percentage';
    DateTime startDate = isEditing ? promoCode.startDate : DateTime.now();
    DateTime endDate =
        isEditing
            ? promoCode.endDate
            : DateTime.now().add(const Duration(days: 30));
    bool isActive = isEditing ? promoCode.isActive : true;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: Text(
                isEditing
                    ? AppLocalizations.of(context)?.editPromoCode ??
                        'Edit Promo Code'
                    : AppLocalizations.of(context)?.addPromoCode ??
                        'Add Promo Code',
              ),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TextField(
                      controller: codeController,
                      decoration: InputDecoration(
                        labelText: AppLocalizations.of(context)?.code ?? 'Code',
                        hintText: 'WELCOME10',
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: descriptionController,
                      decoration: InputDecoration(
                        labelText:
                            AppLocalizations.of(context)?.description ??
                            'Description',
                        hintText: '10% discount for new users',
                      ),
                      maxLines: 2,
                    ),
                    const SizedBox(height: 16),
                    DropdownButtonFormField<String>(
                      value: discountType,
                      decoration: InputDecoration(
                        labelText:
                            AppLocalizations.of(context)?.discountType ??
                            'Discount Type',
                      ),
                      items: [
                        DropdownMenuItem(
                          value: 'percentage',
                          child: Text(
                            AppLocalizations.of(context)?.percentage ??
                                'Percentage',
                          ),
                        ),
                        DropdownMenuItem(
                          value: 'fixed',
                          child: Text(
                            AppLocalizations.of(context)?.fixed ??
                                'Fixed Amount',
                          ),
                        ),
                      ],
                      onChanged: (value) {
                        setState(() {
                          discountType = value!;
                        });
                      },
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: discountValueController,
                      decoration: InputDecoration(
                        labelText:
                            AppLocalizations.of(context)?.discountValue ??
                            'Discount Value',
                        hintText: discountType == 'percentage' ? '10' : '50',
                        suffixText: discountType == 'percentage' ? '%' : '\$',
                      ),
                      keyboardType: TextInputType.number,
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: maxUsesController,
                      decoration: InputDecoration(
                        labelText:
                            AppLocalizations.of(context)?.maxUses ??
                            'Maximum Uses',
                        hintText: '100',
                      ),
                      keyboardType: TextInputType.number,
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: ListTile(
                            title: Text(
                              AppLocalizations.of(context)?.startDate ??
                                  'Start Date',
                            ),
                            subtitle: Text(
                              DateFormat('yyyy-MM-dd').format(startDate),
                            ),
                            onTap: () async {
                              final DateTime? picked = await showDatePicker(
                                context: context,
                                initialDate: startDate,
                                firstDate: DateTime.now().subtract(
                                  const Duration(days: 365),
                                ),
                                lastDate: DateTime.now().add(
                                  const Duration(days: 365 * 5),
                                ),
                              );
                              if (picked != null) {
                                setState(() {
                                  startDate = picked;
                                });
                              }
                            },
                          ),
                        ),
                      ],
                    ),
                    Row(
                      children: [
                        Expanded(
                          child: ListTile(
                            title: Text(
                              AppLocalizations.of(context)?.endDate ??
                                  'End Date',
                            ),
                            subtitle: Text(
                              DateFormat('yyyy-MM-dd').format(endDate),
                            ),
                            onTap: () async {
                              final DateTime? picked = await showDatePicker(
                                context: context,
                                initialDate: endDate,
                                firstDate: DateTime.now(),
                                lastDate: DateTime.now().add(
                                  const Duration(days: 365 * 5),
                                ),
                              );
                              if (picked != null) {
                                setState(() {
                                  endDate = picked;
                                });
                              }
                            },
                          ),
                        ),
                      ],
                    ),
                    SwitchListTile(
                      title: Text(
                        AppLocalizations.of(context)?.isActive ?? 'Is Active',
                      ),
                      value: isActive,
                      onChanged: (bool value) {
                        setState(() {
                          isActive = value;
                        });
                      },
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: Text(AppLocalizations.of(context)?.cancel ?? 'Cancel'),
                ),
                ElevatedButton(
                  onPressed: () {
                    if (codeController.text.isEmpty ||
                        descriptionController.text.isEmpty ||
                        discountValueController.text.isEmpty ||
                        maxUsesController.text.isEmpty) {
                      _showErrorSnackBar('Please fill all required fields');
                      return;
                    }

                    final PromoCode newPromoCode = PromoCode(
                      id: isEditing ? promoCode.id : null,
                      code: codeController.text,
                      description: descriptionController.text,
                      discountType: discountType,
                      discountValue:
                          double.tryParse(discountValueController.text) ?? 0,
                      maxUses: int.tryParse(maxUsesController.text) ?? 0,
                      usesCount: isEditing ? promoCode.usesCount : 0,
                      startDate: startDate,
                      endDate: endDate,
                      isActive: isActive,
                      createdAt:
                          isEditing ? promoCode.createdAt : DateTime.now(),
                    );

                    Navigator.of(context).pop();

                    if (isEditing) {
                      _updatePromoCode(newPromoCode);
                    } else {
                      _addPromoCode(newPromoCode);
                    }
                  },
                  child: Text(
                    isEditing
                        ? AppLocalizations.of(context)?.update ?? 'Update'
                        : AppLocalizations.of(context)?.add ?? 'Add',
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(AppLocalizations.of(context)?.promoCodes ?? 'Promo Codes'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _fetchPromoCodes,
            tooltip: AppLocalizations.of(context)?.refresh ?? 'Refresh',
          ),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _promoCodes.isEmpty
              ? Center(
                child: Text(
                  AppLocalizations.of(context)?.noPromoCodesFound ??
                      'No promo codes found',
                  style: const TextStyle(fontSize: 18),
                ),
              )
              : ListView.builder(
                itemCount: _promoCodes.length,
                itemBuilder: (context, index) {
                  final promoCode = _promoCodes[index];
                  return Card(
                    margin: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    child: ExpansionTile(
                      title: Text(
                        promoCode.code,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      subtitle: Text(promoCode.description),
                      leading: Icon(
                        promoCode.isActive ? Icons.check_circle : Icons.cancel,
                        color: promoCode.isActive ? Colors.green : Colors.red,
                      ),
                      children: [
                        Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              _buildInfoRow(
                                AppLocalizations.of(context)?.discountType ??
                                    'Discount Type',
                                promoCode.discountType == 'percentage'
                                    ? AppLocalizations.of(
                                          context,
                                        )?.percentage ??
                                        'Percentage'
                                    : AppLocalizations.of(context)?.fixed ??
                                        'Fixed Amount',
                              ),
                              _buildInfoRow(
                                AppLocalizations.of(context)?.discount ??
                                    'Discount',
                                '${promoCode.discountValue}${promoCode.discountType == 'percentage' ? '%' : ' ${AppLocalizations.of(context)?.currency ?? 'EGP'}'}',
                              ),
                              _buildInfoRow(
                                AppLocalizations.of(context)?.usage ?? 'Usage',
                                '${promoCode.usesCount} / ${promoCode.maxUses}',
                              ),
                              _buildInfoRow(
                                AppLocalizations.of(context)?.validity ??
                                    'Validity',
                                '${DateFormat('yyyy-MM-dd').format(promoCode.startDate)} to ${DateFormat('yyyy-MM-dd').format(promoCode.endDate)}',
                              ),
                              _buildInfoRow(
                                AppLocalizations.of(context)?.status ??
                                    'Status',
                                promoCode.isActive
                                    ? AppLocalizations.of(context)?.active ??
                                        'Active'
                                    : AppLocalizations.of(context)?.inactive ??
                                        'Inactive',
                                valueColor:
                                    promoCode.isActive
                                        ? Colors.green
                                        : Colors.red,
                              ),
                              _buildInfoRow(
                                AppLocalizations.of(context)?.createdAt ??
                                    'Created At',
                                DateFormat(
                                  'yyyy-MM-dd HH:mm',
                                ).format(promoCode.createdAt),
                              ),
                              const SizedBox(height: 16),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  TextButton.icon(
                                    icon: const Icon(
                                      Icons.people,
                                      color: Colors.purple,
                                    ),
                                    label: Text(
                                      AppLocalizations.of(
                                            context,
                                          )?.eligibility ??
                                          'Eligibility',
                                    ),
                                    onPressed: () {
                                      if (promoCode.id != null) {
                                        _showPromoCodeEligibilityDialog(
                                          promoCode,
                                        );
                                      } else {
                                        _showErrorSnackBar(
                                          'Please save the promo code first',
                                        );
                                      }
                                    },
                                  ),
                                  const SizedBox(width: 16),
                                  TextButton.icon(
                                    icon: const Icon(
                                      Icons.edit,
                                      color: Colors.blue,
                                    ),
                                    label: Text(
                                      AppLocalizations.of(context)?.edit ??
                                          'Edit',
                                    ),
                                    onPressed: () {
                                      _showPromoCodeDialog(
                                        promoCode: promoCode,
                                      );
                                    },
                                  ),
                                  const SizedBox(width: 16),
                                  TextButton.icon(
                                    icon: const Icon(
                                      Icons.delete,
                                      color: Colors.red,
                                    ),
                                    label: Text(
                                      AppLocalizations.of(context)?.delete ??
                                          'Delete',
                                    ),
                                    onPressed: () {
                                      showDialog(
                                        context: context,
                                        builder: (BuildContext context) {
                                          return AlertDialog(
                                            title: Text(
                                              AppLocalizations.of(
                                                    context,
                                                  )?.confirmDelete ??
                                                  'Confirm Delete',
                                            ),
                                            content: Text(
                                              AppLocalizations.of(
                                                    context,
                                                  )?.deletePromoCodeConfirmation ??
                                                  'Are you sure you want to delete this promo code?',
                                            ),
                                            actions: [
                                              TextButton(
                                                onPressed: () {
                                                  Navigator.of(context).pop();
                                                },
                                                child: Text(
                                                  AppLocalizations.of(
                                                        context,
                                                      )?.cancel ??
                                                      'Cancel',
                                                ),
                                              ),
                                              TextButton(
                                                onPressed: () {
                                                  Navigator.of(context).pop();
                                                  if (promoCode.id != null) {
                                                    _deletePromoCode(
                                                      promoCode.id!,
                                                    );
                                                  }
                                                },
                                                child: Text(
                                                  AppLocalizations.of(
                                                        context,
                                                      )?.delete ??
                                                      'Delete',
                                                  style: const TextStyle(
                                                    color: Colors.red,
                                                  ),
                                                ),
                                              ),
                                            ],
                                          );
                                        },
                                      );
                                    },
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showPromoCodeDialog(),
        child: const Icon(Icons.add),
        tooltip: AppLocalizations.of(context)?.addPromoCode ?? 'Add Promo Code',
      ),
    );
  }

  Widget _buildPromoCodeCard(PromoCode promoCode) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    promoCode.code,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Row(
                  children: [
                    // Edit button
                    IconButton(
                      icon: const Icon(Icons.edit),
                      onPressed: () {
                        _editPromoCode(promoCode);
                      },
                    ),
                    // Delete button
                    IconButton(
                      icon: const Icon(Icons.delete, color: Colors.red),
                      onPressed: () {
                        _deletePromoCode(promoCode.id!);
                      },
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 8),
            _buildInfoRow(
              AppLocalizations.of(context)?.discount ?? 'Discount',
              '${promoCode.discountValue}${promoCode.discountType == 'percentage' ? '%' : ' ${AppLocalizations.of(context)?.currency ?? 'EGP'}'}',
            ),
            _buildInfoRow(
              AppLocalizations.of(context)?.validFrom ?? 'Valid From',
              promoCode.startDate != null
                  ? DateFormat('yyyy-MM-dd').format(promoCode.startDate)
                  : 'N/A',
            ),
            _buildInfoRow(
              AppLocalizations.of(context)?.validTo ?? 'Valid To',
              promoCode.endDate != null
                  ? DateFormat('yyyy-MM-dd').format(promoCode.endDate)
                  : 'N/A',
            ),
            _buildInfoRow(
              AppLocalizations.of(context)?.maxUses ?? 'Max Uses',
              '${promoCode.maxUses}',
            ),
            _buildInfoRow(
              AppLocalizations.of(context)?.usesCount ?? 'Uses Count',
              '${promoCode.usesCount}',
            ),
            _buildInfoRow(
              AppLocalizations.of(context)?.status ?? 'Status',
              promoCode.isActive == 1
                  ? AppLocalizations.of(context)?.active ?? 'Active'
                  : AppLocalizations.of(context)?.inactive ?? 'Inactive',
              valueColor: promoCode.isActive ? Colors.green : Colors.red,
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                // Eligibility button
                if (promoCode.id != null) ...[
                  ElevatedButton.icon(
                    icon: const Icon(Icons.people),
                    label: Text(
                      AppLocalizations.of(context)?.eligibility ??
                          'Eligibility',
                    ),
                    onPressed: () {
                      _showPromoCodeEligibilityDialog(promoCode);
                    },
                  ),
                  const SizedBox(width: 8),
                ],
                // Usage button
                if (promoCode.id != null) ...[
                  ElevatedButton.icon(
                    icon: const Icon(Icons.history),
                    label: Text(
                      AppLocalizations.of(context)?.usages ?? 'Usages',
                    ),
                    onPressed: () {
                      _showPromoCodeUsageDialog(promoCode);
                    },
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, {Color? valueColor}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value, style: TextStyle(color: valueColor))),
        ],
      ),
    );
  }

  void _editPromoCode(PromoCode promoCode) {
    _showPromoCodeDialog(promoCode: promoCode);
  }

  Future<List<Map<String, dynamic>>> _fetchPromoCodeEligibility(
    String promoCodeId,
  ) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final response = await http.get(
        Uri.parse(
          '$_baseUrl/get/select_promo_code_eligibility.php?promo_code_id=$promoCodeId',
        ),
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        setState(() {
          _isLoading = false;
        });
        return data.cast<Map<String, dynamic>>();
      } else {
        _showErrorSnackBar('Failed to load promo code eligibility');
        setState(() {
          _isLoading = false;
        });
        return [];
      }
    } catch (e) {
      _showErrorSnackBar('Error: $e');
      setState(() {
        _isLoading = false;
      });
      return [];
    }
  }

  Future<void> _addPromoCodeEligibility(
    String promoCodeId,
    String customerId,
  ) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/insert/insert_promo_code_eligibility.php'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          "promo_code_id": promoCodeId,
          "customer_id": customerId,
        }),
      );

      if (response.statusCode == 200) {
        _showSuccessSnackBar('Promo code eligibility added successfully');
      } else {
        _showErrorSnackBar(
          'Failed to add promo code eligibility: ${response.body}',
        );
      }
    } catch (e) {
      _showErrorSnackBar('Error: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _deletePromoCodeEligibility(String id) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/delete/delete_promo_code_eligibility.php'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({"id": id}),
      );

      if (response.statusCode == 200) {
        _showSuccessSnackBar('Promo code eligibility deleted successfully');
      } else {
        _showErrorSnackBar(
          'Failed to delete promo code eligibility: ${response.body}',
        );
      }
    } catch (e) {
      _showErrorSnackBar('Error: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showPromoCodeEligibilityDialog(PromoCode promoCode) async {
    if (promoCode.id == null) {
      _showErrorSnackBar('Invalid promo code ID');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    List<Map<String, dynamic>> eligibleCustomers = [];
    List<Map<String, dynamic>> allCustomers = [];

    try {
      // Fetch current eligibility
      eligibleCustomers = await _fetchPromoCodeEligibility(promoCode.id!);

      // Fetch all customers
      final customersResponse = await http.get(
        Uri.parse(
          'https://backend2.hemmasportacademy.com/customers/select_all.php',
        ),
      );

      if (customersResponse.statusCode == 200) {
        final List<dynamic> data = json.decode(customersResponse.body);
        allCustomers = data.cast<Map<String, dynamic>>();
      } else {
        _showErrorSnackBar('Failed to load customers');
      }
    } catch (e) {
      _showErrorSnackBar('Error: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            return AlertDialog(
              title: Text(
                AppLocalizations.of(context)?.eligibility ??
                    'Promo Code Eligibility',
              ),
              content: SizedBox(
                width: double.maxFinite,
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        AppLocalizations.of(context)?.selectCustomers ??
                            'Select customers eligible for this promo code:',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 16),
                      if (allCustomers.isEmpty)
                        const Text('No customers available')
                      else
                        Flexible(
                          child: ListView.builder(
                            shrinkWrap: true,
                            itemCount: allCustomers.length,
                            itemBuilder: (context, index) {
                              final customer = allCustomers[index];
                              final bool isEligible = eligibleCustomers.any(
                                (e) => e['customer_id'] == customer['id'],
                              );
                              return CheckboxListTile(
                                title: Text(customer['name'] ?? 'Unknown'),
                                subtitle: Text(customer['email'] ?? ''),
                                value: isEligible,
                                onChanged: (bool? value) async {
                                  setDialogState(() {
                                    _isLoading = true;
                                  });
                                  try {
                                    if (value == true) {
                                      await _addPromoCodeEligibility(
                                        promoCode.id!,
                                        customer['id'],
                                      );
                                      setDialogState(() {
                                        eligibleCustomers.add({
                                          'customer_id': customer['id'],
                                          'promo_code_id': promoCode.id,
                                        });
                                      });
                                    } else {
                                      final eligibility = eligibleCustomers
                                          .firstWhere(
                                            (e) =>
                                                e['customer_id'] ==
                                                customer['id'],
                                            orElse: () => {},
                                          );
                                      if (eligibility.containsKey('id')) {
                                        await _deletePromoCodeEligibility(
                                          eligibility['id'],
                                        );
                                      }
                                      setDialogState(() {
                                        eligibleCustomers.removeWhere(
                                          (e) =>
                                              e['customer_id'] ==
                                              customer['id'],
                                        );
                                      });
                                    }
                                  } catch (e) {
                                    _showErrorSnackBar(
                                      'Error updating eligibility: $e',
                                    );
                                  } finally {
                                    if (mounted) {
                                      setDialogState(() {
                                        _isLoading = false;
                                      });
                                    }
                                  }
                                },
                              );
                            },
                          ),
                        ),
                    ],
                  ),
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: Text(AppLocalizations.of(context)?.close ?? 'Close'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Future<List<Map<String, dynamic>>> _fetchPromoCodeUsages(
    String promoCodeId,
  ) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final response = await http.get(
        Uri.parse(
          '$_baseUrl/get/select_promo_code_usages.php?promo_code_id=$promoCodeId',
        ),
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        setState(() {
          _isLoading = false;
        });
        return data.cast<Map<String, dynamic>>();
      } else {
        _showErrorSnackBar('Failed to load promo code usages');
        setState(() {
          _isLoading = false;
        });
        return [];
      }
    } catch (e) {
      _showErrorSnackBar('Error: $e');
      setState(() {
        _isLoading = false;
      });
      return [];
    }
  }

  Future<void> _addPromoCodeUsage(String promoCodeId, String customerId) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/insert/insert_promo_code_usage.php'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          "promo_code_id": promoCodeId,
          "customer_id": customerId,
          "used_at": DateTime.now().toIso8601String(),
        }),
      );

      if (response.statusCode == 200) {
        _showSuccessSnackBar('Promo code usage added successfully');
        _fetchPromoCodes(); // Refresh to update usesCount
      } else {
        _showErrorSnackBar('Failed to add promo code usage: ${response.body}');
      }
    } catch (e) {
      _showErrorSnackBar('Error: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _deletePromoCodeUsage(String id) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/delete/delete_promo_code_usage.php'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({"id": id}),
      );

      if (response.statusCode == 200) {
        _showSuccessSnackBar('Promo code usage deleted successfully');
        _fetchPromoCodes(); // Refresh to update usesCount
      } else {
        _showErrorSnackBar(
          'Failed to delete promo code usage: ${response.body}',
        );
      }
    } catch (e) {
      _showErrorSnackBar('Error: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showPromoCodeUsageDialog(PromoCode promoCode) async {
    setState(() {
      _isLoading = true;
    });

    List<Map<String, dynamic>> usages = [];
    List<Map<String, dynamic>> eligibleCustomers = [];

    try {
      // Fetch current usages
      usages = await _fetchPromoCodeUsages(promoCode.id!);

      // Fetch eligible customers
      eligibleCustomers = await _fetchPromoCodeEligibility(promoCode.id!);

      // Fetch customer details for each eligible customer
      final List<Map<String, dynamic>> customers = [];

      for (var eligibility in eligibleCustomers) {
        final customerResponse = await http.get(
          Uri.parse(
            'https://backend2.hemmasportacademy.com/customers/select_all.php?id=${eligibility['customer_id']}',
          ),
        );

        if (customerResponse.statusCode == 200) {
          final List<dynamic> data = json.decode(customerResponse.body);
          if (data.isNotEmpty) {
            customers.add(data.first);
          }
        }
      }

      setState(() {
        _isLoading = false;
      });

      if (!mounted) return;

      showDialog(
        context: context,
        builder: (BuildContext context) {
          return StatefulBuilder(
            builder: (context, setDialogState) {
              return AlertDialog(
                title: Text(
                  AppLocalizations.of(context)?.usages ?? 'Promo Code Usages',
                ),
                content: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Usage statistics
                      Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                AppLocalizations.of(context)?.usageStatistics ??
                                    'Usage Statistics',
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                              const SizedBox(height: 8),
                              _buildInfoRow(
                                AppLocalizations.of(context)?.totalUsages ??
                                    'Total Usages',
                                '${usages.length}',
                              ),
                              _buildInfoRow(
                                AppLocalizations.of(context)?.maxUses ??
                                    'Maximum Uses',
                                '${promoCode.maxUses}',
                              ),
                              _buildInfoRow(
                                AppLocalizations.of(context)?.remaining ??
                                    'Remaining',
                                '${promoCode.maxUses - usages.length}',
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Usage history
                      if (usages.isNotEmpty) ...[
                        Text(
                          AppLocalizations.of(context)?.usageHistory ??
                              'Usage History',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        const SizedBox(height: 8),
                        ...usages.map((usage) {
                          // Find customer name
                          final customer = customers.firstWhere(
                            (c) => c['id'] == usage['customer_id'],
                            orElse: () => {'name': 'Unknown Customer'},
                          );

                          return ListTile(
                            title: Text(customer['name'] ?? 'Unknown Customer'),
                            subtitle: Text(
                              'Used on: ${DateFormat('yyyy-MM-dd HH:mm').format(DateTime.parse(usage['used_at']))}',
                            ),
                            trailing: IconButton(
                              icon: const Icon(Icons.delete, color: Colors.red),
                              onPressed: () async {
                                await _deletePromoCodeUsage(usage['id']);
                                setDialogState(() {
                                  usages.removeWhere(
                                    (u) => u['id'] == usage['id'],
                                  );
                                });
                              },
                            ),
                          );
                        }).toList(),
                      ] else ...[
                        Column(
                          children: [
                            Text(
                              AppLocalizations.of(context)?.noUsagesYet ??
                                  'No usages recorded yet',
                              style: const TextStyle(
                                fontStyle: FontStyle.italic,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ],
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    child: Text(AppLocalizations.of(context)?.close ?? 'Close'),
                  ),
                ],
              );
            },
          );
        },
      );
    } catch (e) {
      _showErrorSnackBar('Error: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }
}
