import 'package:flutter/material.dart';
import 'package:hemmaerp/models/customers.dart';

class AccountTypeScreen extends StatefulWidget {
  final Customer customer;
  final Function(Customer) onUpdate;

  const AccountTypeScreen({
    Key? key,
    required this.customer,
    required this.onUpdate,
  }) : super(key: key);

  @override
  AccountTypeScreenState createState() => AccountTypeScreenState();
}

class AccountTypeScreenState extends State<AccountTypeScreen> {
  late String _accountType;
  late int? _numberOfChildren;

  @override
  void initState() {
    super.initState();
    _accountType = widget.customer.accountType;
    _numberOfChildren = widget.customer.numberOfChildren;
  }

  void _updateAccountType(String type) {
    setState(() {
      _accountType = type;
      if (type == 'Individual') {
        _numberOfChildren = null;
      } else {
        _numberOfChildren ??= 1;
      }
      widget.onUpdate(
        widget.customer.copyWith(
          accountType: _accountType,
          numberOfChildren: _numberOfChildren,
        ),
      );
    });
  }

  void _updateNumberOfChildren(int? value) {
    setState(() {
      _numberOfChildren = value;
      widget.onUpdate(
        widget.customer.copyWith(numberOfChildren: _numberOfChildren),
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header Section
          Text(
            'Select Account Type',
            style: theme.textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.primary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Choose the type of registration that best fits your needs',
            style: theme.textTheme.bodyLarge?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 32),

          // Individual Account Option
          _buildAccountTypeCard(
            context: context,
            title: 'Individual Account',
            description: 'Register one person for training',
            icon: Icons.person,
            value: 'Individual',
            isSelected: _accountType == 'Individual',
            features: [
              'Single person registration',
              'Personal training schedule',
              'Individual progress tracking',
              'Flexible payment options',
            ],
          ),

          const SizedBox(height: 20),

          // Family Account Option
          _buildAccountTypeCard(
            context: context,
            title: 'Family Account',
            description: 'Register multiple children under one parent/guardian',
            icon: Icons.family_restroom,
            value: 'Family',
            isSelected: _accountType == 'Family',
            features: [
              'Multiple children registration',
              'Shared parent information',
              'Bulk registration process',
              'Family discount eligibility',
            ],
          ),
          // Number of Children Section (only for Family accounts)
          if (_accountType == 'Family') ...[
            const SizedBox(height: 24),
            _buildNumberOfChildrenSection(context),
          ],
        ],
      ),
    );
  }

  Widget _buildAccountTypeCard({
    required BuildContext context,
    required String title,
    required String description,
    required IconData icon,
    required String value,
    required bool isSelected,
    required List<String> features,
  }) {
    final theme = Theme.of(context);

    return GestureDetector(
      onTap: () => _updateAccountType(value),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        decoration: BoxDecoration(
          border: Border.all(
            color:
                isSelected
                    ? theme.colorScheme.primary
                    : theme.colorScheme.outline.withOpacity(0.3),
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(16),
          color:
              isSelected
                  ? theme.colorScheme.primaryContainer.withOpacity(0.3)
                  : theme.colorScheme.surface,
          boxShadow:
              isSelected
                  ? [
                    BoxShadow(
                      color: theme.colorScheme.primary.withOpacity(0.2),
                      blurRadius: 12,
                      offset: const Offset(0, 4),
                    ),
                  ]
                  : [
                    BoxShadow(
                      color: theme.colorScheme.shadow.withOpacity(0.1),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color:
                        isSelected
                            ? theme.colorScheme.primary
                            : theme.colorScheme.primaryContainer.withOpacity(
                              0.5,
                            ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    icon,
                    color:
                        isSelected
                            ? theme.colorScheme.onPrimary
                            : theme.colorScheme.primary,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color:
                              isSelected
                                  ? theme.colorScheme.primary
                                  : theme.colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        description,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurface.withOpacity(0.7),
                        ),
                      ),
                    ],
                  ),
                ),
                Radio<String>(
                  value: value,
                  groupValue: _accountType,
                  onChanged: (value) {
                    if (value != null) {
                      _updateAccountType(value);
                    }
                  },
                  activeColor: theme.colorScheme.primary,
                  fillColor: MaterialStateProperty.resolveWith<Color>((states) {
                    if (states.contains(MaterialState.selected)) {
                      return theme.colorScheme.primary;
                    }
                    return theme.colorScheme.outline;
                  }),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...features.map(
              (feature) => Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Row(
                  children: [
                    Icon(
                      Icons.check_circle_rounded,
                      size: 16,
                      color:
                          isSelected
                              ? theme.colorScheme.primary
                              : theme.colorScheme.primary.withOpacity(0.6),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        feature,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface.withOpacity(0.8),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNumberOfChildrenSection(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        border: Border.all(color: theme.colorScheme.outline.withOpacity(0.3)),
        borderRadius: BorderRadius.circular(16),
        color: theme.colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Number of Children',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'How many children would you like to register? (Maximum 10)',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              IconButton(
                onPressed:
                    _numberOfChildren != null && _numberOfChildren! > 1
                        ? () => _updateNumberOfChildren(_numberOfChildren! - 1)
                        : null,
                icon: const Icon(Icons.remove_circle_outline),
                style: IconButton.styleFrom(
                  backgroundColor: theme.colorScheme.primaryContainer
                      .withOpacity(0.5),
                  foregroundColor: theme.colorScheme.primary,
                  disabledBackgroundColor: theme.colorScheme.surfaceVariant,
                  disabledForegroundColor: theme.colorScheme.onSurfaceVariant
                      .withOpacity(0.5),
                ),
              ),
              Expanded(
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: theme.colorScheme.outline.withOpacity(0.3),
                    ),
                    borderRadius: BorderRadius.circular(12),
                    color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                  ),
                  child: Text(
                    '${_numberOfChildren ?? 1} ${(_numberOfChildren ?? 1) == 1 ? 'Child' : 'Children'}',
                    textAlign: TextAlign.center,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                ),
              ),
              IconButton(
                onPressed:
                    (_numberOfChildren ?? 0) < 10
                        ? () => _updateNumberOfChildren(
                          (_numberOfChildren ?? 0) + 1,
                        )
                        : null,
                icon: const Icon(Icons.add_circle_outline),
                style: IconButton.styleFrom(
                  backgroundColor: theme.colorScheme.primaryContainer
                      .withOpacity(0.5),
                  foregroundColor: theme.colorScheme.primary,
                  disabledBackgroundColor: theme.colorScheme.surfaceVariant,
                  disabledForegroundColor: theme.colorScheme.onSurfaceVariant
                      .withOpacity(0.5),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
