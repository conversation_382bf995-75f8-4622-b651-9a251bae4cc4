import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:hemmaerp/models/customers.dart';

class AccountTypeScreen extends StatefulWidget {
  final Customer customer;
  final Function(Customer) onUpdate;

  const AccountTypeScreen({
    Key? key,
    required this.customer,
    required this.onUpdate,
  }) : super(key: key);

  @override
  AccountTypeScreenState createState() => AccountTypeScreenState();
}

class AccountTypeScreenState extends State<AccountTypeScreen> {
  late String _accountType;
  late int? _numberOfChildren;

  @override
  void initState() {
    super.initState();
    _accountType = widget.customer.accountType;
    _numberOfChildren = widget.customer.numberOfChildren;
  }

  void _updateAccountType(String type) {
    setState(() {
      _accountType = type;
      if (type == 'Individual') {
        _numberOfChildren = null;
      } else _numberOfChildren ??= 1;
      widget.onUpdate(
        widget.customer.copyWith(
          accountType: _accountType,
          numberOfChildren: _numberOfChildren,
        ),
      );
    });
  }

  void _updateNumberOfChildren(int? value) {
    setState(() {
      _numberOfChildren = value;
      widget.onUpdate(
        widget.customer.copyWith(numberOfChildren: _numberOfChildren),
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context)!.accountType,
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 24),
          RadioListTile<String>(
            title: Text(AppLocalizations.of(context)!.individual),
            value: 'Individual',
            groupValue: _accountType,
            onChanged: (value) {
              if (value != null) {
                _updateAccountType(value);
              }
            },
          ),
          RadioListTile<String>(
            title: Text(AppLocalizations.of(context)!.family),
            value: 'Family',
            groupValue: _accountType,
            onChanged: (value) {
              if (value != null) {
                _updateAccountType(value);
              }
            },
          ),
          if (_accountType == 'Family') ...[
            const SizedBox(height: 24),
            Text(
              AppLocalizations.of(context)!.numberOfChildren,
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                IconButton(
                  icon: const Icon(Icons.remove),
                  onPressed:
                      _numberOfChildren != null && _numberOfChildren! > 1
                          ? () =>
                              _updateNumberOfChildren(_numberOfChildren! - 1)
                          : null,
                ),
                Expanded(
                  child: TextField(
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      hintText: AppLocalizations.of(context)!.numberOfChildren,
                    ),
                    controller: TextEditingController(
                      text: _numberOfChildren?.toString() ?? '',
                    ),
                    onChanged: (value) {
                      final number = int.tryParse(value);
                      if (number != null && number > 0) {
                        _updateNumberOfChildren(number);
                      }
                    },
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.add),
                  onPressed:
                      () =>
                          _updateNumberOfChildren((_numberOfChildren ?? 0) + 1),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }
}
