import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:hemmaerp/models/customers.dart';
import 'package:hemmaerp/widgets/forms/date_picker_field.dart';

class FamilyMemberScreen extends StatefulWidget {
  final Customer customer;
  final int memberIndex;
  final Function(Customer) onUpdate;

  const FamilyMemberScreen({
    Key? key,
    required this.customer,
    required this.memberIndex,
    required this.onUpdate,
  }) : super(key: key);

  @override
  State<FamilyMemberScreen> createState() => _FamilyMemberScreenState();
}

class _FamilyMemberScreenState extends State<FamilyMemberScreen> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late DateTime? _selectedBirthdate;
  late String _selectedGender;
  late String _selectedNationality;
  late TextEditingController _healthConditionsController;
  late String _selectedSportActivity;
  late String _selectedSportLevel;
  late String _selectedTrainer;
  late String _selectedSubscriptionType;
  late String _selectedSubscriptionDuration;
  late int _numberOfSessions;
  late String _selectedTrainingTimes;
  late TextEditingController _paymentNoteController;
  late double _totalFees;
  late TextEditingController _birthdateController;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController();
    _selectedBirthdate = null;
    _selectedGender = 'Male';
    _selectedNationality = '';
    _healthConditionsController = TextEditingController();
    _selectedSportActivity = '';
    _selectedSportLevel = '';
    _selectedTrainer = '';
    _selectedSubscriptionType = '';
    _selectedSubscriptionDuration = '';
    _numberOfSessions = 0;
    _selectedTrainingTimes = '';
    _paymentNoteController = TextEditingController();
    _totalFees = 0.0;
    _birthdateController = TextEditingController();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _healthConditionsController.dispose();
    _paymentNoteController.dispose();
    _birthdateController.dispose();
    super.dispose();
  }

  void _updateCustomer() {
    if (_formKey.currentState!.validate()) {
      final updatedCustomer = widget.customer.copyWith(
        name: _nameController.text,
        birthdate: _selectedBirthdate?.toIso8601String().split('T')[0] ?? '',
        gender: _selectedGender,
        nationality: _selectedNationality,
        healthConditions: _healthConditionsController.text,
        sportActivity: _selectedSportActivity,
        sportLevel: _selectedSportLevel,
        trainer: _selectedTrainer,
        subscriptionType: _selectedSubscriptionType,
        subscriptionDuration: _selectedSubscriptionDuration,
        numberOfSessions: _numberOfSessions,
        trainingTimes: _selectedTrainingTimes,
        paymentNote: _paymentNoteController.text,
        totalFees: _totalFees,
      );
      widget.onUpdate(updatedCustomer);
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${AppLocalizations.of(context)!.familyMember} ${widget.memberIndex + 1}',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 24),

            // Name field
            TextFormField(
              controller: _nameController,
              decoration: InputDecoration(
                labelText: AppLocalizations.of(context)!.name,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return AppLocalizations.of(context)!.pleaseEnterName;
                }
                return null;
              },
              onChanged: (_) => _updateCustomer(),
            ),
            const SizedBox(height: 16),

            // Birthdate field
            DatePickerField(
              label: AppLocalizations.of(context)!.birthdate,
              controller: _birthdateController,
              onChanged: (date) {
                setState(() {
                  _selectedBirthdate = DateTime.tryParse(date);
                });
                _updateCustomer();
              },
            ),
            const SizedBox(height: 16),

            // Gender field
            DropdownButtonFormField<String>(
              value: _selectedGender,
              decoration: InputDecoration(
                labelText: AppLocalizations.of(context)!.gender,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              items: [
                DropdownMenuItem(
                  value: 'Male',
                  child: Text(AppLocalizations.of(context)!.male),
                ),
                DropdownMenuItem(
                  value: 'Female',
                  child: Text(AppLocalizations.of(context)!.female),
                ),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedGender = value!;
                });
                _updateCustomer();
              },
            ),
            const SizedBox(height: 16),

            // Nationality field
            TextFormField(
              initialValue: _selectedNationality,
              decoration: InputDecoration(
                labelText: AppLocalizations.of(context)!.nationality,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return AppLocalizations.of(context)!.pleaseEnterNationality;
                }
                return null;
              },
              onChanged: (value) {
                setState(() {
                  _selectedNationality = value;
                });
                _updateCustomer();
              },
            ),
            const SizedBox(height: 16),

            // Health conditions field
            TextFormField(
              controller: _healthConditionsController,
              decoration: InputDecoration(
                labelText: AppLocalizations.of(context)!.healthConditions,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              maxLines: 3,
              onChanged: (_) => _updateCustomer(),
            ),
            const SizedBox(height: 16),

            // Sport activity field
            TextFormField(
              initialValue: _selectedSportActivity,
              decoration: InputDecoration(
                labelText: AppLocalizations.of(context)!.sportActivity,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return AppLocalizations.of(context)!.pleaseEnterSportActivity;
                }
                return null;
              },
              onChanged: (value) {
                setState(() {
                  _selectedSportActivity = value;
                });
                _updateCustomer();
              },
            ),
            const SizedBox(height: 16),

            // Sport level field
            TextFormField(
              initialValue: _selectedSportLevel,
              decoration: InputDecoration(
                labelText: AppLocalizations.of(context)!.sportLevel,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return AppLocalizations.of(context)!.pleaseEnterSportLevel;
                }
                return null;
              },
              onChanged: (value) {
                setState(() {
                  _selectedSportLevel = value;
                });
                _updateCustomer();
              },
            ),
            const SizedBox(height: 16),

            // Trainer field
            TextFormField(
              initialValue: _selectedTrainer,
              decoration: InputDecoration(
                labelText: AppLocalizations.of(context)!.trainer,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return AppLocalizations.of(context)!.pleaseEnterTrainer;
                }
                return null;
              },
              onChanged: (value) {
                setState(() {
                  _selectedTrainer = value;
                });
                _updateCustomer();
              },
            ),
            const SizedBox(height: 16),

            // Subscription type field
            TextFormField(
              initialValue: _selectedSubscriptionType,
              decoration: InputDecoration(
                labelText: AppLocalizations.of(context)!.subscriptionType,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return AppLocalizations.of(
                    context,
                  )!.pleaseEnterSubscriptionType;
                }
                return null;
              },
              onChanged: (value) {
                setState(() {
                  _selectedSubscriptionType = value;
                });
                _updateCustomer();
              },
            ),
            const SizedBox(height: 16),

            // Subscription duration field
            TextFormField(
              initialValue: _selectedSubscriptionDuration,
              decoration: InputDecoration(
                labelText: AppLocalizations.of(context)!.subscriptionDuration,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter subscription duration.';
                }
                return null;
              },
              onChanged: (value) {
                setState(() {
                  _selectedSubscriptionDuration = value;
                });
                _updateCustomer();
              },
            ),
            const SizedBox(height: 16),

            // Number of sessions field
            TextFormField(
              initialValue: _numberOfSessions.toString(),
              decoration: InputDecoration(
                labelText: AppLocalizations.of(context)!.numberOfSessions,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return AppLocalizations.of(
                    context,
                  )!.pleaseEnterNumberOfSessions;
                }
                final number = int.tryParse(value);
                if (number == null || number <= 0) {
                  return 'Please enter a valid number of sessions.';
                }
                return null;
              },
              onChanged: (value) {
                final number = int.tryParse(value);
                if (number != null) {
                  setState(() {
                    _numberOfSessions = number;
                  });
                  _updateCustomer();
                }
              },
            ),
            const SizedBox(height: 16),

            // Training times field
            TextFormField(
              initialValue: _selectedTrainingTimes,
              decoration: InputDecoration(
                labelText: AppLocalizations.of(context)!.trainingTimes,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter training times.';
                }
                return null;
              },
              onChanged: (value) {
                setState(() {
                  _selectedTrainingTimes = value;
                });
                _updateCustomer();
              },
            ),
            const SizedBox(height: 16),

            // Payment note field
            TextFormField(
              controller: _paymentNoteController,
              decoration: InputDecoration(
                labelText: AppLocalizations.of(context)!.paymentNote,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              maxLines: 3,
              onChanged: (_) => _updateCustomer(),
            ),
            const SizedBox(height: 16),

            // Total fees field
            TextFormField(
              initialValue: _totalFees.toString(),
              decoration: InputDecoration(
                labelText: AppLocalizations.of(context)!.totalFees,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return AppLocalizations.of(context)!.pleaseEnterFees;
                }
                final number = double.tryParse(value);
                if (number == null || number < 0) {
                  return AppLocalizations.of(context)!.pleaseEnterValidAmount;
                }
                return null;
              },
              onChanged: (value) {
                final number = double.tryParse(value);
                if (number != null) {
                  setState(() {
                    _totalFees = number;
                  });
                  _updateCustomer();
                }
              },
            ),
          ],
        ),
      ),
    );
  }
}
