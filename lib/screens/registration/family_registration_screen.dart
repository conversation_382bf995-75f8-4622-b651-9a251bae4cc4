import 'package:flutter/material.dart';
import 'package:hemmaerp/models/customers.dart';
import 'package:hemmaerp/widgets/forms/form_input_field.dart';
import 'package:hemmaerp/widgets/forms/dropdown_field.dart';
import 'package:hemmaerp/widgets/forms/date_picker_field.dart';
import 'package:hemmaerp/utils/validators.dart';

class FamilyRegistrationScreen extends StatefulWidget {
  final Customer customer;
  final List<Customer> familyMembers;
  final Function(Customer, List<Customer>) onUpdate;

  const FamilyRegistrationScreen({
    Key? key,
    required this.customer,
    required this.familyMembers,
    required this.onUpdate,
  }) : super(key: key);

  @override
  State<FamilyRegistrationScreen> createState() =>
      _FamilyRegistrationScreenState();
}

class _FamilyRegistrationScreenState extends State<FamilyRegistrationScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  late TabController _tabController;

  // Parent/Guardian Information Controllers
  final _parentNameController = TextEditingController();
  final _parentEmailController = TextEditingController();
  final _parentPhoneController = TextEditingController();
  final _parentAddressController = TextEditingController();
  final _parentNationalIdController = TextEditingController();
  String _parentNationality = 'Saudi';

  // Children Information
  List<Map<String, dynamic>> _childrenData = [];

  // Constants
  static const List<String> _nationalityOptions = ['Saudi', 'Non-Saudi'];
  static const List<String> _genderOptions = ['Male', 'Female'];
  static const List<String> _sportLevels = [
    'Beginner',
    'Intermediate',
    'Advanced',
  ];
  static const List<String> _subscriptionTypes = [
    'Monthly',
    'Quarterly',
    'Yearly',
  ];

  @override
  void initState() {
    super.initState();
    _initializeData();
    _tabController = TabController(
      length: widget.customer.numberOfChildren! + 1, // +1 for parent info tab
      vsync: this,
    );
  }

  void _initializeData() {
    // Initialize parent data
    _parentNameController.text = widget.customer.name;
    _parentEmailController.text = widget.customer.email;
    _parentPhoneController.text = widget.customer.phone;
    _parentAddressController.text = widget.customer.address;
    _parentNationalIdController.text = widget.customer.nationalId;
    _parentNationality =
        widget.customer.nationality.isNotEmpty
            ? widget.customer.nationality
            : 'Saudi';

    // Initialize children data
    _childrenData = List.generate(
      widget.customer.numberOfChildren!,
      (index) => {
        'nameController': TextEditingController(),
        'healthConditionsController': TextEditingController(),
        'paymentNoteController': TextEditingController(),
        'birthdate': null as DateTime?,
        'gender': 'Male',
        'nationality': _parentNationality,
        'sportActivity': '',
        'sportLevel': 'Beginner',
        'trainer': '',
        'subscriptionType': 'Monthly',
        'subscriptionDuration': '1 Month',
        'numberOfSessions': 8,
        'trainingTimes': '',
        'totalFees': 0.0,
      },
    );

    // If we have existing family members, populate the data
    for (
      int i = 0;
      i < widget.familyMembers.length && i < _childrenData.length;
      i++
    ) {
      final member = widget.familyMembers[i];
      _childrenData[i]['nameController'].text = member.name;
      _childrenData[i]['healthConditionsController'].text =
          member.healthConditions;
      _childrenData[i]['paymentNoteController'].text = member.paymentNote ?? '';
      _childrenData[i]['birthdate'] =
          member.birthdate.isNotEmpty
              ? DateTime.tryParse(member.birthdate)
              : null;
      _childrenData[i]['gender'] =
          member.gender.isNotEmpty ? member.gender : 'Male';
      _childrenData[i]['nationality'] =
          member.nationality.isNotEmpty
              ? member.nationality
              : _parentNationality;
      _childrenData[i]['sportActivity'] = member.sportActivity;
      _childrenData[i]['sportLevel'] =
          member.sportLevel.isNotEmpty ? member.sportLevel : 'Beginner';
      _childrenData[i]['trainer'] = member.trainer;
      _childrenData[i]['subscriptionType'] =
          member.subscriptionType.isNotEmpty
              ? member.subscriptionType
              : 'Monthly';
      _childrenData[i]['subscriptionDuration'] =
          member.subscriptionDuration.isNotEmpty
              ? member.subscriptionDuration
              : '1 Month';
      _childrenData[i]['numberOfSessions'] =
          member.numberOfSessions > 0 ? member.numberOfSessions : 8;
      _childrenData[i]['trainingTimes'] = member.trainingTimes;
      _childrenData[i]['totalFees'] = member.totalFees;
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _parentNameController.dispose();
    _parentEmailController.dispose();
    _parentPhoneController.dispose();
    _parentAddressController.dispose();
    _parentNationalIdController.dispose();

    for (var childData in _childrenData) {
      childData['nameController']?.dispose();
      childData['healthConditionsController']?.dispose();
      childData['paymentNoteController']?.dispose();
    }
    super.dispose();
  }

  void _updateData() {
    if (_formKey.currentState!.validate()) {
      // Update parent customer
      final updatedParent = widget.customer.copyWith(
        name: _parentNameController.text,
        email: _parentEmailController.text,
        phone: _parentPhoneController.text,
        address: _parentAddressController.text,
        nationalId: _parentNationalIdController.text,
        nationality: _parentNationality,
        fullAddress: _parentAddressController.text,
        parentPhone: _parentPhoneController.text,
      );

      // Create updated family members
      List<Customer> updatedFamilyMembers = [];
      for (int i = 0; i < _childrenData.length; i++) {
        final childData = _childrenData[i];
        final child = Customer(
          id: i < widget.familyMembers.length ? widget.familyMembers[i].id : '',
          name: childData['nameController'].text,
          email: _parentEmailController.text, // Shared from parent
          phone: _parentPhoneController.text, // Shared from parent
          address: _parentAddressController.text, // Shared from parent
          nationalId: _parentNationalIdController.text, // Shared from parent
          birthdate:
              childData['birthdate']?.toIso8601String().split('T')[0] ?? '',
          gender: childData['gender'],
          nationality: childData['nationality'],
          parentPhone: _parentPhoneController.text,
          fullAddress: _parentAddressController.text, // Shared from parent
          healthConditions: childData['healthConditionsController'].text,
          sportActivity: childData['sportActivity'],
          sportLevel: childData['sportLevel'],
          trainer: childData['trainer'],
          subscriptionType: childData['subscriptionType'],
          subscriptionDuration: childData['subscriptionDuration'],
          numberOfSessions: childData['numberOfSessions'],
          trainingTimes: childData['trainingTimes'],
          paymentMethod: 'Cash', // Default, will be set in payment screen
          paymentNote: childData['paymentNoteController'].text,
          totalFees: childData['totalFees'],
          howDidHearAboutUs: '', // Will be set in payment screen
          accountType: 'Family',
          taxEnabled: false,
          status: 'active',
        );
        updatedFamilyMembers.add(child);
      }

      widget.onUpdate(updatedParent, updatedFamilyMembers);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      body: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.primaryColor.withOpacity(0.1),
              border: Border(bottom: BorderSide(color: theme.dividerColor)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Family Registration',
                  style: theme.textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.primaryColor,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Register parent/guardian information and ${widget.customer.numberOfChildren} children',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.textTheme.bodySmall?.color,
                  ),
                ),
              ],
            ),
          ),

          // Tab Bar
          Container(
            decoration: BoxDecoration(
              border: Border(bottom: BorderSide(color: theme.dividerColor)),
            ),
            child: TabBar(
              controller: _tabController,
              isScrollable: true,
              labelColor: theme.primaryColor,
              unselectedLabelColor: theme.textTheme.bodyMedium?.color,
              indicatorColor: theme.primaryColor,
              tabs: [
                const Tab(icon: Icon(Icons.person), text: 'Parent/Guardian'),
                ...List.generate(
                  widget.customer.numberOfChildren!,
                  (index) => Tab(
                    icon: const Icon(Icons.child_care),
                    text: 'Child ${index + 1}',
                  ),
                ),
              ],
            ),
          ),

          // Tab Views
          Expanded(
            child: Form(
              key: _formKey,
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildParentInfoTab(),
                  ...List.generate(
                    widget.customer.numberOfChildren!,
                    (index) => _buildChildInfoTab(index),
                  ),
                ],
              ),
            ),
          ),

          // Bottom Action Bar
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border(top: BorderSide(color: theme.dividerColor)),
            ),
            child: Row(
              children: [
                if (_tabController.index > 0)
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () {
                        _tabController.animateTo(_tabController.index - 1);
                      },
                      child: const Text('Previous'),
                    ),
                  ),
                if (_tabController.index > 0) const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      if (_tabController.index < _tabController.length - 1) {
                        _tabController.animateTo(_tabController.index + 1);
                      } else {
                        _updateData();
                      }
                    },
                    child: Text(
                      _tabController.index < _tabController.length - 1
                          ? 'Next'
                          : 'Continue',
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildParentInfoTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Parent/Guardian Information',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text(
            'This information will be shared across all children\'s records',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).textTheme.bodySmall?.color,
            ),
          ),
          const SizedBox(height: 24),

          FormInputField(
            label: 'Full Name',
            controller: _parentNameController,
            isRequired: true,
            validator: (value) => CustomerValidators.validateName(value),
            prefixIcon: Icons.person,
          ),
          const SizedBox(height: 16),

          FormInputField(
            label: 'Email Address',
            controller: _parentEmailController,
            keyboardType: TextInputType.emailAddress,
            isRequired: true,
            validator: (value) => CustomerValidators.validateEmail(value),
            prefixIcon: Icons.email,
          ),
          const SizedBox(height: 16),

          FormInputField(
            label: 'Phone Number',
            controller: _parentPhoneController,
            keyboardType: TextInputType.phone,
            isRequired: true,
            validator: (value) => CustomerValidators.validatePhone(value),
            prefixIcon: Icons.phone,
          ),
          const SizedBox(height: 16),

          FormInputField(
            label: 'National ID',
            controller: _parentNationalIdController,
            isRequired: true,
            validator: (value) => CustomerValidators.validateNationalId(value),
            prefixIcon: Icons.badge,
          ),
          const SizedBox(height: 16),

          DropdownField<String>(
            label: 'Nationality',
            value: _parentNationality,
            items:
                _nationalityOptions
                    .map(
                      (nationality) => DropdownMenuItem(
                        value: nationality,
                        child: Text(nationality),
                      ),
                    )
                    .toList(),
            onChanged: (value) {
              setState(() {
                _parentNationality = value!;
                // Update all children's nationality to match parent
                for (var childData in _childrenData) {
                  childData['nationality'] = value;
                }
              });
            },
            isRequired: true,
          ),
          const SizedBox(height: 16),

          FormInputField(
            label: 'Address',
            controller: _parentAddressController,
            maxLines: 3,
            isRequired: true,
            validator:
                (value) =>
                    CustomerValidators.validateRequired(value, 'Address'),
            prefixIcon: Icons.location_on,
          ),
        ],
      ),
    );
  }

  Widget _buildChildInfoTab(int index) {
    final childData = _childrenData[index];

    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.child_care,
                  color: Theme.of(context).primaryColor,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Child ${index + 1} Information',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      'Personal and training details for this child',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).textTheme.bodySmall?.color,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Personal Information Section
          _buildSectionHeader('Personal Information'),
          const SizedBox(height: 16),

          FormInputField(
            label: 'Child\'s Full Name',
            controller: childData['nameController'],
            isRequired: true,
            validator: (value) => CustomerValidators.validateName(value),
            prefixIcon: Icons.person,
          ),
          const SizedBox(height: 16),

          DatePickerField(
            label: 'Date of Birth',
            initialValue: childData['birthdate']?.toIso8601String(),
            onChanged: (value) {
              setState(() {
                childData['birthdate'] = DateTime.parse(value);
              });
            },
            validator: (value) => CustomerValidators.validateDate(value),
          ),
          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: DropdownField<String>(
                  label: 'Gender',
                  value: childData['gender'],
                  items:
                      _genderOptions
                          .map(
                            (gender) => DropdownMenuItem(
                              value: gender,
                              child: Text(gender),
                            ),
                          )
                          .toList(),
                  onChanged: (value) {
                    setState(() {
                      childData['gender'] = value!;
                    });
                  },
                  isRequired: true,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownField<String>(
                  label: 'Nationality',
                  value: childData['nationality'],
                  items:
                      _nationalityOptions
                          .map(
                            (nationality) => DropdownMenuItem(
                              value: nationality,
                              child: Text(nationality),
                            ),
                          )
                          .toList(),
                  onChanged: (value) {
                    setState(() {
                      childData['nationality'] = value!;
                    });
                  },
                  isRequired: true,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          FormInputField(
            label: 'Health Conditions',
            controller: childData['healthConditionsController'],
            maxLines: 3,
            hint: 'Any medical conditions, allergies, or special requirements',
            prefixIcon: Icons.medical_services,
          ),
          const SizedBox(height: 24),

          // Training Information Section
          _buildSectionHeader('Training Information'),
          const SizedBox(height: 16),

          FormInputField(
            label: 'Sport Activity',
            initialValue: childData['sportActivity'],
            isRequired: true,
            validator:
                (value) => CustomerValidators.validateRequired(
                  value,
                  'Sport Activity',
                ),
            onChanged: (value) {
              setState(() {
                childData['sportActivity'] = value;
              });
            },
            prefixIcon: Icons.sports,
          ),
          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: DropdownField<String>(
                  label: 'Sport Level',
                  value: childData['sportLevel'],
                  items:
                      _sportLevels
                          .map(
                            (level) => DropdownMenuItem(
                              value: level,
                              child: Text(level),
                            ),
                          )
                          .toList(),
                  onChanged: (value) {
                    setState(() {
                      childData['sportLevel'] = value!;
                    });
                  },
                  isRequired: true,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: FormInputField(
                  label: 'Trainer',
                  initialValue: childData['trainer'],
                  isRequired: true,
                  validator:
                      (value) =>
                          CustomerValidators.validateRequired(value, 'Trainer'),
                  onChanged: (value) {
                    setState(() {
                      childData['trainer'] = value;
                    });
                  },
                  prefixIcon: Icons.person_pin,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: DropdownField<String>(
                  label: 'Subscription Type',
                  value: childData['subscriptionType'],
                  items:
                      _subscriptionTypes
                          .map(
                            (type) => DropdownMenuItem(
                              value: type,
                              child: Text(type),
                            ),
                          )
                          .toList(),
                  onChanged: (value) {
                    setState(() {
                      childData['subscriptionType'] = value!;
                    });
                  },
                  isRequired: true,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: FormInputField(
                  label: 'Duration',
                  initialValue: childData['subscriptionDuration'],
                  isRequired: true,
                  validator:
                      (value) => CustomerValidators.validateRequired(
                        value,
                        'Duration',
                      ),
                  onChanged: (value) {
                    setState(() {
                      childData['subscriptionDuration'] = value;
                    });
                  },
                  prefixIcon: Icons.schedule,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: FormInputField(
                  label: 'Number of Sessions',
                  initialValue: childData['numberOfSessions'].toString(),
                  keyboardType: TextInputType.number,
                  isRequired: true,
                  validator:
                      (value) => CustomerValidators.validatePositiveNumber(
                        value,
                        'Number of Sessions',
                      ),
                  onChanged: (value) {
                    final number = int.tryParse(value);
                    if (number != null) {
                      setState(() {
                        childData['numberOfSessions'] = number;
                      });
                    }
                  },
                  prefixIcon: Icons.numbers,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: FormInputField(
                  label: 'Total Fees',
                  initialValue: childData['totalFees'].toString(),
                  keyboardType: TextInputType.number,
                  isRequired: true,
                  validator:
                      (value) => CustomerValidators.validatePositiveNumber(
                        value,
                        'Total Fees',
                      ),
                  onChanged: (value) {
                    final number = double.tryParse(value);
                    if (number != null) {
                      setState(() {
                        childData['totalFees'] = number;
                      });
                    }
                  },
                  prefixIcon: Icons.attach_money,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          FormInputField(
            label: 'Training Times',
            initialValue: childData['trainingTimes'],
            isRequired: true,
            validator:
                (value) => CustomerValidators.validateRequired(
                  value,
                  'Training Times',
                ),
            onChanged: (value) {
              setState(() {
                childData['trainingTimes'] = value;
              });
            },
            hint: 'e.g., Monday 4:00 PM, Wednesday 5:00 PM',
            prefixIcon: Icons.access_time,
          ),
          const SizedBox(height: 16),

          FormInputField(
            label: 'Payment Notes',
            controller: childData['paymentNoteController'],
            maxLines: 2,
            hint: 'Any special payment instructions or notes',
            prefixIcon: Icons.note,
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            size: 16,
            color: Theme.of(context).primaryColor,
          ),
          const SizedBox(width: 8),
          Text(
            title,
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: Theme.of(context).primaryColor,
            ),
          ),
        ],
      ),
    );
  }
}
