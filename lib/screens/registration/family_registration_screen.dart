import 'package:flutter/material.dart';
import 'package:hemmaerp/models/customers.dart';
import 'package:hemmaerp/models/sport.dart';
import 'package:hemmaerp/widgets/forms/form_input_field.dart';
import 'package:hemmaerp/widgets/forms/date_picker_field.dart';
import 'package:hemmaerp/utils/validators.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

class FamilyRegistrationScreen extends StatefulWidget {
  final Customer customer;
  final List<Customer> familyMembers;
  final Function(Customer, List<Customer>) onUpdate;

  const FamilyRegistrationScreen({
    Key? key,
    required this.customer,
    required this.familyMembers,
    required this.onUpdate,
  }) : super(key: key);

  @override
  State<FamilyRegistrationScreen> createState() =>
      _FamilyRegistrationScreenState();
}

class _FamilyRegistrationScreenState extends State<FamilyRegistrationScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  late TabController _tabController;

  // Parent/Guardian Information Controllers
  final _parentNameController = TextEditingController();
  final _parentEmailController = TextEditingController();
  final _parentPhoneController = TextEditingController();
  final _parentAddressController = TextEditingController();
  final _parentNationalIdController = TextEditingController();
  String _parentNationality = 'Saudi';

  // Children Information
  List<Map<String, dynamic>> _childrenData = [];

  // Constants
  static const List<String> _nationalityOptions = ['Saudi', 'Non-Saudi'];
  static const List<String> _genderOptions = ['Male', 'Female'];
  static const List<String> _sportLevels = [
    'Beginner',
    'Intermediate',
    'Advanced',
  ];
  static const List<String> _subscriptionTypes = [
    'Monthly',
    'Quarterly',
    'Yearly',
  ];

  // Sports and Trainers Data
  List<Sport> _availableSports = [];
  Map<String, List<String>> _sportTrainers = {};
  Map<String, String> _sportTrainingTimes = {};
  bool _isLoadingSports = false;

  @override
  void initState() {
    super.initState();
    _initializeData();
    _loadSports();
    _tabController = TabController(
      length: widget.customer.numberOfChildren! + 1, // +1 for parent info tab
      vsync: this,
    );
  }

  void _initializeData() {
    // Initialize parent data
    _parentNameController.text = widget.customer.name;
    _parentEmailController.text = widget.customer.email;
    _parentPhoneController.text = widget.customer.phone;
    _parentAddressController.text = widget.customer.address;
    _parentNationalIdController.text = widget.customer.nationalId;
    _parentNationality =
        widget.customer.nationality.isNotEmpty
            ? widget.customer.nationality
            : 'Saudi';

    // Initialize children data
    _childrenData = List.generate(
      widget.customer.numberOfChildren!,
      (index) => {
        'nameController': TextEditingController(),
        'healthConditionsController': TextEditingController(),
        'paymentNoteController': TextEditingController(),
        'birthdate': null as DateTime?,
        'gender': 'Male',
        'nationality': _parentNationality,
        'selectedSports': <Map<String, dynamic>>[], // Multiple sports per child
        'totalFees': 0.0,
      },
    );

    // If we have existing family members, populate the data
    for (
      int i = 0;
      i < widget.familyMembers.length && i < _childrenData.length;
      i++
    ) {
      final member = widget.familyMembers[i];
      _childrenData[i]['nameController'].text = member.name;
      _childrenData[i]['healthConditionsController'].text =
          member.healthConditions;
      _childrenData[i]['paymentNoteController'].text = member.paymentNote ?? '';
      _childrenData[i]['birthdate'] =
          member.birthdate.isNotEmpty
              ? DateTime.tryParse(member.birthdate)
              : null;
      _childrenData[i]['gender'] =
          member.gender.isNotEmpty ? member.gender : 'Male';
      _childrenData[i]['nationality'] =
          member.nationality.isNotEmpty
              ? member.nationality
              : _parentNationality;

      // Initialize with existing sport data if available
      if (member.sportActivity.isNotEmpty) {
        _childrenData[i]['selectedSports'] = [
          {
            'sportName': member.sportActivity,
            'sportLevel':
                member.sportLevel.isNotEmpty ? member.sportLevel : 'Beginner',
            'trainer': member.trainer,
            'subscriptionType':
                member.subscriptionType.isNotEmpty
                    ? member.subscriptionType
                    : 'Monthly',
            'subscriptionDuration':
                member.subscriptionDuration.isNotEmpty
                    ? member.subscriptionDuration
                    : '1 Month',
            'numberOfSessions':
                member.numberOfSessions > 0 ? member.numberOfSessions : 8,
            'selectedTrainingTimes':
                member.trainingTimes.isNotEmpty
                    ? [member.trainingTimes]
                    : <String>[],
            'fees': member.totalFees,
          },
        ];
      } else {
        _childrenData[i]['selectedSports'] = <Map<String, dynamic>>[];
      }

      _childrenData[i]['totalFees'] = member.totalFees;
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _parentNameController.dispose();
    _parentEmailController.dispose();
    _parentPhoneController.dispose();
    _parentAddressController.dispose();
    _parentNationalIdController.dispose();

    for (var childData in _childrenData) {
      childData['nameController']?.dispose();
      childData['healthConditionsController']?.dispose();
      childData['paymentNoteController']?.dispose();
    }
    super.dispose();
  }

  Future<void> _loadSports() async {
    setState(() {
      _isLoadingSports = true;
    });

    try {
      final response = await http.get(
        Uri.parse('https://backend2.hemmasportacademy.com/fetch_sports.php'),
      );

      if (response.statusCode == 200) {
        final List<dynamic> sportsJson = json.decode(response.body);
        setState(() {
          _availableSports =
              sportsJson
                  .map(
                    (json) => Sport(
                      id: int.parse(json['id'].toString()),
                      name: json['name'] ?? '',
                      trainers: json['trainers'] ?? '',
                      fees: double.parse(json['fees'].toString()),
                      number_of_sessions: int.parse(
                        json['number_of_sessions'].toString(),
                      ),
                      training_time: json['training_time'] ?? '',
                      training_days: json['training_time'] ?? '',
                    ),
                  )
                  .toList();

          // Build trainers map
          _sportTrainers.clear();
          _sportTrainingTimes.clear();
          for (var sport in _availableSports) {
            if (sport.trainers.isNotEmpty) {
              _sportTrainers[sport.name] =
                  sport.trainers.split(',').map((t) => t.trim()).toList();
            }
            if (sport.training_time.isNotEmpty) {
              _sportTrainingTimes[sport.name] = sport.training_time;
            }
          }
        });
      }
    } catch (e) {
      // Log error and fallback to empty lists
      debugPrint('Error loading sports: $e');
      setState(() {
        _availableSports = [];
        _sportTrainers = {};
        _sportTrainingTimes = {};
      });
    } finally {
      setState(() {
        _isLoadingSports = false;
      });
    }
  }

  void _addSportToChild(int childIndex) {
    if (_availableSports.isEmpty) return;

    setState(() {
      final selectedSports =
          _childrenData[childIndex]['selectedSports']
              as List<Map<String, dynamic>>;

      // Add a new sport with default values
      selectedSports.add({
        'sportName': '',
        'sportLevel': 'Beginner',
        'trainer': '',
        'subscriptionType': 'Monthly',
        'subscriptionDuration': '1 Month',
        'numberOfSessions': 8,
        'selectedTrainingTimes': <String>[],
        'availableTrainingTimes': <String>[],
        'fees': 0.0,
      });

      _calculateChildTotalFees(childIndex);
    });
  }

  void _removeSportFromChild(int childIndex, int sportIndex) {
    setState(() {
      final selectedSports =
          _childrenData[childIndex]['selectedSports']
              as List<Map<String, dynamic>>;
      selectedSports.removeAt(sportIndex);
      _calculateChildTotalFees(childIndex);
    });
  }

  void _updateChildSport(
    int childIndex,
    int sportIndex,
    Map<String, dynamic> updatedSport,
  ) {
    setState(() {
      final selectedSports =
          _childrenData[childIndex]['selectedSports']
              as List<Map<String, dynamic>>;
      selectedSports[sportIndex] = updatedSport;
      _calculateChildTotalFees(childIndex);
    });
  }

  void _onSportSelected(String sportName, int childIndex, int sportIndex) {
    final sport = _availableSports.firstWhere(
      (s) => s.name == sportName,
      orElse: () => _availableSports.first,
    );

    final selectedSports =
        _childrenData[childIndex]['selectedSports']
            as List<Map<String, dynamic>>;
    final currentSport = selectedSports[sportIndex];

    // Parse training times from API
    final availableTrainingTimes = <String>[];
    if (sport.training_time.isNotEmpty) {
      // Split by comma and clean up
      availableTrainingTimes.addAll(
        sport.training_time
            .split(',')
            .map((time) => time.trim())
            .where((time) => time.isNotEmpty),
      );
    }
    if (sport.training_days.isNotEmpty) {
      // Also include training days if different from training_time
      final days = sport.training_days
          .split(',')
          .map((day) => day.trim())
          .where((day) => day.isNotEmpty);
      for (final day in days) {
        if (!availableTrainingTimes.any((time) => time.contains(day))) {
          availableTrainingTimes.add(day);
        }
      }
    }

    final updatedSport = Map<String, dynamic>.from(currentSport);
    updatedSport['sportName'] = sportName;
    updatedSport['trainer'] = ''; // Reset trainer when sport changes
    updatedSport['numberOfSessions'] = sport.number_of_sessions;
    updatedSport['fees'] = sport.fees;
    updatedSport['availableTrainingTimes'] = availableTrainingTimes;
    updatedSport['selectedTrainingTimes'] = <String>[]; // Reset selected times

    _updateChildSport(childIndex, sportIndex, updatedSport);
  }

  void _calculateChildTotalFees(int childIndex) {
    final selectedSports =
        _childrenData[childIndex]['selectedSports']
            as List<Map<String, dynamic>>;
    double total = 0.0;
    for (final sport in selectedSports) {
      total += (sport['fees'] as double? ?? 0.0);
    }
    _childrenData[childIndex]['totalFees'] = total;
  }

  void _updateData() {
    if (_formKey.currentState!.validate()) {
      // Update parent customer
      final updatedParent = widget.customer.copyWith(
        name: _parentNameController.text,
        email: _parentEmailController.text,
        phone: _parentPhoneController.text,
        address: _parentAddressController.text,
        nationalId: _parentNationalIdController.text,
        nationality: _parentNationality,
        fullAddress: _parentAddressController.text,
        parentPhone: _parentPhoneController.text,
      );

      // Create updated family members - one record per child per sport
      List<Customer> updatedFamilyMembers = [];
      for (int i = 0; i < _childrenData.length; i++) {
        final childData = _childrenData[i];
        final selectedSports =
            childData['selectedSports'] as List<Map<String, dynamic>>;

        if (selectedSports.isEmpty) {
          // Create a basic record even if no sports selected
          final child = Customer(
            id:
                i < widget.familyMembers.length
                    ? widget.familyMembers[i].id
                    : '',
            name: childData['nameController'].text,
            email: _parentEmailController.text,
            phone: _parentPhoneController.text,
            address: _parentAddressController.text,
            nationalId: _parentNationalIdController.text,
            birthdate:
                childData['birthdate']?.toIso8601String().split('T')[0] ?? '',
            gender: childData['gender'],
            nationality: _parentNationality,
            parentPhone: _parentPhoneController.text,
            fullAddress: _parentAddressController.text,
            healthConditions: childData['healthConditionsController'].text,
            sportActivity: '',
            sportLevel: '',
            trainer: '',
            subscriptionType: '',
            subscriptionDuration: '',
            numberOfSessions: 0,
            trainingTimes: '',
            paymentMethod: 'Cash',
            paymentNote: childData['paymentNoteController'].text,
            totalFees: childData['totalFees'],
            howDidHearAboutUs: '',
            accountType: 'Family',
            taxEnabled: false,
            status: 'active',
          );
          updatedFamilyMembers.add(child);
        } else {
          // Create one record for the primary sport (first sport)
          final primarySport = selectedSports.first;
          final selectedTrainingTimes =
              primarySport['selectedTrainingTimes'] as List<String>;

          final child = Customer(
            id:
                i < widget.familyMembers.length
                    ? widget.familyMembers[i].id
                    : '',
            name: childData['nameController'].text,
            email: _parentEmailController.text,
            phone: _parentPhoneController.text,
            address: _parentAddressController.text,
            nationalId: _parentNationalIdController.text,
            birthdate:
                childData['birthdate']?.toIso8601String().split('T')[0] ?? '',
            gender: childData['gender'],
            nationality: _parentNationality,
            parentPhone: _parentPhoneController.text,
            fullAddress: _parentAddressController.text,
            healthConditions: childData['healthConditionsController'].text,
            sportActivity: primarySport['sportName'] ?? '',
            sportLevel: primarySport['sportLevel'] ?? 'Beginner',
            trainer: primarySport['trainer'] ?? '',
            subscriptionType: primarySport['subscriptionType'] ?? 'Monthly',
            subscriptionDuration:
                primarySport['subscriptionDuration'] ?? '1 Month',
            numberOfSessions: primarySport['numberOfSessions'] ?? 8,
            trainingTimes: selectedTrainingTimes.join(', '),
            paymentMethod: 'Cash',
            paymentNote: childData['paymentNoteController'].text,
            totalFees: childData['totalFees'],
            howDidHearAboutUs: '',
            accountType: 'Family',
            taxEnabled: false,
            status: 'active',
          );
          updatedFamilyMembers.add(child);
        }
      }

      widget.onUpdate(updatedParent, updatedFamilyMembers);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      body: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.primaryColor.withOpacity(0.1),
              border: Border(bottom: BorderSide(color: theme.dividerColor)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Family Registration',
                  style: theme.textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.primaryColor,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Register parent/guardian information and ${widget.customer.numberOfChildren} children',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.textTheme.bodySmall?.color,
                  ),
                ),
              ],
            ),
          ),

          // Tab Bar
          Container(
            decoration: BoxDecoration(
              border: Border(bottom: BorderSide(color: theme.dividerColor)),
            ),
            child: TabBar(
              controller: _tabController,
              isScrollable: true,
              labelColor: theme.primaryColor,
              unselectedLabelColor: theme.textTheme.bodyMedium?.color,
              indicatorColor: theme.primaryColor,
              tabs: [
                const Tab(icon: Icon(Icons.person), text: 'Parent/Guardian'),
                ...List.generate(
                  widget.customer.numberOfChildren!,
                  (index) => Tab(
                    icon: const Icon(Icons.child_care),
                    text: 'Child ${index + 1}',
                  ),
                ),
              ],
            ),
          ),

          // Tab Views
          Expanded(
            child: Form(
              key: _formKey,
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildParentInfoTab(),
                  ...List.generate(
                    widget.customer.numberOfChildren!,
                    (index) => _buildChildInfoTab(index),
                  ),
                ],
              ),
            ),
          ),

          // Bottom Action Bar
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border(top: BorderSide(color: theme.dividerColor)),
            ),
            child: Row(
              children: [
                if (_tabController.index > 0)
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () {
                        _tabController.animateTo(_tabController.index - 1);
                      },
                      child: const Text('Previous'),
                    ),
                  ),
                if (_tabController.index > 0) const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      if (_tabController.index < _tabController.length - 1) {
                        _tabController.animateTo(_tabController.index + 1);
                      } else {
                        _updateData();
                      }
                    },
                    child: Text(
                      _tabController.index < _tabController.length - 1
                          ? 'Next'
                          : 'Continue',
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildParentInfoTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Parent/Guardian Information',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text(
            'This information will be shared across all children\'s records',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).textTheme.bodySmall?.color,
            ),
          ),
          const SizedBox(height: 24),

          FormInputField(
            label: 'Full Name',
            controller: _parentNameController,
            isRequired: true,
            validator: (value) => CustomerValidators.validateName(value),
            prefixIcon: Icons.person,
          ),
          const SizedBox(height: 16),

          FormInputField(
            label: 'Email Address',
            controller: _parentEmailController,
            keyboardType: TextInputType.emailAddress,
            isRequired: true,
            validator: (value) => CustomerValidators.validateEmail(value),
            prefixIcon: Icons.email,
          ),
          const SizedBox(height: 16),

          FormInputField(
            label: 'Phone Number',
            controller: _parentPhoneController,
            keyboardType: TextInputType.phone,
            isRequired: true,
            validator: (value) => CustomerValidators.validatePhone(value),
            prefixIcon: Icons.phone,
          ),
          const SizedBox(height: 16),

          FormInputField(
            label: 'National ID',
            controller: _parentNationalIdController,
            isRequired: true,
            validator: (value) => CustomerValidators.validateNationalId(value),
            prefixIcon: Icons.badge,
          ),
          const SizedBox(height: 16),

          DropdownButtonFormField<String>(
            value: _parentNationality,
            decoration: InputDecoration(
              labelText: 'Nationality',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            items:
                _nationalityOptions
                    .map(
                      (nationality) => DropdownMenuItem(
                        value: nationality,
                        child: Text(
                          nationality,
                          style: TextStyle(
                            color: Theme.of(context).textTheme.bodyLarge?.color,
                          ),
                        ),
                      ),
                    )
                    .toList(),
            onChanged: (value) {
              setState(() {
                _parentNationality = value!;
                // Update all children's nationality to match parent
                for (var childData in _childrenData) {
                  childData['nationality'] = value;
                }
              });
            },
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please select nationality';
              }
              return null;
            },
            dropdownColor: Theme.of(context).cardColor,
            style: TextStyle(
              color: Theme.of(context).textTheme.bodyLarge?.color,
            ),
          ),
          const SizedBox(height: 16),

          FormInputField(
            label: 'Address',
            controller: _parentAddressController,
            maxLines: 3,
            isRequired: true,
            validator:
                (value) =>
                    CustomerValidators.validateRequired(value, 'Address'),
            prefixIcon: Icons.location_on,
          ),
        ],
      ),
    );
  }

  Widget _buildChildInfoTab(int index) {
    final childData = _childrenData[index];
    final selectedSports =
        childData['selectedSports'] as List<Map<String, dynamic>>;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.child_care,
                  color: Theme.of(context).primaryColor,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Child ${index + 1} Information',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      'Personal and training details for this child',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).textTheme.bodySmall?.color,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Personal Information Section
          _buildSectionHeader('Personal Information'),
          const SizedBox(height: 16),

          FormInputField(
            label: 'Child\'s Full Name',
            controller: childData['nameController'],
            isRequired: true,
            validator: (value) => CustomerValidators.validateName(value),
            prefixIcon: Icons.person,
          ),
          const SizedBox(height: 16),

          DatePickerField(
            label: 'Date of Birth',
            initialValue: childData['birthdate']?.toIso8601String(),
            onChanged: (value) {
              setState(() {
                childData['birthdate'] = DateTime.parse(value);
              });
            },
            validator: (value) => CustomerValidators.validateDate(value),
          ),
          const SizedBox(height: 16),

          DropdownButtonFormField<String>(
            value: childData['gender'],
            decoration: InputDecoration(
              labelText: 'Gender',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            items:
                _genderOptions
                    .map(
                      (gender) => DropdownMenuItem(
                        value: gender,
                        child: Text(
                          gender,
                          style: TextStyle(
                            color: Theme.of(context).textTheme.bodyLarge?.color,
                          ),
                        ),
                      ),
                    )
                    .toList(),
            onChanged: (value) {
              setState(() {
                childData['gender'] = value!;
              });
            },
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please select gender';
              }
              return null;
            },
            dropdownColor: Theme.of(context).cardColor,
            style: TextStyle(
              color: Theme.of(context).textTheme.bodyLarge?.color,
            ),
          ),
          const SizedBox(height: 16),

          FormInputField(
            label: 'Health Conditions',
            controller: childData['healthConditionsController'],
            maxLines: 3,
            hint: 'Any medical conditions, allergies, or special requirements',
            prefixIcon: Icons.medical_services,
          ),
          const SizedBox(height: 24),

          // Sports Section
          Row(
            children: [
              Expanded(child: _buildSectionHeader('Sports Activities')),
              const SizedBox(width: 16),
              ElevatedButton.icon(
                onPressed:
                    _isLoadingSports ? null : () => _addSportToChild(index),
                icon: const Icon(Icons.add, size: 18),
                label: const Text('Add Sport'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          if (_isLoadingSports)
            const Center(child: CircularProgressIndicator())
          else if (selectedSports.isEmpty)
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                border: Border.all(color: Theme.of(context).dividerColor),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.sports,
                    size: 48,
                    color: Theme.of(context).disabledColor,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No sports selected',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Theme.of(context).disabledColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Click "Add Sport" to add sports activities for this child',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).disabledColor,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            )
          else
            ...selectedSports.asMap().entries.map((entry) {
              final sportIndex = entry.key;
              final sport = entry.value;
              return _buildSportCard(index, sportIndex, sport);
            }),

          const SizedBox(height: 24),

          // Total Fees Display
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: Theme.of(context).primaryColor.withOpacity(0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.attach_money, color: Theme.of(context).primaryColor),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Total Fees for Child ${index + 1}',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                Text(
                  '${(childData['totalFees'] as double).toStringAsFixed(2)} SAR',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          FormInputField(
            label: 'Payment Notes',
            controller: childData['paymentNoteController'],
            maxLines: 2,
            hint: 'Any special payment instructions or notes',
            prefixIcon: Icons.note,
          ),
        ],
      ),
    );
  }

  Widget _buildSportCard(
    int childIndex,
    int sportIndex,
    Map<String, dynamic> sport,
  ) {
    final availableTrainingTimes =
        sport['availableTrainingTimes'] as List<String>? ?? [];
    final selectedTrainingTimes =
        sport['selectedTrainingTimes'] as List<String>? ?? [];

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Sport Header with Remove Button
            Row(
              children: [
                Icon(Icons.sports, color: Theme.of(context).primaryColor),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Sport ${sportIndex + 1}',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                IconButton(
                  onPressed:
                      () => _removeSportFromChild(childIndex, sportIndex),
                  icon: const Icon(Icons.delete_outline),
                  color: Colors.red,
                  tooltip: 'Remove Sport',
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Sport Selection
            DropdownButtonFormField<String>(
              value: sport['sportName'].isNotEmpty ? sport['sportName'] : null,
              decoration: InputDecoration(
                labelText: 'Sport Activity',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                prefixIcon: const Icon(Icons.sports),
              ),
              items:
                  _availableSports
                      .map(
                        (availableSport) => DropdownMenuItem(
                          value: availableSport.name,
                          child: Text(
                            availableSport.name,
                            style: TextStyle(
                              color:
                                  Theme.of(context).textTheme.bodyLarge?.color,
                            ),
                          ),
                        ),
                      )
                      .toList(),
              onChanged: (value) {
                if (value != null) {
                  _onSportSelected(value, childIndex, sportIndex);
                }
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please select a sport activity';
                }
                return null;
              },
              dropdownColor: Theme.of(context).cardColor,
              style: TextStyle(
                color: Theme.of(context).textTheme.bodyLarge?.color,
              ),
            ),
            const SizedBox(height: 16),

            // Sport Level and Trainer
            Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: sport['sportLevel'],
                    decoration: InputDecoration(
                      labelText: 'Sport Level',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    items:
                        _sportLevels
                            .map(
                              (level) => DropdownMenuItem(
                                value: level,
                                child: Text(
                                  level,
                                  style: TextStyle(
                                    color:
                                        Theme.of(
                                          context,
                                        ).textTheme.bodyLarge?.color,
                                  ),
                                ),
                              ),
                            )
                            .toList(),
                    onChanged: (value) {
                      if (value != null) {
                        final updatedSport = Map<String, dynamic>.from(sport);
                        updatedSport['sportLevel'] = value;
                        _updateChildSport(childIndex, sportIndex, updatedSport);
                      }
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please select sport level';
                      }
                      return null;
                    },
                    dropdownColor: Theme.of(context).cardColor,
                    style: TextStyle(
                      color: Theme.of(context).textTheme.bodyLarge?.color,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value:
                        sport['trainer'].isNotEmpty ? sport['trainer'] : null,
                    decoration: InputDecoration(
                      labelText: 'Trainer',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      prefixIcon: const Icon(Icons.person_pin),
                    ),
                    items:
                        (sport['sportName'].isNotEmpty &&
                                _sportTrainers.containsKey(sport['sportName']))
                            ? _sportTrainers[sport['sportName']]!
                                .map(
                                  (trainer) => DropdownMenuItem(
                                    value: trainer,
                                    child: Text(
                                      trainer,
                                      style: TextStyle(
                                        color:
                                            Theme.of(
                                              context,
                                            ).textTheme.bodyLarge?.color,
                                      ),
                                    ),
                                  ),
                                )
                                .toList()
                            : [],
                    onChanged: (value) {
                      final updatedSport = Map<String, dynamic>.from(sport);
                      updatedSport['trainer'] = value ?? '';
                      _updateChildSport(childIndex, sportIndex, updatedSport);
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please select a trainer';
                      }
                      return null;
                    },
                    dropdownColor: Theme.of(context).cardColor,
                    style: TextStyle(
                      color: Theme.of(context).textTheme.bodyLarge?.color,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Training Times Selection
            if (availableTrainingTimes.isNotEmpty) ...[
              Text(
                'Training Times',
                style: Theme.of(
                  context,
                ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children:
                    availableTrainingTimes.map((time) {
                      final isSelected = selectedTrainingTimes.contains(time);
                      return FilterChip(
                        label: Text(time),
                        selected: isSelected,
                        onSelected: (selected) {
                          final updatedSport = Map<String, dynamic>.from(sport);
                          final updatedTimes = List<String>.from(
                            selectedTrainingTimes,
                          );

                          if (selected) {
                            updatedTimes.add(time);
                          } else {
                            updatedTimes.remove(time);
                          }

                          updatedSport['selectedTrainingTimes'] = updatedTimes;
                          _updateChildSport(
                            childIndex,
                            sportIndex,
                            updatedSport,
                          );
                        },
                      );
                    }).toList(),
              ),
              const SizedBox(height: 16),
            ],

            // Subscription and Fees
            Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: sport['subscriptionType'],
                    decoration: InputDecoration(
                      labelText: 'Subscription Type',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    items:
                        _subscriptionTypes
                            .map(
                              (type) => DropdownMenuItem(
                                value: type,
                                child: Text(
                                  type,
                                  style: TextStyle(
                                    color:
                                        Theme.of(
                                          context,
                                        ).textTheme.bodyLarge?.color,
                                  ),
                                ),
                              ),
                            )
                            .toList(),
                    onChanged: (value) {
                      if (value != null) {
                        final updatedSport = Map<String, dynamic>.from(sport);
                        updatedSport['subscriptionType'] = value;
                        _updateChildSport(childIndex, sportIndex, updatedSport);
                      }
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please select subscription type';
                      }
                      return null;
                    },
                    dropdownColor: Theme.of(context).cardColor,
                    style: TextStyle(
                      color: Theme.of(context).textTheme.bodyLarge?.color,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      border: Border.all(color: Theme.of(context).dividerColor),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Fees',
                          style: Theme.of(context).textTheme.labelMedium,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${(sport['fees'] as double).toStringAsFixed(2)} SAR',
                          style: Theme.of(
                            context,
                          ).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).primaryColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            size: 16,
            color: Theme.of(context).primaryColor,
          ),
          const SizedBox(width: 8),
          Text(
            title,
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: Theme.of(context).primaryColor,
            ),
          ),
        ],
      ),
    );
  }
}
