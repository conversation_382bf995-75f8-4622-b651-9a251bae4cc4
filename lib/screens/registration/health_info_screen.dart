// ignore_for_file: unused_import, deprecated_member_use

import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:hemmaerp/models/customers.dart';
import 'package:hemmaerp/widgets/forms/custom_text_field.dart';

class HealthInfoScreen extends StatefulWidget {
  final Customer customer;
  final Function(Customer) onUpdate;

  const HealthInfoScreen({
    Key? key,
    required this.customer,
    required this.onUpdate,
  }) : super(key: key);

  @override
  HealthInfoScreenState createState() => HealthInfoScreenState();
}

class HealthInfoScreenState extends State<HealthInfoScreen> {
  late TextEditingController _healthConditionsController;

  @override
  void initState() {
    super.initState();
    _healthConditionsController = TextEditingController(
      text: widget.customer.healthConditions,
    );
  }

  @override
  void dispose() {
    _healthConditionsController.dispose();
    super.dispose();
  }

  void _updateHealthConditions(String value) {
    widget.onUpdate(widget.customer.copyWith(healthConditions: value));
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context)!.healthInformation,
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 24),
          TextFormField(
            controller: _healthConditionsController,
            maxLines: 5,
            decoration: InputDecoration(
              labelText: AppLocalizations.of(context)!.healthConditions,
              hintText: AppLocalizations.of(context)!.enterHealthConditions,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            onChanged: _updateHealthConditions,
          ),
        ],
      ),
    );
  }
}
