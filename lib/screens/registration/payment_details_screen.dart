import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:hemmaerp/models/customers.dart';

class PaymentDetailsScreen extends StatefulWidget {
  final Customer customer;
  final Function(Customer) onUpdate;

  const PaymentDetailsScreen({
    Key? key,
    required this.customer,
    required this.onUpdate,
  }) : super(key: key);

  @override
  PaymentDetailsScreenState createState() => PaymentDetailsScreenState();
}

class PaymentDetailsScreenState extends State<PaymentDetailsScreen> {
  late TextEditingController _paymentMethodController;
  late TextEditingController _paymentNoteController;
  late TextEditingController _totalFeesController;
  late TextEditingController _howDidHearAboutUsController;

  @override
  void initState() {
    super.initState();
    _paymentMethodController = TextEditingController(
      text: widget.customer.paymentMethod,
    );
    _paymentNoteController = TextEditingController(
      text: widget.customer.paymentNote,
    );
    _totalFeesController = TextEditingController(
      text: widget.customer.totalFees.toString(),
    );
    _howDidHearAboutUsController = TextEditingController(
      text: widget.customer.howDidHearAboutUs,
    );
  }

  @override
  void dispose() {
    _paymentMethodController.dispose();
    _paymentNoteController.dispose();
    _totalFeesController.dispose();
    _howDidHearAboutUsController.dispose();
    super.dispose();
  }

  void _updateCustomer() {
    widget.onUpdate(
      widget.customer.copyWith(
        paymentMethod: _paymentMethodController.text,
        paymentNote: _paymentNoteController.text,
        totalFees: double.tryParse(_totalFeesController.text) ?? 0.0,
        howDidHearAboutUs: _howDidHearAboutUsController.text,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context)!.paymentInformation,
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 24),
          TextFormField(
            controller: _paymentMethodController,
            decoration: InputDecoration(
              labelText: AppLocalizations.of(context)!.paymentMethod,
              hintText: 'Enter payment method',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            onChanged: (_) => _updateCustomer(),
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _paymentNoteController,
            decoration: InputDecoration(
              labelText: AppLocalizations.of(context)!.paymentNote,
              hintText: 'Enter payment note',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            onChanged: (_) => _updateCustomer(),
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _totalFeesController,
            keyboardType: TextInputType.number,
            decoration: InputDecoration(
              labelText: AppLocalizations.of(context)!.totalFees,
              hintText: 'Enter total fees',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              prefixIcon: const Icon(Icons.attach_money),
            ),
            onChanged: (_) => _updateCustomer(),
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _howDidHearAboutUsController,
            decoration: InputDecoration(
              labelText: AppLocalizations.of(context)!.howDidHearAboutUs,
              hintText: AppLocalizations.of(context)!.enterHowDidHearAboutUs,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            onChanged: (_) => _updateCustomer(),
          ),
        ],
      ),
    );
  }
}
