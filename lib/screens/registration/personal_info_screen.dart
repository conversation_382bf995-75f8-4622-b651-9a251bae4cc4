// ignore_for_file: unused_import, unnecessary_null_comparison, unnecessary_import, prefer_final_fields, unnecessary_to_list_in_spreads

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:hemmaerp/models/customers.dart';
import 'package:hemmaerp/widgets/forms/custom_text_field.dart';
import 'package:hemmaerp/widgets/forms/date_picker_field.dart';
import 'package:hemmaerp/widgets/forms/dropdown_field.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:hemmaerp/widgets/forms/form_row.dart';

// ignore_for_file: library_private_types_in_public_api

class PersonalInfoScreen extends StatefulWidget {
  final Customer customer;
  final Function(Customer) onUpdate;

  const PersonalInfoScreen({
    Key? key,
    required this.customer,
    required this.onUpdate,
  }) : super(key: key);

  @override
  State<PersonalInfoScreen> createState() => PersonalInfoScreenState();
}

class PersonalInfoScreenState extends State<PersonalInfoScreen> {
  final formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _nationalIdController = TextEditingController();
  final _parentPhoneController = TextEditingController();
  final _addressController = TextEditingController();
  String _selectedGender = 'Male';
  String _selectedNationality = '';
  DateTime? _selectedBirthdate;

  @override
  void initState() {
    super.initState();
    if (widget.customer != null) {
      _nameController.text = widget.customer.name;
      _emailController.text = widget.customer.email;
      _phoneController.text = widget.customer.phone;
      _nationalIdController.text = widget.customer.nationalId;
      _parentPhoneController.text = widget.customer.parentPhone;
      _addressController.text = widget.customer.address;
      _selectedGender = widget.customer.gender;
      _selectedNationality = widget.customer.nationality;
      if (widget.customer.birthdate.isNotEmpty) {
        try {
          _selectedBirthdate = DateTime.parse(widget.customer.birthdate);
        } catch (e) {
          _selectedBirthdate = null;
        }
      } else {
        _selectedBirthdate = null;
      }
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _nationalIdController.dispose();
    _parentPhoneController.dispose();
    _addressController.dispose();
    super.dispose();
  }

  void _onNext() {
    if (formKey.currentState!.validate()) {
      final customer = Customer(
        id: widget.customer.id,
        name: _nameController.text,
        email: _emailController.text,
        phone: _phoneController.text,
        address: _addressController.text,
        nationalId: _nationalIdController.text,
        birthdate: _selectedBirthdate?.toIso8601String() ?? '',
        gender: _selectedGender,
        nationality: _selectedNationality,
        parentPhone: _parentPhoneController.text,
        healthConditions: widget.customer.healthConditions,
        fullAddress: _addressController.text,
        sportActivity: widget.customer.sportActivity,
        sportLevel: widget.customer.sportLevel,
        trainer: widget.customer.trainer,
        subscriptionType: widget.customer.subscriptionType,
        subscriptionDuration: widget.customer.subscriptionDuration,
        numberOfSessions: widget.customer.numberOfSessions,
        trainingTimes: widget.customer.trainingTimes,
        status: widget.customer.status,
        taxEnabled: widget.customer.taxEnabled,
        paymentMethod: widget.customer.paymentMethod ?? 'Cash',
        accountType: 'Individual',
        howDidHearAboutUs: '',
        totalFees: 0.0,
      );
      widget.onUpdate(customer);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          TextFormField(
            controller: _nameController,
            decoration: InputDecoration(
              labelText: AppLocalizations.of(context)!.fullName,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your name';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _emailController,
            decoration: InputDecoration(
              labelText: AppLocalizations.of(context)!.email,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your email';
              }
              if (!value.contains('@')) {
                return 'Please enter a valid email';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _phoneController,
            decoration: InputDecoration(
              labelText: AppLocalizations.of(context)!.phoneNumber,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your phone number';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _nationalIdController,
            decoration: InputDecoration(
              labelText: AppLocalizations.of(context)!.nationalId,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your national ID';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _parentPhoneController,
            decoration: InputDecoration(
              labelText: 'Parent Phone Number',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter parent phone number';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _addressController,
            decoration: InputDecoration(
              labelText: AppLocalizations.of(context)!.address,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your address';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<String>(
            value: _selectedGender,
            decoration: InputDecoration(
              labelText: AppLocalizations.of(context)!.gender,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            items: [
              DropdownMenuItem(
                value: 'Male',
                child: Text(AppLocalizations.of(context)!.male),
              ),
              DropdownMenuItem(
                value: 'Female',
                child: Text(AppLocalizations.of(context)!.female),
              ),
            ],
            onChanged: (value) {
              setState(() {
                _selectedGender = value!;
              });
            },
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<String>(
            value: _selectedNationality,
            decoration: InputDecoration(
              labelText: AppLocalizations.of(context)!.nationality,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            items: [
              DropdownMenuItem(value: 'Saudi', child: Text('Saudi')),
              DropdownMenuItem(value: 'Non-Saudi', child: Text('Non-Saudi')),
            ],
            onChanged: (value) {
              setState(() {
                _selectedNationality = value!;
              });
            },
          ),
          const SizedBox(height: 16),
          DatePickerField(
            label: AppLocalizations.of(context)!.birthdate,
            initialValue: _selectedBirthdate?.toIso8601String(),
            onChanged: (value) {
              setState(() {
                _selectedBirthdate = DateTime.parse(value);
              });
            },
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please select your birthdate';
              }
              return null;
            },
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _onNext,
            child: Text(AppLocalizations.of(context)!.next),
          ),
        ],
      ),
    );
  }
}
