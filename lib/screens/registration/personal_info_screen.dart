// ignore_for_file: unused_import, unnecessary_null_comparison, unnecessary_import, prefer_final_fields, unnecessary_to_list_in_spreads

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:hemmaerp/models/customers.dart';
import 'package:hemmaerp/widgets/forms/custom_text_field.dart';
import 'package:hemmaerp/widgets/forms/date_picker_field.dart';
import 'package:hemmaerp/widgets/forms/dropdown_field.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:hemmaerp/widgets/forms/form_row.dart';
import 'package:hemmaerp/widgets/forms/compact_form_field.dart';
import 'package:hemmaerp/widgets/forms/form_grid.dart';

// ignore_for_file: library_private_types_in_public_api

class PersonalInfoScreen extends StatefulWidget {
  final Customer customer;
  final Function(Customer) onUpdate;

  const PersonalInfoScreen({
    Key? key,
    required this.customer,
    required this.onUpdate,
  }) : super(key: key);

  @override
  State<PersonalInfoScreen> createState() => PersonalInfoScreenState();
}

class PersonalInfoScreenState extends State<PersonalInfoScreen> {
  final formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _nationalIdController = TextEditingController();
  final _parentPhoneController = TextEditingController();
  final _addressController = TextEditingController();

  // Dropdown constants
  static const List<String> _genderOptions = ['Male', 'Female'];
  static const List<String> _nationalityOptions = ['Saudi', 'Non-Saudi'];

  String _selectedGender = 'Male';
  String _selectedNationality = 'Saudi';
  DateTime? _selectedBirthdate;

  @override
  void initState() {
    super.initState();
    if (widget.customer != null) {
      _nameController.text = widget.customer.name;
      _emailController.text = widget.customer.email;
      _phoneController.text = widget.customer.phone;
      _nationalIdController.text = widget.customer.nationalId;
      _parentPhoneController.text = widget.customer.parentPhone ?? '';
      _addressController.text = widget.customer.address;
      _selectedGender =
          widget.customer.gender.isNotEmpty &&
                  _genderOptions.contains(widget.customer.gender)
              ? widget.customer.gender
              : _genderOptions.first;
      _selectedNationality =
          widget.customer.nationality.isNotEmpty &&
                  _nationalityOptions.contains(widget.customer.nationality)
              ? widget.customer.nationality
              : _nationalityOptions.first;
      if (widget.customer.birthdate.isNotEmpty) {
        try {
          _selectedBirthdate = DateTime.parse(widget.customer.birthdate);
        } catch (e) {
          _selectedBirthdate = null;
        }
      } else {
        _selectedBirthdate = null;
      }
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _nationalIdController.dispose();
    _parentPhoneController.dispose();
    _addressController.dispose();
    super.dispose();
  }

  void _updateCustomer() {
    final customer = Customer(
      id: widget.customer.id,
      name: _nameController.text,
      email: _emailController.text,
      phone: _phoneController.text,
      address: _addressController.text,
      nationalId: _nationalIdController.text,
      birthdate: _selectedBirthdate?.toIso8601String() ?? '',
      gender: _selectedGender,
      nationality: _selectedNationality,
      parentPhone: _parentPhoneController.text,
      healthConditions: widget.customer.healthConditions,
      fullAddress: _addressController.text,
      sportActivity: widget.customer.sportActivity,
      sportLevel: widget.customer.sportLevel,
      trainer: widget.customer.trainer,
      subscriptionType: widget.customer.subscriptionType,
      subscriptionDuration: widget.customer.subscriptionDuration,
      numberOfSessions: widget.customer.numberOfSessions,
      trainingTimes: widget.customer.trainingTimes,
      status: widget.customer.status,
      taxEnabled: widget.customer.taxEnabled,
      paymentMethod: widget.customer.paymentMethod,
      accountType: 'Individual',
      howDidHearAboutUs: '',
      totalFees: 0.0,
    );
    widget.onUpdate(customer);
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Form(
        key: formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            FormCard(
              title: 'Personal Information',
              subtitle: 'Enter your basic personal details',
              icon: Icon(
                Icons.person,
                color: Theme.of(context).colorScheme.primary,
                size: 28,
              ),
              children: [
                FormGrid(
                  children: [
                    CompactFormField(
                      label: AppLocalizations.of(context)!.fullName,
                      controller: _nameController,
                      isRequired: true,
                      prefixIcon: const Icon(Icons.person_outline),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter your name';
                        }
                        return null;
                      },
                      onChanged: (value) => _updateCustomer(),
                    ),
                    CompactFormField(
                      label: AppLocalizations.of(context)!.email,
                      controller: _emailController,
                      keyboardType: TextInputType.emailAddress,
                      isRequired: true,
                      prefixIcon: const Icon(Icons.email_outlined),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter your email';
                        }
                        if (!value.contains('@')) {
                          return 'Please enter a valid email';
                        }
                        return null;
                      },
                      onChanged: (value) => _updateCustomer(),
                    ),
                    CompactFormField(
                      label: AppLocalizations.of(context)!.phoneNumber,
                      controller: _phoneController,
                      keyboardType: TextInputType.phone,
                      isRequired: true,
                      prefixIcon: const Icon(Icons.phone_outlined),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter your phone number';
                        }
                        return null;
                      },
                      onChanged: (value) => _updateCustomer(),
                    ),
                    CompactFormField(
                      label: AppLocalizations.of(context)!.nationalId,
                      controller: _nationalIdController,
                      isRequired: true,
                      prefixIcon: const Icon(Icons.badge_outlined),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter your national ID';
                        }
                        return null;
                      },
                      onChanged: (value) => _updateCustomer(),
                    ),
                    CompactFormField(
                      label: 'Parent Phone Number',
                      controller: _parentPhoneController,
                      keyboardType: TextInputType.phone,
                      isRequired: true,
                      prefixIcon: const Icon(Icons.contact_phone_outlined),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter parent phone number';
                        }
                        return null;
                      },
                      onChanged: (value) => _updateCustomer(),
                    ),
                    CompactFormField(
                      label: AppLocalizations.of(context)!.address,
                      controller: _addressController,
                      isRequired: true,
                      prefixIcon: const Icon(Icons.location_on_outlined),
                      maxLines: 2,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter your address';
                        }
                        return null;
                      },
                      onChanged: (value) => _updateCustomer(),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 8),
            FormCard(
              title: 'Additional Details',
              subtitle: 'Gender, nationality, and birthdate information',
              icon: Icon(
                Icons.info_outline,
                color: Theme.of(context).colorScheme.primary,
                size: 28,
              ),
              children: [
                FormGrid(
                  children: [
                    CompactDropdownField<String>(
                      label: AppLocalizations.of(context)!.gender,
                      value: _selectedGender,
                      isRequired: true,
                      prefixIcon: const Icon(Icons.person_outline),
                      items:
                          _genderOptions.map((String value) {
                            return DropdownMenuItem<String>(
                              value: value,
                              child: Text(
                                value == 'Male'
                                    ? AppLocalizations.of(context)!.male
                                    : AppLocalizations.of(context)!.female,
                              ),
                            );
                          }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedGender = value!;
                        });
                        _updateCustomer();
                      },
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please select your gender';
                        }
                        return null;
                      },
                    ),
                    CompactDropdownField<String>(
                      label: AppLocalizations.of(context)!.nationality,
                      value: _selectedNationality,
                      isRequired: true,
                      prefixIcon: const Icon(Icons.flag_outlined),
                      items:
                          _nationalityOptions.map((String value) {
                            return DropdownMenuItem<String>(
                              value: value,
                              child: Text(value),
                            );
                          }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedNationality = value!;
                        });
                        _updateCustomer();
                      },
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please select your nationality';
                        }
                        return null;
                      },
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                DatePickerField(
                  label: AppLocalizations.of(context)!.birthdate,
                  initialValue: _selectedBirthdate?.toIso8601String(),
                  isRequired: true,
                  showAge: true,
                  onChanged: (value) {
                    setState(() {
                      _selectedBirthdate = DateTime.parse(value);
                    });
                    _updateCustomer();
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please select your birthdate';
                    }
                    return null;
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
