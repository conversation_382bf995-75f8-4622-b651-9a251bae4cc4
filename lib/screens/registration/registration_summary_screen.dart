// ignore_for_file: unnecessary_null_comparison, unnecessary_to_list_in_spreads

import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:hemmaerp/models/customers.dart';
import 'package:hemmaerp/models/customer_sport.dart';

class RegistrationSummaryScreen extends StatelessWidget {
  final Customer customer;
  final List<CustomerSport> selectedSports;
  final double totalAmount;
  final String paymentType;

  const RegistrationSummaryScreen({
    Key? key,
    required this.customer,
    required this.selectedSports,
    required this.totalAmount,
    required this.paymentType,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Registration Summary',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 24),
          
          // Personal Information Section
          _buildSectionHeader(context, AppLocalizations.of(context)!.personalInformation),
          _buildInfoCard(
            context,
            [
              _buildInfoRow('Name', customer.name),
              _buildInfoRow('Email', customer.email),
              _buildInfoRow('Phone', customer.phone),
              _buildInfoRow('Address', customer.address),
              _buildInfoRow('National ID', customer.nationalId),
              _buildInfoRow('Birthdate', customer.birthdate),
              _buildInfoRow('Gender', customer.gender),
              _buildInfoRow('Nationality', customer.nationality),
              if (customer.parentPhone != null && customer.parentPhone.isNotEmpty)
                _buildInfoRow('Parent Phone', customer.parentPhone),
            ],
          ),
          const SizedBox(height: 16),
          
          // Health Information Section
          _buildSectionHeader(context, AppLocalizations.of(context)!.healthInformation),
          _buildInfoCard(
            context,
            [
              _buildInfoRow('Health Conditions', 
                customer.healthConditions.isNotEmpty == true ? 
                customer.healthConditions : 'None'),
            ],
          ),
          const SizedBox(height: 16),
          
          // Sports Activities Section
          _buildSectionHeader(context, AppLocalizations.of(context)!.sportsActivities),
          ...selectedSports.map((sport) => _buildSportCard(context, sport)).toList(),
          const SizedBox(height: 16),
          
          // Payment Details Section
          _buildSectionHeader(context, AppLocalizations.of(context)!.paymentDetails),
          _buildInfoCard(
            context,
            [
              _buildInfoRow('Payment Method', paymentType),
              _buildInfoRow('Total Amount (including 15% tax)', '${totalAmount.toStringAsFixed(2)} SAR'),
            ],
          ),
          const SizedBox(height: 32),
          
          // Confirmation message
          Center(
            child: Text(
              'Please review the information above before submitting',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildSectionHeader(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleLarge?.copyWith(
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
  
  Widget _buildInfoCard(BuildContext context, List<Widget> children) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: children,
        ),
      ),
    );
  }
  
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }
  
  Widget _buildSportCard(BuildContext context, CustomerSport sport) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              sport.sportName,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const Divider(),
            _buildInfoRow('Trainer', sport.trainerName),
            _buildInfoRow('Subscription Type', sport.subscriptionType),
            _buildInfoRow('Subscription Duration', sport.subscriptionDuration),
            _buildInfoRow('Number of Sessions', sport.numberOfSessions.toString()),
            _buildInfoRow('Training Times', sport.trainingTimes.join(', ')),
            _buildInfoRow('Fees', '${sport.fees.toStringAsFixed(2)} SAR'),
            if (sport.uniformIncluded ?? false)
              _buildInfoRow('Uniform Price', '${(sport.uniformPrice ?? 0).toStringAsFixed(2)} SAR'),
            _buildInfoRow('Total', '${((sport.fees) + (sport.uniformIncluded ?? false ? (sport.uniformPrice ?? 0) : 0)).toStringAsFixed(2)} SAR'),
          ],
        ),
      ),
    );
  }
}