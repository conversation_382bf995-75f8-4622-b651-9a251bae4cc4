// ignore_for_file: unnecessary_to_list_in_spreads, unnecessary_null_comparison

import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:hemmaerp/models/customers.dart';
import 'package:hemmaerp/models/customer_sport.dart';
import 'package:hemmaerp/models/sport.dart';
import 'package:hemmaerp/widgets/forms/custom_text_field.dart';
import 'package:hemmaerp/widgets/forms/dropdown_field.dart';

class SportsSelectionScreen extends StatefulWidget {
  final Customer customer;
  final List<CustomerSport> selectedSports;
  final List<Sport> availableSports;
  final List<String> subscriptionTypes;
  final Map<String, List<String>> subscriptionDurations;
  final List<Map<String, dynamic>> trainers;
  final Function(List<CustomerSport>) onUpdate;

  const SportsSelectionScreen({
    Key? key,
    required this.customer,
    required this.selectedSports,
    required this.availableSports,
    required this.subscriptionTypes,
    required this.subscriptionDurations,
    required this.trainers,
    required this.onUpdate,
  }) : super(key: key);

  @override
  State<SportsSelectionScreen> createState() => _SportsSelectionScreenState();
}

class _SportsSelectionScreenState extends State<SportsSelectionScreen> {
  List<CustomerSport> _sports = [];

  @override
  void initState() {
    super.initState();
    _sports = List.from(widget.selectedSports);

    // If no sports are selected yet, add an empty one
    if (_sports.isEmpty) {
      // Find a unique sport ID
      int? sportId;
      String sportName = '';
      double fees = 0;
      int numberOfSessions = 0;

      if (widget.availableSports.isNotEmpty) {
        final firstSport = widget.availableSports.first;
        sportId = firstSport.id;
        sportName = firstSport.name;
        fees = firstSport.fees;
        numberOfSessions = firstSport.number_of_sessions;
      }

      // Add sport without notifying parent during initialization
      setState(() {
        _sports.add(
          CustomerSport(
            id: 0,
            sportId: sportId ?? 0,
            sportName: sportName,
            sportLevel: 'Beginner', // Default sport level
            trainer: '',
            trainerId:
                widget.trainers.isNotEmpty
                    ? widget.trainers.first['id'] as int
                    : 0,
            trainerName:
                widget.trainers.isNotEmpty
                    ? widget.trainers.first['name'] as String
                    : '',
            subscriptionType:
                widget.subscriptionTypes.isNotEmpty
                    ? widget.subscriptionTypes.first
                    : '',
            subscriptionDuration:
                widget.subscriptionDurations.isNotEmpty &&
                        widget
                            .subscriptionDurations[widget
                                .subscriptionTypes
                                .first]!
                            .isNotEmpty
                    ? widget
                        .subscriptionDurations[widget.subscriptionTypes.first]!
                        .first
                    : '',
            numberOfSessions: numberOfSessions,
            trainingTimes: [],
            trainingTime: '',
            trainingDays: '',
            fees: fees,
            additionalFees: 0,
            taxAmount: fees * 0.15, // 15% tax
            totalAmount: fees + (fees * 0.15), // Total with tax
            uniformIncluded: false,
            uniformPrice: 0,
            startDate:
                DateTime.now().toString().split(
                  ' ',
                )[0], // Today's date as default start date
            endDate: '', // Will be calculated based on subscription duration
          ),
        );
      });
      // Defer parent notification until after build is complete
      WidgetsBinding.instance.addPostFrameCallback((_) {
        widget.onUpdate(_sports);
      });
    }
  }

  void _addNewSport() {
    // Find a unique sport ID that's not already selected
    int? sportId;
    String sportName = '';
    double fees = 0;
    int numberOfSessions = 0;
    String trainerName = '';

    if (widget.availableSports.isNotEmpty) {
      // Try to find a sport that's not already selected
      final availableSportIds = widget.availableSports.map((s) => s.id).toSet();
      final selectedSportIds = _sports.map((s) => s.sportId).toSet();

      // Find sports that aren't already selected
      final availableIds = availableSportIds.difference(selectedSportIds);

      if (availableIds.isNotEmpty) {
        // Use the first available unselected sport
        sportId = availableIds.first;
        final sport = widget.availableSports.firstWhere((s) => s.id == sportId);
        sportName = sport.name;
        fees = sport.fees;
        numberOfSessions = sport.number_of_sessions;
        trainerName =
            sport.trainers.isNotEmpty
                ? sport.trainers.split(',').first.trim()
                : '';
      } else {
        // If all sports are already selected, use the first one but with a unique identifier
        final firstSport = widget.availableSports.first;
        sportId = firstSport.id;
        sportName = firstSport.name;
        fees = firstSport.fees;
        numberOfSessions = firstSport.number_of_sessions;
        trainerName =
            firstSport.trainers.isNotEmpty
                ? firstSport.trainers.split(',').first.trim()
                : '';
      }
    }

    setState(() {
      _sports.add(
        CustomerSport(
          id: 0,
          sportId: sportId ?? 0,
          sportName: sportName,
          sportLevel: 'Beginner', // Default sport level
          trainer: '',
          trainerId:
              widget.trainers.isNotEmpty
                  ? widget.trainers.first['id'] as int
                  : 1,
          trainerName: trainerName,
          subscriptionType:
              widget.subscriptionTypes.isNotEmpty
                  ? widget.subscriptionTypes.first
                  : '',
          subscriptionDuration:
              widget.subscriptionDurations.isNotEmpty &&
                      widget
                          .subscriptionDurations[widget
                              .subscriptionTypes
                              .first]!
                          .isNotEmpty
                  ? widget
                      .subscriptionDurations[widget.subscriptionTypes.first]!
                      .first
                  : '',
          numberOfSessions: numberOfSessions,
          trainingTimes: [],
          trainingTime: '',
          trainingDays: '',
          fees: fees,
          additionalFees: 0,
          taxAmount: fees * 0.15, // 15% tax
          totalAmount: fees + (fees * 0.15), // Total with tax
          uniformIncluded: false,
          uniformPrice: 0,
          startDate:
              DateTime.now().toString().split(
                ' ',
              )[0], // Today's date as default start date
          endDate: '', // Will be calculated based on subscription duration
        ),
      );
    });

    // Notify parent
    widget.onUpdate(_sports);
  }

  void _removeSport(int index) {
    if (_sports.length > 1) {
      setState(() {
        _sports.removeAt(index);
      });

      // Notify parent
      widget.onUpdate(_sports);
    }
  }

  void _updateSport(int index, CustomerSport updatedSport) {
    setState(() {
      _sports[index] = updatedSport;
    });

    // Notify parent
    widget.onUpdate(_sports);
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context)!.sportsActivities,
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 16),

          // Sports list
          ..._sports.asMap().entries.map((entry) {
            final index = entry.key;
            final sport = entry.value;

            return _buildSportCard(context, index, sport);
          }).toList(),

          // Add sport button
          const SizedBox(height: 16),
          Center(
            child: ElevatedButton.icon(
              onPressed: _addNewSport,
              icon: const Icon(Icons.add),
              label: Text(AppLocalizations.of(context)!.addSport),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSportCard(BuildContext context, int index, CustomerSport sport) {
    // Find the selected sport
    final selectedSport = widget.availableSports.firstWhere(
      (s) => s.id == sport.sportId,
      orElse:
          () =>
              widget.availableSports.isNotEmpty
                  ? widget.availableSports.first
                  : Sport(
                    id: 0,
                    name: '',
                    trainers: '',
                    fees: 0,
                    number_of_sessions: 0,
                    training_time: '',
                    training_days: '',
                  ),
    );

    // Use trainers from API instead of parsing from sport's trainers field
    final trainers = widget.trainers;

    // Get training times for the selected sport
    final trainingTimes =
        selectedSport.training_time
            .split(',')
            .map((time) => time.trim())
            .toList();

    // Get subscription durations for the selected subscription type
    final subscriptionDurations =
        widget.subscriptionDurations[sport.subscriptionType] ?? [];

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Card header with remove button
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${AppLocalizations.of(context)!.sports} ${index + 1}',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                if (_sports.length > 1)
                  IconButton(
                    icon: const Icon(Icons.delete, color: Colors.red),
                    onPressed: () => _removeSport(index),
                  ),
              ],
            ),
            const Divider(),

            // Sport selection
            DropdownField<int>(
              label: AppLocalizations.of(context)!.sportName,
              value: sport.sportId,
              items:
                  widget.availableSports
                      .map(
                        (s) => DropdownMenuItem<int>(
                          value: s.id,
                          child: Text(s.name),
                        ),
                      )
                      .toList(),
              onChanged: (value) {
                if (value != null) {
                  final selectedSport = widget.availableSports.firstWhere(
                    (s) => s.id == value,
                  );
                  final newFees = selectedSport.fees;
                  final taxAmount = newFees * 0.15;
                  final totalAmount =
                      newFees + taxAmount + (sport.additionalFees ?? 0);

                  _updateSport(
                    index,
                    sport.copyWith(
                      sportId: value,
                      sportName: selectedSport.name,
                      fees: newFees,
                      taxAmount: taxAmount,
                      totalAmount: totalAmount,
                      numberOfSessions: selectedSport.number_of_sessions,
                    ),
                  );
                }
              },
            ),
            const SizedBox(height: 16),

            // Trainer selection
            DropdownField<int>(
              label: AppLocalizations.of(context)!.trainers,
              value: sport.trainerId,
              items:
                  trainers.isEmpty
                      ? [
                        DropdownMenuItem<int>(
                          value: 0,
                          child: Text('No trainers available'),
                        ),
                      ]
                      : trainers
                          .map(
                            (trainer) => DropdownMenuItem<int>(
                              value: trainer['id'] as int,
                              child: Text(trainer['name'] as String),
                            ),
                          )
                          .toList(),
              onChanged: (value) {
                if (value != null) {
                  final selectedTrainer = trainers.firstWhere(
                    (t) => t['id'] == value,
                    orElse: () => {'id': 0, 'name': 'Unknown'},
                  );
                  _updateSport(
                    index,
                    sport.copyWith(
                      trainerId: value,
                      trainerName: selectedTrainer['name'] as String,
                    ),
                  );
                }
              },
            ),
            const SizedBox(height: 16),

            // Subscription type
            DropdownField<String>(
              label: AppLocalizations.of(context)!.subscriptionType,
              value: sport.subscriptionType,
              items:
                  widget.subscriptionTypes
                      .map(
                        (type) => DropdownMenuItem<String>(
                          value: type,
                          child: Text(type),
                        ),
                      )
                      .toList(),
              onChanged: (value) {
                if (value != null) {
                  final durations = widget.subscriptionDurations[value] ?? [];
                  final newDuration =
                      durations.isNotEmpty ? durations.first : '';

                  // Calculate end date based on new subscription duration
                  String endDate = '';
                  if (sport.startDate != null &&
                      sport.startDate!.isNotEmpty &&
                      newDuration.isNotEmpty) {
                    final startDate = DateTime.parse(sport.startDate!);
                    final durationParts = newDuration.split(' ');
                    if (durationParts.length >= 2) {
                      final durationValue = int.tryParse(durationParts[0]) ?? 0;
                      final durationUnit = durationParts[1].toLowerCase();

                      if (durationUnit.contains('month')) {
                        final endDateTime = DateTime(
                          startDate.year,
                          startDate.month + durationValue,
                          startDate.day,
                        );
                        endDate = endDateTime.toString().split(' ')[0];
                      } else if (durationUnit.contains('year')) {
                        final endDateTime = DateTime(
                          startDate.year + durationValue,
                          startDate.month,
                          startDate.day,
                        );
                        endDate = endDateTime.toString().split(' ')[0];
                      }
                    }
                  }

                  _updateSport(
                    index,
                    sport.copyWith(
                      subscriptionType: value,
                      subscriptionDuration: newDuration,
                      endDate: endDate,
                    ),
                  );
                }
              },
            ),
            const SizedBox(height: 16),

            // Subscription duration
            DropdownField<String>(
              label: AppLocalizations.of(context)!.subscriptionDuration,
              value: sport.subscriptionDuration,
              items:
                  subscriptionDurations
                      .map(
                        (duration) => DropdownMenuItem<String>(
                          value: duration,
                          child: Text(duration),
                        ),
                      )
                      .toList(),
              onChanged: (value) {
                if (value != null) {
                  // Calculate end date based on new subscription duration
                  String endDate = '';
                  if (sport.startDate != null &&
                      sport.startDate!.isNotEmpty &&
                      value != null &&
                      value.isNotEmpty) {
                    final startDate = DateTime.parse(sport.startDate!);
                    final durationParts = value.split(' ');
                    if (durationParts.length >= 2) {
                      final durationValue = int.tryParse(durationParts[0]) ?? 0;
                      final durationUnit = durationParts[1].toLowerCase();

                      if (durationUnit.contains('month')) {
                        final endDateTime = DateTime(
                          startDate.year,
                          startDate.month + durationValue,
                          startDate.day,
                        );
                        endDate = endDateTime.toString().split(' ')[0];
                      } else if (durationUnit.contains('year')) {
                        final endDateTime = DateTime(
                          startDate.year + durationValue,
                          startDate.month,
                          startDate.day,
                        );
                        endDate = endDateTime.toString().split(' ')[0];
                      }
                    }
                  }

                  _updateSport(
                    index,
                    sport.copyWith(
                      subscriptionDuration: value,
                      endDate: endDate,
                    ),
                  );
                }
              },
            ),
            const SizedBox(height: 16),

            // Number of sessions
            CustomTextField(
              label: AppLocalizations.of(context)!.numberOfSessions,
              hint: 'Enter number of sessions',
              controller: TextEditingController(
                text: sport.numberOfSessions.toString(),
              ),
              keyboardType: TextInputType.number,
              onChanged: (value) {
                _updateSport(
                  index,
                  sport.copyWith(numberOfSessions: int.tryParse(value) ?? 0),
                );
              },
            ),
            const SizedBox(height: 16),

            // Training times
            Text(
              AppLocalizations.of(context)!.trainingTimes,
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),

            // Day selection section
            Text('Select Days', style: Theme.of(context).textTheme.titleSmall),
            const SizedBox(height: 4),

            // Extract unique days from training times
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children:
                  trainingTimes
                      .map((time) => time.split(' ')[0]) // Extract day part
                      .toSet() // Get unique days
                      .map((day) {
                        final isSelected = sport.trainingTimes.any(
                          (t) => t.startsWith(day),
                        );
                        return FilterChip(
                          label: Text(day),
                          selected: isSelected,
                          onSelected: (selected) {
                            final updatedTimes = List<String>.from(
                              sport.trainingTimes,
                            );
                            final dayTimes =
                                trainingTimes
                                    .where((t) => t.startsWith(day))
                                    .toList();

                            if (selected) {
                              // Add all time slots for this day
                              for (final time in dayTimes) {
                                if (!updatedTimes.contains(time)) {
                                  updatedTimes.add(time);
                                }
                              }
                            } else {
                              // Remove all time slots for this day
                              updatedTimes.removeWhere(
                                (t) => t.startsWith(day),
                              );
                            }

                            _updateSport(
                              index,
                              sport.copyWith(trainingTimes: updatedTimes),
                            );
                          },
                        );
                      })
                      .toList(),
            ),
            const SizedBox(height: 16),

            // Time slots section - grouped by day
            if (sport.trainingTimes.isNotEmpty) ...[
              Text(
                'Selected Time Slots',
                style: Theme.of(context).textTheme.titleSmall,
              ),
              const SizedBox(height: 4),

              // Group time slots by day
              ...sport.trainingTimes.map((t) => t.split(' ')[0]).toSet().map((
                day,
              ) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(top: 8.0, bottom: 4.0),
                      child: Text(
                        day,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children:
                          sport.trainingTimes
                              .where((t) => t.startsWith(day))
                              .map((time) {
                                return FilterChip(
                                  label: Text(
                                    time.split(' ')[1],
                                  ), // Show only time part
                                  selected: true,
                                  onSelected: (selected) {
                                    if (!selected) {
                                      final updatedTimes = List<String>.from(
                                        sport.trainingTimes,
                                      );
                                      updatedTimes.remove(time);
                                      _updateSport(
                                        index,
                                        sport.copyWith(
                                          trainingTimes: updatedTimes,
                                        ),
                                      );
                                    }
                                  },
                                );
                              })
                              .toList(),
                    ),
                  ],
                );
              }).toList(),
            ],
            const SizedBox(height: 16),

            // Sport Level
            DropdownField<String>(
              label: 'Sport Level',
              value: sport.sportLevel,
              items:
                  ['Beginner', 'Intermediate', 'Advanced', 'Professional']
                      .map(
                        (level) => DropdownMenuItem<String>(
                          value: level,
                          child: Text(level),
                        ),
                      )
                      .toList(),
              onChanged: (value) {
                if (value != null) {
                  _updateSport(index, sport.copyWith(sportLevel: value));
                }
              },
            ),
            const SizedBox(height: 16),

            // Training dates in a row
            Row(
              children: [
                Expanded(
                  child: GestureDetector(
                    onTap: () async {
                      final picked = await showDatePicker(
                        context: context,
                        initialDate: DateTime.now(),
                        firstDate: DateTime.now().subtract(
                          const Duration(days: 365),
                        ),
                        lastDate: DateTime.now().add(const Duration(days: 365)),
                      );
                      if (picked != null) {
                        final startDate = picked.toString().split(' ')[0];

                        // Calculate end date based on subscription duration
                        String endDate = '';
                        if (sport.subscriptionDuration.isNotEmpty) {
                          final durationParts = sport.subscriptionDuration
                              .split(' ');
                          if (durationParts.length >= 2) {
                            final durationValue =
                                int.tryParse(durationParts[0]) ?? 0;
                            final durationUnit = durationParts[1].toLowerCase();

                            if (durationUnit.contains('month')) {
                              final endDateTime = DateTime(
                                picked.year,
                                picked.month + durationValue,
                                picked.day,
                              );
                              endDate = endDateTime.toString().split(' ')[0];
                            } else if (durationUnit.contains('year')) {
                              final endDateTime = DateTime(
                                picked.year + durationValue,
                                picked.month,
                                picked.day,
                              );
                              endDate = endDateTime.toString().split(' ')[0];
                            }
                          }
                        }

                        _updateSport(
                          index,
                          sport.copyWith(
                            startDate: startDate,
                            endDate: endDate,
                          ),
                        );
                      }
                    },
                    child: AbsorbPointer(
                      child: CustomTextField(
                        label: 'Training Start Date',
                        hint: 'Select start date',
                        controller: TextEditingController(
                          text: sport.startDate ?? '',
                        ),
                        suffix: const Icon(Icons.calendar_today),
                        readOnly: true,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: CustomTextField(
                    label: 'Training End Date',
                    hint:
                        sport.endDate?.isNotEmpty == true
                            ? 'Subscription will end on ${sport.endDate}'
                            : 'End date will be calculated based on subscription period',
                    controller: TextEditingController(
                      text: sport.endDate ?? '',
                    ),
                    readOnly: true,
                    suffix: const Icon(Icons.calendar_today),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Fees section in a row
            Row(
              children: [
                Expanded(
                  child: CustomTextField(
                    label: AppLocalizations.of(context)!.fees,
                    hint: 'Enter fees amount',
                    controller: TextEditingController(
                      text: sport.fees.toString(),
                    ),
                    keyboardType: TextInputType.number,
                    prefix: const Icon(Icons.attach_money),
                    onChanged: (value) {
                      final newFees = double.tryParse(value) ?? 0;
                      final additionalFees = sport.additionalFees ?? 0;
                      final taxAmount = (newFees + additionalFees) * 0.15;
                      final totalAmount = newFees + additionalFees + taxAmount;

                      _updateSport(
                        index,
                        sport.copyWith(
                          fees: newFees,
                          taxAmount: taxAmount,
                          totalAmount: totalAmount,
                        ),
                      );
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: CustomTextField(
                    label: 'Additional Fees',
                    hint: 'Enter additional fees',
                    controller: TextEditingController(
                      text: (sport.additionalFees ?? 0).toString(),
                    ),
                    keyboardType: TextInputType.number,
                    prefix: const Icon(Icons.attach_money),
                    onChanged: (value) {
                      final newAdditionalFees = double.tryParse(value) ?? 0;
                      final baseFees = sport.fees;
                      final taxAmount = (baseFees + newAdditionalFees) * 0.15;
                      final totalAmount =
                          baseFees + newAdditionalFees + taxAmount;

                      _updateSport(
                        index,
                        sport.copyWith(
                          additionalFees: newAdditionalFees,
                          taxAmount: taxAmount,
                          totalAmount: totalAmount,
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Tax Amount (15%) - Read-only
            CustomTextField(
              label: 'Tax Amount (15%)',
              hint: 'Tax amount',
              controller: TextEditingController(
                text: (sport.taxAmount ?? 0).toStringAsFixed(2),
              ),
              keyboardType: TextInputType.number,
              prefix: const Icon(Icons.attach_money),
              readOnly: true,
            ),
            const SizedBox(height: 16),

            // Total Amount - Read-only
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primaryContainer,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Total Amount:',
                    style: Theme.of(context).textTheme.titleMedium!.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    '${(sport.totalAmount ?? 0).toStringAsFixed(2)} SAR',
                    style: Theme.of(context).textTheme.titleMedium!.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // Uniform options
            Row(
              children: [
                Checkbox(
                  value: sport.uniformIncluded,
                  onChanged: (value) {
                    final isIncluded = value ?? false;
                    final uniformPrice = sport.uniformPrice ?? 0;
                    final uniformTax = isIncluded ? uniformPrice * 0.15 : 0;
                    final baseFees = sport.fees;
                    final additionalFees = sport.additionalFees ?? 0;
                    final sportTax = (baseFees + additionalFees) * 0.15;
                    final totalAmount =
                        baseFees +
                        additionalFees +
                        sportTax +
                        (isIncluded ? uniformPrice + uniformTax : 0);

                    _updateSport(
                      index,
                      sport.copyWith(
                        uniformIncluded: isIncluded,
                        totalAmount: totalAmount,
                      ),
                    );
                  },
                ),
                Text(AppLocalizations.of(context)!.includeUniform),
              ],
            ),

            if (sport.uniformIncluded ?? false)
              Column(
                children: [
                  CustomTextField(
                    label: AppLocalizations.of(context)!.uniformPrice,
                    hint: 'Enter uniform price',
                    controller: TextEditingController(
                      text: (sport.uniformPrice ?? 0).toString(),
                    ),
                    keyboardType: TextInputType.number,
                    prefix: const Icon(Icons.attach_money),
                    onChanged: (value) {
                      final newUniformPrice = double.tryParse(value) ?? 0;
                      final uniformTax = newUniformPrice * 0.15;
                      final baseFees = sport.fees;
                      final additionalFees = sport.additionalFees ?? 0.0;
                      final sportTax = (baseFees + additionalFees) * 0.15;
                      final totalAmount =
                          baseFees +
                          additionalFees +
                          sportTax +
                          newUniformPrice +
                          uniformTax;

                      _updateSport(
                        index,
                        sport.copyWith(
                          uniformPrice: newUniformPrice,
                          totalAmount: totalAmount,
                        ),
                      );
                    },
                  ),
                  const SizedBox(height: 16),
                  CustomTextField(
                    label: 'Uniform Tax (15%)',
                    hint: 'Tax amount',
                    controller: TextEditingController(
                      text: ((sport.uniformPrice ?? 0) * 0.15).toStringAsFixed(
                        2,
                      ),
                    ),
                    keyboardType: TextInputType.number,
                    prefix: const Icon(Icons.attach_money),
                    readOnly: true,
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }
}
