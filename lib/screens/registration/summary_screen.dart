// ignore_for_file: deprecated_member_use, unnecessary_string_interpolations

import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart'; // Import AppLocalizations
import 'package:hemmaerp/models/customers.dart';

class RegistrationSummaryScreen extends StatelessWidget {
  final Customer customer;
  final List<Customer>? familyMembers;
  final Function(Customer) onUpdate;

  const RegistrationSummaryScreen({
    Key? key,
    required this.customer,
    this.familyMembers,
    required this.onUpdate,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context)!.summary,
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 24),

          // Account Type
          _buildSection(
            context,
            AppLocalizations.of(context)!.accountType,
            customer.accountType,
          ),

          if (customer.accountType == 'Family') ...[
            const SizedBox(height: 16),
            _buildSection(
              context,
              AppLocalizations.of(context)!.numberOfChildren,
              customer.numberOfChildren.toString(),
            ),
          ],

          const SizedBox(height: 24),

          // Personal Information
          Text(
            AppLocalizations.of(context)!.personalInformation,
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 16),
          _buildSection(
            context,
            AppLocalizations.of(context)!.name,
            customer.name,
          ),
          _buildSection(
            context,
            AppLocalizations.of(context)!.email,
            customer.email,
          ),
          _buildSection(
            context,
            AppLocalizations.of(context)!.phone,
            customer.phone,
          ),
          _buildSection(
            context,
            AppLocalizations.of(context)!.address,
            customer.address,
          ),
          _buildSection(
            context,
            AppLocalizations.of(context)!.nationalId,
            customer.nationalId,
          ),
          _buildSection(
            context,
            AppLocalizations.of(context)!.birthdate,
            customer.birthdate,
          ),
          _buildSection(
            context,
            AppLocalizations.of(context)!.gender,
            customer.gender,
          ),
          _buildSection(
            context,
            AppLocalizations.of(context)!.nationality,
            customer.nationality,
          ),
          if (customer.parentPhone != null)
            _buildSection(
              context,
              AppLocalizations.of(context)!.parentPhone,
              customer.parentPhone!,
            ),
          _buildSection(
            context,
            AppLocalizations.of(context)!.fullAddress,
            customer.fullAddress,
          ),

          const SizedBox(height: 24),

          // Health Information
          Text(
            AppLocalizations.of(context)!.healthInformation,
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 16),
          _buildSection(
            context,
            AppLocalizations.of(context)!.healthConditions,
            customer.healthConditions,
          ),

          const SizedBox(height: 24),

          // Sports Information
          Text(
            AppLocalizations.of(context)!.sportsInformation,
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 16),
          _buildSection(
            context,
            AppLocalizations.of(context)!.sportActivity,
            customer.sportActivity,
          ),
          _buildSection(
            context,
            AppLocalizations.of(context)!.sportLevel,
            customer.sportLevel,
          ),
          _buildSection(
            context,
            AppLocalizations.of(context)!.trainer,
            customer.trainer,
          ),
          _buildSection(
            context,
            AppLocalizations.of(context)!.subscriptionType,
            customer.subscriptionType,
          ),
          _buildSection(
            context,
            AppLocalizations.of(context)!.subscriptionDuration,
            customer.subscriptionDuration,
          ),
          _buildSection(
            context,
            AppLocalizations.of(context)!.numberOfSessions,
            customer.numberOfSessions.toString(),
          ),
          _buildSection(
            context,
            AppLocalizations.of(context)!.trainingTimes,
            customer.trainingTimes,
          ),

          const SizedBox(height: 24),

          // Payment Information
          Text(
            AppLocalizations.of(context)!.paymentInformation,
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 16),
          _buildSection(
            context,
            AppLocalizations.of(context)!.paymentMethod,
            customer.paymentMethod,
          ),
          if (customer.paymentNote != null)
            _buildSection(
              context,
              AppLocalizations.of(context)!.paymentNote,
              customer.paymentNote!,
            ),
          _buildSection(
            context,
            AppLocalizations.of(context)!.totalFees,
            customer.totalFees.toString(),
          ),

          const SizedBox(height: 24),

          // Additional Information
          Text(
            AppLocalizations.of(context)!.additionalInformation,
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 16),
          _buildSection(
            context,
            AppLocalizations.of(context)!.howDidHearAboutUs,
            customer.howDidHearAboutUs,
          ),

          if (familyMembers != null && familyMembers!.isNotEmpty) ...[
            const SizedBox(height: 24),

            // Family Members
            Text(
              AppLocalizations.of(context)!.familyMembers,
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),

            ...familyMembers!.asMap().entries.map((entry) {
              final index = entry.key;
              final member = entry.value;
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '${AppLocalizations.of(context)!.familyMember} ${index + 1}',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  _buildSection(
                    context,
                    AppLocalizations.of(context)!.name,
                    member.name,
                  ),
                  _buildSection(
                    context,
                    AppLocalizations.of(context)!.birthdate,
                    member.birthdate,
                  ),
                  _buildSection(
                    context,
                    AppLocalizations.of(context)!.gender,
                    member.gender,
                  ),
                  _buildSection(
                    context,
                    AppLocalizations.of(context)!.nationality,
                    member.nationality,
                  ),
                  _buildSection(
                    context,
                    AppLocalizations.of(context)!.healthConditions,
                    member.healthConditions,
                  ),
                  _buildSection(
                    context,
                    AppLocalizations.of(context)!.sportActivity,
                    member.sportActivity,
                  ),
                  _buildSection(
                    context,
                    AppLocalizations.of(context)!.sportLevel,
                    member.sportLevel,
                  ),
                  _buildSection(
                    context,
                    AppLocalizations.of(context)!.trainer,
                    member.trainer,
                  ),
                  _buildSection(
                    context,
                    AppLocalizations.of(context)!.subscriptionType,
                    member.subscriptionType,
                  ),
                  _buildSection(
                    context,
                    AppLocalizations.of(context)!.subscriptionDuration,
                    member.subscriptionDuration,
                  ),
                  _buildSection(
                    context,
                    AppLocalizations.of(context)!.numberOfSessions,
                    member.numberOfSessions.toString(),
                  ),
                  _buildSection(
                    context,
                    AppLocalizations.of(context)!.trainingTimes,
                    member.trainingTimes,
                  ),
                  if (member.paymentNote != null)
                    _buildSection(
                      context,
                      AppLocalizations.of(context)!.paymentNote,
                      member.paymentNote!,
                    ),
                  _buildSection(
                    context,
                    AppLocalizations.of(context)!.totalFees,
                    member.totalFees.toString(),
                  ),
                  const SizedBox(height: 16),
                ],
              );
            }),
          ],
        ],
      ),
    );
  }

  Widget _buildSection(BuildContext context, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(flex: 3, child: Text(value)),
        ],
      ),
    );
  }
}
