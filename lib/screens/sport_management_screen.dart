// ignore_for_file: library_private_types_in_public_api, use_build_context_synchronously, unnecessary_to_list_in_spreads, deprecated_member_use, prefer_final_fields, unused_element, unnecessary_brace_in_string_interps, avoid_print
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:hemmaerp/models/sport.dart';
import 'package:hemmaerp/services/sport_service.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class SportManagementScreen extends StatefulWidget {
  final Sport? sport;
  const SportManagementScreen({Key? key, this.sport}) : super(key: key);

  @override
  _SportManagementScreenState createState() => _SportManagementScreenState();
}

class _SportManagementScreenState extends State<SportManagementScreen> {
  final _formKey = GlobalKey<FormState>();
  final SportService _sportService = SportService(
    baseUrl: 'https://backend2.hemmasportacademy.com'
  );
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _feesController = TextEditingController();
  final TextEditingController _sessionsController = TextEditingController();
  final TextEditingController _trainersController = TextEditingController();
  bool _isLoading = false;
  bool _isEditing = false;
  final List<String> _daysOfWeek = [
    'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'
  ];
  Map<String, Map<String, dynamic>> _businessHours = {};
  List<Map<String, dynamic>> _trainers = [];

  @override
  void initState() {
    super.initState();
    _initializeData();
    _initializeBusinessHours(); // Uncomment this line
    _loadTrainers();
  }

  void _initializeBusinessHours() {
    for (String day in _daysOfWeek) {
      _businessHours[day] = {
        'status': 'closed',
        'timeSlots': [],
        'selectedTrainerId': null,
      };
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _feesController.dispose();
    _sessionsController.dispose();
    _trainersController.dispose();
    super.dispose();
  }

  Future<void> _loadTrainers() async {
    try {
      final response = await http.get(
        Uri.parse('${_sportService.baseUrl}/crud.php?table=trainers&action=select')
      );
      if (response.statusCode == 200) {
        setState(() {
          _trainers = List<Map<String, dynamic>>.from(json.decode(response.body));
        });
      }
    } catch (e) {
      _showErrorSnackBar('Failed to load trainers: $e');
    }
  }

  void _initializeData() {
    if (widget.sport != null) {
      setState(() {
        _isEditing = true;
        _nameController.text = widget.sport!.name;
        _feesController.text = widget.sport!.fees.toString();
        _sessionsController.text = widget.sport!.number_of_sessions.toString();
        _trainersController.text = widget.sport!.trainers;
      });
      _loadBusinessHours();
    }
  }

  Future<void> _saveSport() async {
    if (!_formKey.currentState!.validate()) return;
    
    setState(() => _isLoading = true);
    try {
      final sportData = {
        'name': _nameController.text,
        'fees': double.parse(_feesController.text),
        'number_of_sessions': int.parse(_sessionsController.text),
        'trainers': _getSelectedTrainers(),
        'training_time': _formatBusinessHours(),
        'training_days': _formatBusinessHours(),
      };

      final response = await http.post(
        Uri.parse(_isEditing 
          ? 'https://backend2.hemmasportacademy.com/sports/update_sport.php'
          : 'https://backend2.hemmasportacademy.com/sports/insert_sport.php'
        ),
        headers: {'Content-Type': 'application/json'},
        body: json.encode(_isEditing ? {...sportData, 'id': widget.sport!.id} : sportData),
      );

      final responseData = json.decode(response.body);
      if (response.statusCode != 200 || (responseData is Map && responseData.containsKey('error'))) {
        throw Exception(responseData['error'] ?? 'Failed to save sport');
      }

      _showSuccessMessage();
      Navigator.pop(context, true);
    } catch (e) {
      _showErrorSnackBar('Error saving sport: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showDeleteConfirmation(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n.deleteSport),
        content: Text(l10n.deleteConfirmation as String),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(l10n.cancel),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteSport();
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: Text(l10n.delete),
          ),
        ],
      ),
    );
  }

  Widget _buildHelpButton(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    return IconButton(
      icon: const Icon(Icons.help_outline),
      tooltip: l10n.businessHoursHelp,
      onPressed: () {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(l10n.businessHoursHelp),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(l10n.businessHoursHelpText
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(l10n.close),
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _deleteSport() async {
    try {
      if (!_isEditing) return;
      
      final response = await http.post(
        Uri.parse('https://backend2.hemmasportacademy.com/sports/delete_sport.php'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({'id': widget.sport!.id}),
      );

      final responseData = json.decode(response.body);
      if (response.statusCode != 200 || (responseData is Map && responseData.containsKey('error'))) {
        throw Exception(responseData['error'] ?? 'Failed to delete sport');
      }

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Sport deleted successfully')),
      );
      Navigator.pop(context, true);
    } catch (e) {
      _showErrorSnackBar('Error deleting sport: $e');
    }
  }

  Future<void> _loadBusinessHours() async {
    try {
      final response = await http.get(
        Uri.parse('${_sportService.baseUrl}/crud.php?table=business_hours&action=select&sport_id=${widget.sport!.id}')
      );
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        setState(() {
          _businessHours = Map<String, Map<String, dynamic>>.from(data);
        });
      }
    } catch (e) {
      _showErrorSnackBar('Error loading business hours: $e');
    }
  }

  String _getSelectedTrainers() {
    final Set<String> selectedTrainers = {};
    for (var dayData in _businessHours.values) {
      if (dayData['status'] == 'open') {
        dayData['timeSlots'].forEach((slot) {
          if (slot['trainerName'] != null) {
            selectedTrainers.add(slot['trainerName']);
          }
        });
      }
    }
    return selectedTrainers.join(', ');
  }

  String _formatTimeOfDay(TimeOfDay time) {
    final hour = time.hour.toString().padLeft(2, '0');
    final minute = time.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  Future<void> _addTimeSlot(String day) async {
    final TimeOfDay? startTime = await showTimePicker(
      context: context,
      initialTime: const TimeOfDay(hour: 9, minute: 0),
    );
    if (startTime == null) return;
    final TimeOfDay? endTime = await showTimePicker(
      context: context,
      initialTime: TimeOfDay(hour: startTime.hour + 1, minute: startTime.minute),
    );
    if (endTime == null) return;
    if (_trainers.isEmpty) {
      _showErrorSnackBar('No trainers available');
      return;
    }
    final trainer = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Trainer'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: _trainers.length,
            itemBuilder: (context, index) => ListTile(
              title: Text(_trainers[index]['name']),
              onTap: () => Navigator.pop(context, _trainers[index]),
            ),
          ),
        ),
      ),
    );
    if (trainer == null) return;
    setState(() {
      _businessHours[day]!['status'] = 'open';
      _businessHours[day]!['timeSlots'].add({
        'startTime': _formatTimeOfDay(startTime),
        'endTime': _formatTimeOfDay(endTime),
        'trainerId': trainer['id'],
        'trainerName': trainer['name'],
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? l10n.editSport : l10n.addSport),
        elevation: 2,
        actions: _isEditing ? [
          IconButton(
            icon: const Icon(Icons.delete),
            tooltip: l10n.deleteSport,
            onPressed: () => _showDeleteConfirmation(context),
          ),
        ] : null,
      ),
      body: _isLoading
        ? const Center(child: CircularProgressIndicator())
        : Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  theme.scaffoldBackgroundColor,
                  theme.colorScheme.surfaceVariant.withOpacity(0.2),
                ],
              ),
            ),
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Basic Information Card
                    Card(
                      elevation: 2,
                      margin: const EdgeInsets.only(bottom: 16),
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(Icons.sports, color: theme.primaryColor),
                                const SizedBox(width: 8),
                                Text(
                                  l10n.basicInformation,
                                  style: theme.textTheme.titleLarge,
                                ),
                              ],
                            ),
                            const Divider(height: 24),
                            TextFormField(
                              controller: _nameController,
                              decoration: InputDecoration(
                                labelText: l10n.sportName,
                                hintText: l10n.enterSportName,
                                prefixIcon: const Icon(Icons.sports_soccer),
                                border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                              ),
                              validator: (value) => value?.isEmpty ?? true ? l10n.requiredField : null,
                            ),
                            const SizedBox(height: 16),
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Expanded(
                                  child: TextFormField(
                                    controller: _feesController,
                                    decoration: InputDecoration(
                                      labelText: l10n.fees,
                                      hintText: l10n.enterFees,
                                      prefixIcon: const Icon(Icons.attach_money),
                                      border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                                    ),
                                    keyboardType: TextInputType.number,
                                    validator: (value) => value?.isEmpty ?? true ? l10n.requiredField : null,
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: TextFormField(
                                    controller: _sessionsController,
                                    decoration: InputDecoration(
                                      labelText: l10n.numberOfSessions,
                                      hintText: l10n.enterNumberOfSessions,
                                      prefixIcon: const Icon(Icons.calendar_today),
                                      border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                                    ),
                                    keyboardType: TextInputType.number,
                                    validator: (value) => value?.isEmpty ?? true ? l10n.requiredField : null,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                    
                    // Business Hours Card
                    Card(
                      elevation: 2,
                      margin: const EdgeInsets.only(bottom: 16),
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(Icons.access_time, color: theme.primaryColor),
                                const SizedBox(width: 8),
                                Text(
                                  l10n.businessHours,
                                  style: theme.textTheme.titleLarge,
                                ),
                                const Spacer(),
                                _buildHelpButton(context),
                              ],
                            ),
                            const Divider(height: 24),
                            ..._daysOfWeek.map((day) => _buildDaySchedule(day)),
                          ],
                        ),
                      ),
                    ),
                    
                    // Save Button
                    Center(
                      child: SizedBox(
                        width: double.infinity,
                        child: ElevatedButton.icon(
                          onPressed: _saveSport,
                          icon: const Icon(Icons.save),
                          label: Text(
                            _isEditing ? (l10n.updateSport) : (l10n.createSport),
                            style: const TextStyle(fontSize: 16),
                          ),
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
      floatingActionButton: null, // Removed floating action button as we have a proper button in the form
    );
  }

  Widget _buildDaySchedule(String day) {
    final dayData = _businessHours[day]!;
    final isActive = dayData['status'] == 'open';
    final theme = Theme.of(context);
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: theme.dividerColor.withOpacity(0.3)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Day header with toggle
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: isActive ? theme.colorScheme.primaryContainer.withOpacity(0.3) : null,
              borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
            ),
            child: Row(
              children: [
                Checkbox(
                  value: isActive,
                  onChanged: (value) {
                    setState(() {
                      _businessHours[day]!['status'] = value! ? 'open' : 'closed';
                      if (!value) {
                        _businessHours[day]!['timeSlots'].clear();
                        _businessHours[day]!['selectedTrainerId'] = null;
                      }
                    });
                  },
                ),
                Text(day, style: Theme.of(context).textTheme.titleMedium),
                const Spacer(),
                if (isActive)
                  TextButton.icon(
                    icon: const Icon(Icons.add, size: 18),
                    label: const Text('Add Time'),
                    onPressed: () => _addTimeSlot(day),
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                    ),
                  ),
              ],
            ),
          ),
          // Time slots
          if (isActive) ...[              
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (_trainers.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(bottom: 12.0),
                      child: DropdownButtonFormField<int>(
                        value: dayData['selectedTrainerId'] != null 
                               ? int.parse(dayData['selectedTrainerId'].toString()) 
                               : null,
                        decoration: InputDecoration(
                          labelText: 'Default Trainer for ${day}',
                          hintText: 'Select a trainer',
                          prefixIcon: Icon(Icons.person, color: theme.colorScheme.primary),
                          border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                          filled: true,
                          fillColor: theme.colorScheme.surface,
                        ),
                        icon: Icon(Icons.arrow_drop_down, color: theme.colorScheme.primary),
                        isExpanded: true,
                        items: _trainers
                            .map((trainer) => DropdownMenuItem<int>(
                                  value: int.parse(trainer['id'].toString()),
                                  child: Row(
                                    children: [
                                      Icon(Icons.sports_kabaddi, size: 18, color: theme.colorScheme.secondary),
                                      const SizedBox(width: 8),
                                      Text(
                                        trainer['name'].toString(),
                                        style: const TextStyle(fontWeight: FontWeight.w500),
                                      ),
                                    ],
                                  ),
                                ))
                            .toList(),
                        onChanged: (value) {
                          setState(() {
                            dayData['selectedTrainerId'] = value;
                          });
                        },
                      ),
                    )
                  else
                    Container(
                      padding: const EdgeInsets.all(12),
                      margin: const EdgeInsets.only(bottom: 12),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.error.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: theme.colorScheme.error.withOpacity(0.3)),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.warning_amber_rounded, color: theme.colorScheme.error),
                          const SizedBox(width: 8),
                          const Text(
                            'No trainers available',
                            style: TextStyle(color: Colors.red, fontWeight: FontWeight.w500),
                          ),
                        ],
                      ),
                    ),
                  const SizedBox(height: 8),
                  if (dayData['timeSlots'].isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: Text(
                        'Time Slots',
                        style: TextStyle(
                          fontSize: 14,
                          color: theme.colorScheme.onSurface.withOpacity(0.7),
                        ),
                      ),
                    ),
                  ...dayData['timeSlots'].map<Widget>((slot) => Container(
                    margin: const EdgeInsets.only(bottom: 8),
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.surface,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: theme.dividerColor.withOpacity(0.2)),
                    ),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.primary.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.access_time, size: 16, color: theme.colorScheme.primary),
                              const SizedBox(width: 4),
                              Text(
                                '${slot['startTime']} - ${slot['endTime']}',
                                style: TextStyle(fontWeight: FontWeight.bold, color: theme.colorScheme.primary),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Row(
                            children: [
                              Icon(Icons.person, size: 16, color: theme.colorScheme.secondary),
                              const SizedBox(width: 4),
                              Text(
                                '${slot['trainerName']}',
                                style: TextStyle(fontWeight: FontWeight.w500),
                              ),
                            ],
                          ),
                        ),
                        IconButton(
                          icon: Icon(Icons.delete, size: 20, color: theme.colorScheme.error),
                          tooltip: 'Remove time slot',
                          onPressed: () {
                            setState(() {
                              dayData['timeSlots'].remove(slot);
                            });
                          },
                        ),
                      ],
                    ),
                  )).toList(),
                ],
              ),
            ),
          ] else
            const Padding(
              padding: EdgeInsets.only(left: 16, top: 8, bottom: 16),
              child: Text('Closed', style: TextStyle(color: Colors.grey)),
            ),
        ],
      ),
    );
  }

  String _formatBusinessHours() {
    List<String> formattedHours = [];
    _businessHours.forEach((day, data) {
      if (data['status'] == 'open' && data['timeSlots'].isNotEmpty) {
        for (var slot in data['timeSlots']) {
          formattedHours.add('$day ${slot['startTime']}-${slot['endTime']}');
        }
      }
    });
    return formattedHours.join(',');
  }

  Future<void> _saveBusinessHours(int sportId) async {
    try {
      Map<String, dynamic> scheduleData = {};
      for (var entry in _businessHours.entries) {
        final day = entry.key;
        final dayData = entry.value;
        scheduleData[day] = {
          'status': dayData['status'],
          'timeSlots': (dayData['timeSlots'] as List).map((slot) => {
            'startTime': slot['startTime'],
            'endTime': slot['endTime'],
            'trainerId': slot['trainerId'],
          }).toList()
        };
      }

      final response = await http.post(
        Uri.parse('${_sportService.baseUrl}/business_hours.php?sport_id=$sportId'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode(scheduleData),
      );

      if (response.statusCode != 200) {
        throw Exception('Server returned ${response.statusCode}: ${response.body}');
      }

      final responseData = json.decode(response.body);
      if (!responseData['success']) {
        throw Exception(responseData['error'] ?? 'Unknown error occurred');
      }
    } catch (e) {
      print('Error saving business hours: $e');
      rethrow;
    }
  }

  void _showSuccessMessage() {
    final l10n = AppLocalizations.of(context)!;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(l10n.sportSavedSuccessfully)),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }
}

