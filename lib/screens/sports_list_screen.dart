// ignore_for_file: use_build_context_synchronously, library_private_types_in_public_api, unused_element, deprecated_member_use, sort_child_properties_last

import 'dart:io';
import 'package:csv/csv.dart';
import 'package:flutter/material.dart';
import 'package:hemmaerp/models/sport.dart';
import 'package:hemmaerp/services/sport_service.dart';
import 'package:hemmaerp/screens/sport_management_screen.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
// ignore: depend_on_referenced_packages
import 'package:file_saver/file_saver.dart'; 

class SportsListScreen extends StatefulWidget {
  const SportsListScreen({Key? key}) : super(key: key);

  @override
  _SportsListScreenState createState() => _SportsListScreenState();
}

class _SportsListScreenState extends State<SportsListScreen> {
  List<Sport> _sports = [];
  final SportService _sportService = SportService(
    baseUrl: 'https://backend2.hemmasportacademy.com'
  );
  bool _isLoading = false;
  int _rowsPerPage = PaginatedDataTable.defaultRowsPerPage;
  int? _sortColumnIndex;
  bool _sortAscending = true;

  @override
  void initState() {
    super.initState();
    _refreshSports();
  }

  Future<void> _refreshSports() async {
    try {
      setState(() => _isLoading = true);
      final sportsList = await _sportService.getSports();
      setState(() {
        _sports = sportsList;
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to refresh sports: $e')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _deleteSport(Sport sport) async {
    try {
      setState(() => _isLoading = true);
      await _sportService.deleteSport(sport.id!);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Sport deleted successfully')),
      );
      _refreshSports();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to delete sport: $e')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _refundSport(Sport sport) async {
    try {
      setState(() => _isLoading = true);
      await _sportService.refundSport(sport.id!);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Sport refunded successfully')),
      );
      _refreshSports();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to refund sport: $e')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _navigateToSportManagement([Sport? sport]) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => SportManagementScreen(sport: sport),
      ),
    );

    if (result == true) {
      _refreshSports();
    }
  }

  Future<void> _exportToCsv() async {
    if (_sports.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No data to export')),
      );
      return;
    }

    List<List<dynamic>> rows = [];
    // Add header row
    rows.add([
      'ID',
      'Name',
      'Fees',
      'Sessions/Month',
      'Training Days',
      'Training Times',
      'Trainers'
    ]);

    // Add data rows
    for (var sport in _sports) {
      rows.add([
        sport.id,
        sport.name,
        sport.fees,
        sport.number_of_sessions,
        sport.training_days,
        sport.training_time,
        sport.trainers
      ]);
    }

    String csvData = const ListToCsvConverter().convert(rows);

    try {
      // Request storage permission
      if (Platform.isAndroid || Platform.isIOS) {
        var status = await Permission.storage.status;
        if (!status.isGranted) {
          status = await Permission.storage.request();
        }
        if (!status.isGranted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Storage permission denied')),
          );
          return;
        }
      }
      
      final String dir = (await getApplicationDocumentsDirectory()).path;
      final String path = '$dir/sports_data.csv';
      final File file = File(path);
      await file.writeAsString(csvData);

      // Use file_saver to save the file to a user-accessible location
      await FileSaver.instance.saveFile(
        name: "sports_data",
        bytes: file.readAsBytesSync(),
        ext: "csv",
        mimeType: MimeType.csv
      );

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('CSV exported successfully to Downloads folder')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to export CSV: $e')),
      );
    }
  }

  void _sort<T>(Comparable<T> Function(Sport s) getField, int columnIndex, bool ascending) {
    _sports.sort((a, b) {
      final aValue = getField(a);
      final bValue = getField(b);
      return ascending ? Comparable.compare(aValue, bValue) : Comparable.compare(bValue, aValue);
    });
    setState(() {
      _sortColumnIndex = columnIndex;
      _sortAscending = ascending;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Sports Management'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _isLoading ? null : _refreshSports,
          ),
          IconButton(
            icon: const Icon(Icons.download),
            tooltip: 'Export to CSV',
            onPressed: _isLoading ? null : _exportToCsv,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _sports.isEmpty
              ? const Center(child: Text('No sports found.'))
              : SingleChildScrollView(
                  scrollDirection: Axis.vertical,
                  child: PaginatedDataTable(
                    header: const Text('Sports List'),
                    rowsPerPage: _rowsPerPage,
                    onRowsPerPageChanged: (value) {
                      setState(() {
                        _rowsPerPage = value!;
                      });
                    },
                    sortColumnIndex: _sortColumnIndex,
                    sortAscending: _sortAscending,
                    columns: [
                      DataColumn(
                        label: const Text('ID'),
                        numeric: true,
                        onSort: (columnIndex, ascending) => _sort<String>((s) => s.id?.toString() ?? '', columnIndex, ascending),
                      ),
                      DataColumn(
                        label: const Text('Name'),
                        onSort: (columnIndex, ascending) => _sort<String>((s) => s.name, columnIndex, ascending),
                      ),
                      DataColumn(
                        label: const Text('Fees (SAR)'),
                        numeric: true,
                        onSort: (columnIndex, ascending) => _sort<num>((s) => s.fees, columnIndex, ascending),
                      ),
                      DataColumn(
                        label: const Text('Sessions/Month'),
                        numeric: true,
                        onSort: (columnIndex, ascending) => _sort<num>((s) => s.number_of_sessions, columnIndex, ascending),
                      ),
                      // const DataColumn(label: Text('Training Days')), // Removed as per request
                      const DataColumn(label: Text('Trainers')),
                      const DataColumn(label: Text('Training Times')), // Moved to be before Actions
                      const DataColumn(label: Text('Actions')),
                    ],
                    source: _SportDataSource(context, _sports, _navigateToSportManagement, _showDeleteConfirmation, _showRefundConfirmation),
                  ),
                ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _navigateToSportManagement(),
        child: const Icon(Icons.add),
        tooltip: 'Add Sport',
      ),
    );
  }

  void _showDeleteConfirmation(Sport sport) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Sport'),
        content: Text('Are you sure you want to delete ${sport.name}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteSport(sport);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _showRefundConfirmation(Sport sport) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Refund Sport'),
        content: Text('Are you sure you want to refund ${sport.name}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _refundSport(sport);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.blue),
            child: const Text('Refund'),
          ),
        ],
      ),
    );
  }
}

class _SportDataSource extends DataTableSource {
  final BuildContext context;
  final List<Sport> _sports;
  final Function(Sport?) _navigateToSportManagement;
  final Function(Sport) _showDeleteConfirmation;
  final Function(Sport) _showRefundConfirmation;

  _SportDataSource(this.context, this._sports, this._navigateToSportManagement, this._showDeleteConfirmation, this._showRefundConfirmation);

  @override
  DataRow? getRow(int index) {
    if (index >= _sports.length) return null;
    final sport = _sports[index];
    return DataRow.byIndex(
      index: index,
      cells: [
        DataCell(Text(sport.id?.toString() ?? 'N/A')),
        DataCell(Text(sport.name)),
        DataCell(Text(sport.fees.toStringAsFixed(2))),
        DataCell(Text(sport.number_of_sessions.toString())),
        // DataCell(Text(sport.training_days ?? '')), // Removed as per request
        DataCell(Text(sport.trainers)),
        DataCell(Text(sport.training_time)), // Moved to be before Actions
        DataCell(
          PopupMenuButton<
              String>(
            onSelected: (value) {
              if (value == 'edit') {
                _navigateToSportManagement(sport);
              } else if (value == 'delete') {
                _showDeleteConfirmation(sport);
              } else if (value == 'refund') {
                _showRefundConfirmation(sport);
              }
            },
            itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
              const PopupMenuItem<String>(
                value: 'edit',
                child: ListTile(leading: Icon(Icons.edit), title: Text('Edit')),
              ),
              const PopupMenuItem<String>(
                value: 'delete',
                child: ListTile(leading: Icon(Icons.delete), title: Text('Delete')),
              ),
              const PopupMenuItem<String>(
                value: 'refund',
                child: ListTile(leading: Icon(Icons.money_off), title: Text('Refund')),
              ),
            ],
            icon: const Icon(Icons.more_vert),
          ),
        ),
      ],
    );
  }

  @override
  bool get isRowCountApproximate => false;

  @override
  int get rowCount => _sports.length;

  @override
  int get selectedRowCount => 0;
}
