// ignore_for_file: library_private_types_in_public_api, use_build_context_synchronously, deprecated_member_use

import 'package:flutter/material.dart';
import 'package:table_calendar/table_calendar.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;

class SportsScheduleScreen extends StatefulWidget {
  const SportsScheduleScreen({Key? key}) : super(key: key);

  @override
  _SportsScheduleScreenState createState() => _SportsScheduleScreenState();
}

class _SportsScheduleScreenState extends State<SportsScheduleScreen> {
  CalendarFormat _calendarFormat = CalendarFormat.week;
  DateTime _focusedDay = DateTime.now();
  DateTime? _selectedDay;
  Map<DateTime, List<dynamic>> _events = {};
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _selectedDay = _focusedDay;
    _loadSchedule();
  }

  int _dayToInt(String day) {
    switch (day.toLowerCase()) {
      case 'monday': return 1;
      case 'tuesday': return 2;
      case 'wednesday': return 3;
      case 'thursday': return 4;
      case 'friday': return 5;
      case 'saturday': return 6;
      case 'sunday': return 7;
      default: return 1; // Default to Monday
    }
  }

  List<dynamic> _getEventsForDay(DateTime day) {
    // Normalize the input date to midnight for comparison
    final normalizedDay = DateTime(day.year, day.month, day.day);
    return _events[normalizedDay] ?? [];
  }

  Future<void> _loadSchedule() async {
    setState(() => _isLoading = true);
    try {
      final response = await http.get(
        Uri.parse('https://backend2.hemmasportacademy.com/timeplan/sport_time_plan.php'),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        Map<DateTime, List<dynamic>> events = {};
        
        // Process each sport schedule
        for (var item in data) {
          final timeSlots = item['training_time'].split(',');
          
          // Process each time slot for the sport
          for (var slot in timeSlots) {
            final parts = slot.trim().split(' ');
            if (parts.length >= 2) {
              final day = parts[0];
              final timeRange = parts[1].split('-');
              
              if (timeRange.length == 2) {
                // Get all dates for the next year that match this weekday
                final startDate = DateTime.now();
                final endDate = startDate.add(const Duration(days: 365));
                DateTime currentDate = startDate;
                
                while (currentDate.isBefore(endDate)) {
                  // If current date matches the weekday (e.g., all Mondays)
                  if (_dayToInt(day) == currentDate.weekday) {
                    // Create a normalized date for the event (midnight)
                    final normalizedDate = DateTime(
                      currentDate.year,
                      currentDate.month,
                      currentDate.day,
                    );
                    
                    // Initialize the list if needed
                    events[normalizedDate] ??= [];
                    
                    // Add the event with all details
                    events[normalizedDate]!.add({
                      'sport_name': item['sport_name'],
                      'trainer_name': item['trainer'],
                      'day': day,
                      'start_time': timeRange[0],
                      'end_time': timeRange[1],
                      'session_days': item['session_days'],
                    });
                  }
                  // Move to next day
                  currentDate = currentDate.add(const Duration(days: 1));
                }
              }
            }
          }
        }
        
        setState(() => _events = events);
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading schedule: $e')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Sports Schedule'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadSchedule,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                TableCalendar(
                  firstDay: DateTime.now().subtract(const Duration(days: 365)),
                  lastDay: DateTime.now().add(const Duration(days: 365)),
                  focusedDay: _focusedDay,
                  calendarFormat: _calendarFormat,
                  selectedDayPredicate: (day) {
                    return isSameDay(_selectedDay, day);
                  },
                  onDaySelected: (selectedDay, focusedDay) {
                    setState(() {
                      _selectedDay = selectedDay;
                      _focusedDay = focusedDay;
                    });
                  },
                  calendarStyle: CalendarStyle(
                    selectedDecoration: BoxDecoration(
                      color: Colors.blue,
                      shape: BoxShape.circle,
                    ),
                    todayDecoration: BoxDecoration(
                      color: Colors.blue.withOpacity(0.5),
                      shape: BoxShape.circle,
                    ),
                    markersMaxCount: 5,  // Increased to show more events
                    markerDecoration: BoxDecoration(
                      color: Colors.blue.shade300,
                      shape: BoxShape.circle,
                    ),
                    markersAnchor: 1.0,
                    markerSize: 8.0,
                  ),
                  onFormatChanged: (format) {
                    setState(() {
                      _calendarFormat = format;
                    });
                  },
                  onPageChanged: (focusedDay) {
                    setState(() {
                      _focusedDay = focusedDay;
                    });
                  },
                  eventLoader: _getEventsForDay,
                ),
                const Divider(),
                Expanded(
                  child: _selectedDay == null
                      ? const Center(child: Text('Select a day to view schedule'))
                      : ListView.builder(
                          itemCount: _getEventsForDay(_selectedDay!).length,
                          itemBuilder: (context, index) {
                            final event = _getEventsForDay(_selectedDay!)[index];
                            return Card(
                              margin: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 4,
                              ),
                              child: ExpansionTile(
                                leading: CircleAvatar(
                                  backgroundColor: Colors.blue.shade100,
                                  child: Text(
                                    event['sport_name']?[0]?.toUpperCase() ?? '?',
                                    style: TextStyle(
                                      color: Colors.blue.shade900,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                                title: Text(
                                  event['sport_name'] ?? 'Unknown Sport',
                                  style: const TextStyle(fontWeight: FontWeight.bold),
                                ),
                                subtitle: Text(
                                  '${event['start_time']} - ${event['end_time']}',
                                  style: const TextStyle(color: Colors.blue),
                                ),
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.all(16.0),
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text('Trainer: ${event['trainer_name']}'),
                                        Text('Session Days: ${event['session_days']}'),
                                        const SizedBox(height: 8),
                                        Container(
                                          padding: const EdgeInsets.all(8),
                                          decoration: BoxDecoration(
                                            color: Colors.blue.withOpacity(0.1),
                                            borderRadius: BorderRadius.circular(8),
                                          ),
                                          child: Text(
                                            'Weekly on ${event['day']}',
                                            style: const TextStyle(
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                ),
              ],
            ),
    );
  }
}