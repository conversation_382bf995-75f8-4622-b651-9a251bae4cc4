// ignore_for_file: unused_local_variable, library_private_types_in_public_api, use_build_context_synchronously, avoid_print, unused_element, prefer_final_fields

import 'package:flutter/material.dart';
import 'package:table_calendar/table_calendar.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;

class StadiumBookingScreen extends StatefulWidget {
  const StadiumBookingScreen({Key? key}) : super(key: key);

  @override
  _StadiumBookingScreenState createState() => _StadiumBookingScreenState();
}

class _StadiumBookingScreenState extends State<StadiumBookingScreen> {
  bool _isLoading = false;
  List<Map<String, dynamic>> _stadiums = [];
  Map<String, dynamic>? _selectedStadium;
  DateTime _selectedDate = DateTime.now();
  List<String> _availableTimeSlots = [];
  List<String> _selectedTimeSlots = []; // Replace String? _selectedTimeSlot
  double _totalFee = 0.0; // Add this variable for total fee

  @override
  void initState() {
    super.initState();
    _loadStadiums();
  }

  Future<void> _loadStadiums() async {
    setState(() => _isLoading = true);
    try {
      final response = await http.get(
        Uri.parse('https://backend2.hemmasportacademy.com/stadiums/select_stadiums.php'),
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        setState(() {
          _stadiums = data.map((item) => Map<String, dynamic>.from(item)).toList();
          _isLoading = false;
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading stadiums: $e')),
      );
      setState(() => _isLoading = false);
    }
  }

  // Add controllers for user information
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  void _handleTimeSlotSelection(String timeSlot) {
    setState(() {
      if (_selectedTimeSlots.contains(timeSlot)) {
        _selectedTimeSlots.remove(timeSlot);
      } else {
        _selectedTimeSlots.add(timeSlot);
      }
      
      // Calculate total fee
      _totalFee = _selectedTimeSlots.length * 
          (double.parse(_selectedStadium?['stadium_fees'].toString() ?? '0.0'));
    });
  }

  Future<void> _bookStadium() async {
    if (_selectedStadium == null || _selectedTimeSlots.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select both stadium and time slots')),
      );
      return;
    }

    // Validate user information
    if (_nameController.text.isEmpty || 
        _emailController.text.isEmpty || 
        _phoneController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please fill in all user information')),
      );
      return;
    }

    setState(() => _isLoading = true);
    try {
      // Create separate bookings for each time slot
      for (String timeSlot in _selectedTimeSlots) {
        final response = await http.post(
          Uri.parse('https://backend2.hemmasportacademy.com/stadium_bookings/insert_stadium_booking.php'),
          headers: {'Content-Type': 'application/json'},
          body: json.encode({
            'stadium_id': _selectedStadium!['id'],
            'stadium_name': _selectedStadium!['stadium_name'],
            'booking_date': _selectedDate.toIso8601String().split('T')[0],
            'time_slot': timeSlot,
            'booking_status': 'pending',
            'booking_fee': double.parse(_selectedStadium!['stadium_fees'].toString()),
            'name': _nameController.text,
            'email': _emailController.text,
            'phone': _phoneController.text,
          }),
        );

        if (response.statusCode != 200) {
          throw Exception('Failed to book time slot: $timeSlot');
        }
      }
      
      // Show success dialog with customer service message
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text('Booking Successful!'),
            content: const Text(
              'Thank you for your booking. Our customer service team will contact you shortly to confirm your reservation.',
              style: TextStyle(fontSize: 16),
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: const Text('OK'),
              ),
            ],
          );
        },
      );
      
      // Reset form
      setState(() {
        _selectedTimeSlots.clear();
        _totalFee = 0.0;
        _nameController.clear();
        _emailController.clear();
        _phoneController.clear();
      });
      
      _loadAvailableTimeSlots();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error booking stadium: $e')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  // Add a map to track booked dates
  Map<DateTime, bool> _fullyBookedDates = {};
  Map<String, bool> _bookedTimeSlots = {};

  Future<void> _loadAvailableTimeSlots() async {
    if (_selectedStadium == null) return;
    
    setState(() => _isLoading = true);
    try {
      // Load current bookings for the selected date
      final bookingsResponse = await http.post(
        Uri.parse('https://backend2.hemmasportacademy.com/stadium_bookings/select_stadium_bookings.php'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'stadium_id': _selectedStadium!['id'],
          'booking_date': _selectedDate.toIso8601String().split('T')[0],
        }),
      );

      if (bookingsResponse.statusCode == 200) {
        final List<dynamic> bookings = json.decode(bookingsResponse.body);
        
        // Create a map of booked time slots
        _bookedTimeSlots = Map.fromEntries(
          bookings.map((booking) => MapEntry(booking['time_slot'].toString(), true))
        );

        // Check if all time slots are booked
        final allTimeSlots = [
          '06:00-07:00', '07:00-08:00','08:00-09:00',
          '09:00-10:00','10:00-11:00', '11:00-12:00',
          '12:00-13:00','13:00-14:00', '14:00-15:00',
          '15:00-16:00','16:00-17:00', '17:00-18:00',
          '18:00-19:00','19:00-20:00', '20:00-21:00',
          '21:00-22:00','22:00-23:00', '23:00-00:00',
          '00:00-01:00', '01:00-02:00', '02:00-03:00',
          '03:00-04:00', '04:00-05:00', '05:00-06:00',
        ];

        setState(() {
          _availableTimeSlots = allTimeSlots;
          _fullyBookedDates[_selectedDate] = bookings.length >= allTimeSlots.length;
          _isLoading = false;
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading time slots: $e')),
      );
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Stadium Booking'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Stadium Selection
                  Container(
                    margin: const EdgeInsets.only(bottom: 16.0),
                    padding: const EdgeInsets.symmetric(horizontal: 12.0),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                    child: DropdownButton<Map<String, dynamic>>(
                      isExpanded: true,
                      value: _selectedStadium,
                      hint: const Text('Select Stadium'),
                      underline: Container(),
                      items: _stadiums.map((stadium) {
                        return DropdownMenuItem(
                          value: stadium,
                          child: Text('${stadium['stadium_name']} (${stadium['stadium_fees']}/hour)'),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedStadium = value;
                          _selectedTimeSlots.clear(); // Clear selected time slots instead of _selectedTimeSlot
                        });
                        _loadAvailableTimeSlots();
                      },
                    ),
                  ),

                  // Calendar
                  Card(
                    child: TableCalendar(
                      firstDay: DateTime.now(),
                      lastDay: DateTime.now().add(const Duration(days: 30)),
                      focusedDay: _selectedDate,
                      selectedDayPredicate: (day) => isSameDay(_selectedDate, day),
                      onDaySelected: (selectedDay, focusedDay) {
                        setState(() {
                          _selectedDate = selectedDay;
                          _selectedTimeSlots.clear();
                        });
                        _loadAvailableTimeSlots();
                      },
                      calendarFormat: CalendarFormat.month,
                      calendarStyle: CalendarStyle(
                        // Mark fully booked dates in red
                        markerDecoration: const BoxDecoration(
                          color: Colors.red,
                          shape: BoxShape.circle,
                        ),
                        markersMaxCount: 1,
                        markerSizeScale: 0.2,
                        markersAnchor: 1.0,
                        // Custom day container for fully booked dates
                        defaultDecoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.transparent,
                        ),
                        selectedDecoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Theme.of(context).primaryColor,
                        ),
                        todayDecoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.blue.shade100,
                        ),
                      ),
                      calendarBuilders: CalendarBuilders(
                        markerBuilder: (context, date, events) {
                          if (_fullyBookedDates[date] == true) {
                            return Container(
                              margin: const EdgeInsets.all(4.0),
                              decoration: const BoxDecoration(
                                shape: BoxShape.circle,
                                color: Colors.red,
                              ),
                              width: 8,
                              height: 8,
                            );
                          }
                          return null;
                        },
                      ),
                    ),
                  ),

                  const SizedBox(height: 16.0),

                  // Time Slots
                  if (_selectedStadium != null) ...[
                    const Text(
                      'Available Time Slots:',
                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8.0),
                    Container(
                      padding: const EdgeInsets.all(8.0),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                      child: Wrap(
                        spacing: 8.0,
                        runSpacing: 8.0,
                        children: _availableTimeSlots.map((timeSlot) {
                          final isSelected = _selectedTimeSlots.contains(timeSlot);
                          final isBooked = _bookedTimeSlots[timeSlot] == true;
                          return FilterChip(
                            label: Text(timeSlot),
                            selected: isSelected,
                            selectedColor: Colors.blue.shade100,
                            backgroundColor: isBooked ? Colors.grey.shade300 : null,
                            onSelected: isBooked ? null : (selected) {
                              _handleTimeSlotSelection(timeSlot);
                            },
                          );
                        }).toList(),
                      ),
                    ),
                    
                    if (_selectedTimeSlots.isNotEmpty) ...[
                      const SizedBox(height: 16.0),
                      Text(
                        'Total Hours: ${_selectedTimeSlots.length}',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8.0),
                      Text(
                        'Total Fee: \$${_totalFee.toStringAsFixed(2)}',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          color: Theme.of(context).primaryColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],

                    // Add User Information Fields
                    const SizedBox(height: 24.0),
                    const Text(
                      'Contact Information:',
                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 16.0),
                    TextField(
                      controller: _nameController,
                      decoration: InputDecoration(
                        labelText: 'Name',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8.0),
                        ),
                        filled: true,
                        fillColor: Colors.grey.shade50,
                      ),
                    ),
                    const SizedBox(height: 12.0),
                    TextField(
                      controller: _emailController,
                      keyboardType: TextInputType.emailAddress,
                      decoration: InputDecoration(
                        labelText: 'Email',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8.0),
                        ),
                        filled: true,
                        fillColor: Colors.grey.shade50,
                      ),
                    ),
                    const SizedBox(height: 12.0),
                    TextField(
                      controller: _phoneController,
                      keyboardType: TextInputType.phone,
                      decoration: InputDecoration(
                        labelText: 'Phone',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8.0),
                        ),
                        filled: true,
                        fillColor: Colors.grey.shade50,
                      ),
                    ),
                    
                    const SizedBox(height: 24.0),
                    ElevatedButton(
                      onPressed: _selectedTimeSlots.isNotEmpty ? _bookStadium : null,
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16.0),
                        backgroundColor: _selectedTimeSlots.isNotEmpty ? Colors.blue : Colors.grey,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8.0),
                        ),
                      ),
                      child: const Text(
                        'Book Now',
                        style: TextStyle(
                          fontSize: 16.0,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
    );
  }

  Future<List<Map<String, dynamic>>> _loadCurrentBookings() async {
    try {
      final response = await http.get(
        Uri.parse('https://backend2.hemmasportacademy.com/stadium_bookings/select_stadium_bookings.php'),
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((item) => Map<String, dynamic>.from(item)).toList();
      }
      return [];
    } catch (e) {
      print('Error loading bookings: $e');
      return [];
    }
  }

  Future<void> _deleteBooking(String bookingId) async {
    setState(() => _isLoading = true);
    try {
      final response = await http.delete(
        Uri.parse('https://backend2.hemmasportacademy.com/stadium_bookings/delete_stadium_booking.php'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({'id': bookingId}),
      );

      if (response.statusCode == 200) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Booking deleted successfully!')),
        );
        setState(() {});  // Refresh the UI
      } else {
        throw Exception('Failed to delete booking');
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error deleting booking: $e')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }
}