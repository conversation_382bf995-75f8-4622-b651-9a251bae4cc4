// ignore_for_file: library_private_types_in_public_api, use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

class StadiumRegistrationScreen extends StatefulWidget {
  const StadiumRegistrationScreen({Key? key}) : super(key: key);

  @override
  _StadiumRegistrationScreenState createState() => _StadiumRegistrationScreenState();
}

class _StadiumRegistrationScreenState extends State<StadiumRegistrationScreen> {
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;
  List<Map<String, dynamic>> _stadiums = [];
  
  // Form controllers
  final _stadiumNameController = TextEditingController();
  final _feesController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadStadiums();
  }

  Future<void> _loadStadiums() async {
    setState(() => _isLoading = true);
    try {
      final response = await http.get(
        Uri.parse('https://backend2.hemmasportacademy.com/stadiums/select_stadiums.php'),
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        setState(() {
          _stadiums = data.map((item) => Map<String, dynamic>.from(item)).toList();
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading stadiums: $e')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);
    try {
      final response = await http.post(
        Uri.parse('https://backend2.hemmasportacademy.com/stadiums/insert_stadium.php'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'stadium_name': _stadiumNameController.text,
          'stadium_fees': double.parse(_feesController.text),
        }),
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        if (responseData['message'] == 'Stadium inserted successfully') {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Stadium registered successfully!')),
          );
          _formKey.currentState!.reset();
          _loadStadiums(); // Reload the list after successful registration
        } else {
          throw Exception(responseData['message'] ?? 'Failed to register stadium');
        }
      } else {
        throw Exception('Failed to register stadium');
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error registering stadium: $e')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _deleteStadium(String id) async {
    setState(() => _isLoading = true);
    try {
      final response = await http.post(
        Uri.parse('https://backend2.hemmasportacademy.com/stadiums/delete_stadium.php'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({'id': id}),
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        if (responseData['message'] == 'Stadium deleted successfully') {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Stadium deleted successfully!')),
          );
          _loadStadiums(); // Reload the list
        } else {
          throw Exception(responseData['message'] ?? 'Failed to delete stadium');
        }
      } else {
        throw Exception('Failed to delete stadium');
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error deleting stadium: $e')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _updateStadium(Map<String, dynamic> stadium) async {
    final TextEditingController nameController = TextEditingController(text: stadium['stadium_name']);
    final TextEditingController feesController = TextEditingController(text: stadium['stadium_fees'].toString());

    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Update Stadium'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'Stadium Name',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: feesController,
              decoration: const InputDecoration(
                labelText: 'Fees per Hour',
                border: OutlineInputBorder(),
                suffixText: '/hour',
              ),
              keyboardType: TextInputType.number,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              await _submitUpdate(
                stadium['id'],
                nameController.text,
                double.parse(feesController.text),
              );
            },
            child: const Text('Update'),
          ),
        ],
      ),
    );

    nameController.dispose();
    feesController.dispose();
  }

  Future<void> _submitUpdate(String id, String name, double fees) async {
    setState(() => _isLoading = true);
    try {
      final response = await http.post(
        Uri.parse('https://backend2.hemmasportacademy.com/stadiums/update_stadium.php'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'id': id,
          'stadium_name': name,
          'stadium_fees': fees,
        }),
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        if (responseData['message'] == 'Stadium updated successfully') {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Stadium updated successfully!')),
          );
          _loadStadiums(); // Reload the list
        } else {
          throw Exception(responseData['message'] ?? 'Failed to update stadium');
        }
      } else {
        throw Exception('Failed to update stadium');
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error updating stadium: $e')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Register Stadium'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        TextFormField(
                          controller: _stadiumNameController,
                          decoration: const InputDecoration(
                            labelText: 'Stadium Name',
                            border: OutlineInputBorder(),
                          ),
                          validator: (value) =>
                              value?.isEmpty ?? true ? 'Please enter stadium name' : null,
                        ),
                        const SizedBox(height: 16),
                        TextFormField(
                          controller: _feesController,
                          decoration: const InputDecoration(
                            labelText: 'Fees per Hour',
                            border: OutlineInputBorder(),
                            suffixText: '/hour',
                          ),
                          keyboardType: TextInputType.number,
                          validator: (value) =>
                              value?.isEmpty ?? true ? 'Please enter fees per hour' : null,
                        ),
                        const SizedBox(height: 24),
                        ElevatedButton(
                          onPressed: _submitForm,
                          child: const Text('Register Stadium'),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 32),
                  const Text(
                    'Registered Stadiums',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  if (_stadiums.isEmpty)
                    const Center(
                      child: Text('No stadiums registered yet'),
                    )
                  else
                    ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: _stadiums.length,
                      itemBuilder: (context, index) {
                        final stadium = _stadiums[index];
                        return Card(
                          margin: const EdgeInsets.only(bottom: 8.0),
                          child: ListTile(
                            title: Text(stadium['stadium_name'] ?? ''),
                            subtitle: Text('${stadium['stadium_fees']} per hour'),
                            trailing: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                IconButton(
                                  icon: const Icon(Icons.edit, color: Colors.blue),
                                  onPressed: () => _updateStadium(stadium),
                                ),
                                IconButton(
                                  icon: const Icon(Icons.delete, color: Colors.red),
                                  onPressed: () => _deleteStadium(stadium['id']),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                ],
              ),
            ),
    );
  }

  @override
  void dispose() {
    _stadiumNameController.dispose();
    _feesController.dispose();
    super.dispose();
  }
}