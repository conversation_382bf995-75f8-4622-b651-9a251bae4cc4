// ignore_for_file: library_private_types_in_public_api, use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:hemmaerp/screens/general_ledger_screen.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'trainer_management_screen.dart';
import '../services/api_service.dart';

class TrainerListScreen extends StatefulWidget {
  const TrainerListScreen({Key? key}) : super(key: key);

  @override
  _TrainerListScreenState createState() => _TrainerListScreenState();
}

class _TrainerListScreenState extends State<TrainerListScreen> {
  List<Map<String, dynamic>> _trainers = [];
  List<Map<String, dynamic>> _filteredTrainers = [];
  bool _isLoading = false;
  String _searchQuery = '';
  int? _sortColumnIndex;
  bool _sortAscending = true;
  final TextEditingController _searchController = TextEditingController();
  final ApiService _apiService = ApiService();

  @override
  void initState() {
    super.initState();
    _loadTrainers();
  }

  void _sort<T>(
    Comparable<T> Function(Map<String, dynamic> trainer) getField,
    int columnIndex,
    bool ascending,
  ) {
    _filteredTrainers.sort((a, b) {
      final aValue = getField(a);
      final bValue = getField(b);
      return ascending
          ? Comparable.compare(aValue, bValue)
          : Comparable.compare(bValue, aValue);
    });
    setState(() {
      _sortColumnIndex = columnIndex;
      _sortAscending = ascending;
    });
  }

  void _filterTrainers(String query) {
    query.toLowerCase();
    setState(() {
      _searchQuery = query;
      _filteredTrainers =
          _trainers.where((trainer) {
            final name = (trainer['name'] ?? '').toString().toLowerCase();
            final status = (trainer['status'] ?? '').toString().toLowerCase();
            final phone = (trainer['phone'] ?? '').toString().toLowerCase();
            final email = (trainer['email'] ?? '').toString().toLowerCase();
            final searchLower = query.toLowerCase();
            return name.contains(searchLower) ||
                status.contains(searchLower) ||
                phone.contains(searchLower) ||
                email.contains(searchLower);
          }).toList();
    });
  }

  Future<void> _loadTrainers() async {
    setState(() => _isLoading = true);
    try {
      final data = await _apiService.getTrainers();
      setState(() {
        _trainers = List<Map<String, dynamic>>.from(
          data.map((item) => Map<String, dynamic>.from(item)),
        );
        _filterTrainers(_searchQuery);
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error loading trainers: $e')));
      }
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(AppLocalizations.of(context)?.trainers ?? 'Trainers'),
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                labelText: AppLocalizations.of(context)?.search ?? 'Search',
                prefixIcon: const Icon(Icons.search),
                border: const OutlineInputBorder(),
              ),
              onChanged: _filterTrainers,
            ),
          ),
          Expanded(
            child:
                _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : RefreshIndicator(
                      onRefresh: _loadTrainers,
                      child:
                          _filteredTrainers.isEmpty
                              ? LayoutBuilder(
                                builder:
                                    (
                                      context,
                                      constraints,
                                    ) => SingleChildScrollView(
                                      physics:
                                          const AlwaysScrollableScrollPhysics(),
                                      child: SizedBox(
                                        height: constraints.maxHeight,
                                        width: constraints.maxWidth,
                                        child: Center(
                                          child: Text(
                                            AppLocalizations.of(
                                                  context,
                                                )?.noDataAvailable ??
                                                'No data available',
                                            style:
                                                Theme.of(
                                                  context,
                                                ).textTheme.titleMedium,
                                          ),
                                        ),
                                      ),
                                    ),
                              )
                              : SingleChildScrollView(
                                scrollDirection: Axis.horizontal,
                                child: DataTable(
                                  sortColumnIndex: _sortColumnIndex,
                                  sortAscending: _sortAscending,
                                  columns: [
                                    DataColumn(
                                      label: Text(
                                        AppLocalizations.of(context)?.name ??
                                            'Name',
                                      ),
                                      onSort: (columnIndex, ascending) {
                                        _sort<String>(
                                          (trainer) =>
                                              trainer['full_name']
                                                  ?.toString() ??
                                              '',
                                          columnIndex,
                                          ascending,
                                        );
                                      },
                                    ),
                                    DataColumn(
                                      label: Text(
                                        AppLocalizations.of(context)?.status ??
                                            'Status',
                                      ),
                                      onSort: (columnIndex, ascending) {
                                        _sort<String>(
                                          (trainer) =>
                                              trainer['status']?.toString() ??
                                              '',
                                          columnIndex,
                                          ascending,
                                        );
                                      },
                                    ),
                                    DataColumn(
                                      label: Text(
                                        AppLocalizations.of(context)?.phone ??
                                            'Phone',
                                      ),
                                      onSort: (columnIndex, ascending) {
                                        _sort<String>(
                                          (trainer) =>
                                              trainer['phone']?.toString() ??
                                              '',
                                          columnIndex,
                                          ascending,
                                        );
                                      },
                                    ),
                                    DataColumn(
                                      label: Text(
                                        AppLocalizations.of(context)?.actions ??
                                            'Actions',
                                      ),
                                    ),
                                  ],
                                  rows:
                                      _filteredTrainers.map((trainer) {
                                        return DataRow(
                                          cells: [
                                            DataCell(
                                              Text(
                                                trainer['name']?.toString() ??
                                                    '',
                                              ),
                                            ),
                                            DataCell(
                                              Text(
                                                trainer['status']?.toString() ??
                                                    '',
                                              ),
                                            ),
                                            DataCell(
                                              Text(
                                                trainer['phone']?.toString() ??
                                                    '',
                                              ),
                                            ),
                                            DataCell(
                                              IconButton(
                                                icon: const Icon(
                                                  Icons.edit,
                                                  color: Colors.blue,
                                                ),
                                                onPressed: () async {
                                                  await Navigator.push(
                                                    context,
                                                    MaterialPageRoute(
                                                      builder:
                                                          (context) =>
                                                              TrainerManagementScreen(
                                                                trainer:
                                                                    trainer,
                                                              ),
                                                    ),
                                                  );
                                                  _loadTrainers();
                                                },
                                              ),
                                            ),
                                          ],
                                        );
                                      }).toList(),
                                ),
                              ),
                    ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const TrainerManagementScreen(),
            ),
          );
          if (result == true) {
            _loadTrainers();
          }
        },
        child: const Icon(Icons.add),
      ),
    );
  }
}
