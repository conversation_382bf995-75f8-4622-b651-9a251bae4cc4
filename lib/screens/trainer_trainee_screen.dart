// ignore_for_file: library_private_types_in_public_api, use_build_context_synchronously, unnecessary_to_list_in_spreads

import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;

class TrainerTraineeScreen extends StatefulWidget {
  const TrainerTraineeScreen({Key? key}) : super(key: key);

  @override
  _TrainerTraineeScreenState createState() => _TrainerTraineeScreenState();
}

class _TrainerTraineeScreenState extends State<TrainerTraineeScreen> {
  bool _isLoading = false;
  List<dynamic> _traineeList = [];
  List<String> _trainersList = [];
  List<String> _trainingDaysList = [];
  String? _selectedTrainer;
  String? _selectedTrainingDay;

  @override
  void initState() {
    super.initState();
    _loadTrainees();
  }

  Future<void> _loadTrainees() async {
    setState(() => _isLoading = true);
    try {
      final response = await http.get(
        Uri.parse(
          'https://backend2.hemmasportacademy.com/timeplan/trainer_trainees.php',
        ),
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        // Extract unique trainer names and training days
        final Set<String> trainerSet = {};
        final Set<String> trainingDaysSet = {};

        for (var item in data) {
          if (item['trainer_name'] != null &&
              item['trainer_name'].toString().isNotEmpty) {
            trainerSet.add(item['trainer_name'].toString());
          }
          if (item['training_days'] != null &&
              item['training_days'].toString().isNotEmpty) {
            // Split training days if they contain multiple days
            final days = item['training_days'].toString().split(',');
            for (var day in days) {
              final cleanDay =
                  day.split(':')[0].trim(); // Get only the day part
              if (cleanDay.isNotEmpty) {
                trainingDaysSet.add(cleanDay);
              }
            }
          }
        }

        final List<String> trainers = trainerSet.toList()..sort();
        final List<String> trainingDays = trainingDaysSet.toList()..sort();

        setState(() {
          _traineeList = data;
          _trainersList = trainers;
          _trainingDaysList = trainingDays;
          if (_selectedTrainer == null && trainers.isNotEmpty) {
            _selectedTrainer = trainers.first;
          }
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Error loading trainees: $e')));
    } finally {
      setState(() => _isLoading = false);
    }
  }

  List<dynamic> get _filteredTrainees {
    return _traineeList.where((trainee) {
      bool matchesTrainer =
          _selectedTrainer == null ||
          trainee['trainer_name'] == _selectedTrainer;

      bool matchesDay =
          _selectedTrainingDay == null ||
          (trainee['training_days'] != null &&
              trainee['training_days'].toString().contains(
                _selectedTrainingDay!,
              ));

      return matchesTrainer && matchesDay;
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Trainer Trainees'),
        actions: [
          IconButton(icon: const Icon(Icons.refresh), onPressed: _loadTrainees),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : Column(
                children: [
                  Container(
                    margin: const EdgeInsets.all(16.0),
                    padding: const EdgeInsets.symmetric(horizontal: 12.0),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                    child: DropdownButton<String>(
                      isExpanded: true,
                      value: _selectedTrainer,
                      hint: const Text('Select Trainer'),
                      underline: Container(),
                      items:
                          _trainersList.map((String trainer) {
                            return DropdownMenuItem<String>(
                              value: trainer,
                              child: Text(trainer),
                            );
                          }).toList(),
                      onChanged: (String? newValue) {
                        setState(() {
                          _selectedTrainer = newValue;
                        });
                      },
                    ),
                  ),
                  Container(
                    margin: const EdgeInsets.symmetric(
                      horizontal: 16.0,
                      vertical: 8.0,
                    ),
                    padding: const EdgeInsets.symmetric(horizontal: 12.0),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                    child: DropdownButton<String>(
                      isExpanded: true,
                      value: _selectedTrainingDay,
                      hint: const Text('Select Training Day'),
                      underline: Container(),
                      items: [
                        DropdownMenuItem<String>(
                          value: null,
                          child: Text('All Days'),
                        ),
                        ..._trainingDaysList.map((String day) {
                          return DropdownMenuItem<String>(
                            value: day,
                            child: Text(day),
                          );
                        }).toList(),
                      ],
                      onChanged: (String? newValue) {
                        setState(() {
                          _selectedTrainingDay = newValue;
                        });
                      },
                    ),
                  ),
                  Expanded(
                    child: ListView.builder(
                      itemCount: _filteredTrainees.length,
                      itemBuilder: (context, index) {
                        final trainee = _filteredTrainees[index];
                        return Card(
                          margin: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 8,
                          ),
                          child: ExpansionTile(
                            leading: CircleAvatar(
                              backgroundColor: Colors.blue.shade100,
                              child: Text(
                                trainee['customer_name']?[0]?.toUpperCase() ??
                                    '?',
                                style: TextStyle(
                                  color: Colors.blue.shade900,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            title: Text(
                              trainee['customer_name'] ?? 'Unknown',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            subtitle: Text(trainee['sport_name'] ?? 'No Sport'),
                            children: [
                              Padding(
                                padding: const EdgeInsets.all(16.0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Customer ID: ${trainee['customer_id']}',
                                    ),
                                    Text('Sport: ${trainee['sport_name']}'),
                                    Text(
                                      'Training Days: ${trainee['training_days'] ?? 'Not set'}',
                                    ),
                                    Text(
                                      'Subscription: ${trainee['subscription_type']} - ${trainee['subscription_duration']}',
                                    ),
                                    Text(
                                      'Number of Sessions: ${trainee['number_of_sessions']}',
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
    );
  }
}
