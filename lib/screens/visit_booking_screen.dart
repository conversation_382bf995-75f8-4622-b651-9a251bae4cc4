// ignore_for_file: library_private_types_in_public_api, use_build_context_synchronously

import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;

class VisitBookingScreen extends StatefulWidget {
  const VisitBookingScreen({Key? key}) : super(key: key);

  @override
  _VisitBookingScreenState createState() => _VisitBookingScreenState();
}

class _VisitBookingScreenState extends State<VisitBookingScreen> {
  bool _isLoading = false;
  List<String> _sportsList = [];
  List<String> _trainersList = [];
  List<String> _availableDays = [];
  List<String> _availableTimeSlots = [];
  String? _selectedSport;
  String? _selectedTrainer;
  String? _selectedDay;
  String? _selectedTimeSlot;
  
  // Add controllers for text fields
  final TextEditingController _fullNameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();

  @override
  void dispose() {
    _fullNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _loadSports();
  }

  Future<void> _loadSports() async {
    setState(() => _isLoading = true);
    try {
      final response = await http.get(
        Uri.parse('https://backend2.hemmasportacademy.com/timeplan/sport_time_plan.php'),
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        final Set<String> sportsSet = {};
        
        for (var item in data) {
          if (item['sport_name'] != null && item['sport_name'].toString().isNotEmpty) {
            sportsSet.add(item['sport_name'].toString());
          }
        }
        
        final List<String> sports = sportsSet.toList()..sort();

        setState(() {
          _sportsList = sports;
          _isLoading = false;
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading sports: $e')),
      );
      setState(() => _isLoading = false);
    }
  }

  Future<void> _loadTrainers() async {
    if (_selectedSport == null) return;
    
    setState(() => _isLoading = true);
    try {
      final response = await http.get(
        Uri.parse('https://backend2.hemmasportacademy.com/timeplan/sport_time_plan.php'),
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        final Set<String> trainerSet = {};
        
        for (var item in data) {
          if (item['sport_name'] == _selectedSport && 
              item['trainer'] != null && 
              item['trainer'].toString().isNotEmpty) {
            trainerSet.add(item['trainer'].toString());
          }
        }
        
        final List<String> trainers = trainerSet.toList()..sort();

        setState(() {
          _trainersList = trainers;
          _isLoading = false;
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading trainers: $e')),
      );
      setState(() => _isLoading = false);
    }
  }

  Future<void> _loadAvailableDays() async {
    if (_selectedTrainer == null || _selectedSport == null) return;
    
    setState(() => _isLoading = true);
    try {
      final response = await http.get(
        Uri.parse('https://backend2.hemmasportacademy.com/timeplan/sport_time_plan.php'),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final schedule = data.firstWhere(
          (item) => item['trainer'] == _selectedTrainer && 
                    item['sport_name'] == _selectedSport,
          orElse: () => null,
        );

        if (schedule != null && schedule['training_time'] != null) {
          final timeSlots = schedule['training_time'].toString().split(',');
          final Map<String, List<String>> dayTimeSlots = {};
          
          for (var slot in timeSlots) {
            final parts = slot.trim().split(' ');
            if (parts.length >= 2) {
              final day = parts[0].trim();
              final time = parts[1].trim();
              dayTimeSlots.putIfAbsent(day, () => []).add(time);
            }
          }

          setState(() {
            _availableDays = dayTimeSlots.keys.toList()..sort();
            _availableTimeSlots = [];
            _isLoading = false;
          });
        } else {
          setState(() {
            _availableDays = [];
            _availableTimeSlots = [];
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading available days: $e')),
      );
      setState(() => _isLoading = false);
    }
  }

  Future<void> _loadTimeSlots() async {
    if (_selectedDay == null || _selectedTrainer == null || _selectedSport == null) return;
    
    setState(() => _isLoading = true);
    try {
      final response = await http.get(
        Uri.parse('https://backend2.hemmasportacademy.com/timeplan/sport_time_plan.php'),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final schedule = data.firstWhere(
          (item) => item['trainer'] == _selectedTrainer && 
                    item['sport_name'] == _selectedSport,
          orElse: () => null,
        );

        if (schedule != null && schedule['training_time'] != null) {
          final timeSlots = schedule['training_time'].toString().split(',');
          final List<String> availableTimes = [];
          
          for (var slot in timeSlots) {
            final parts = slot.trim().split(' ');
            if (parts.length >= 2 && parts[0].trim() == _selectedDay) {
              availableTimes.add(parts[1].trim());
            }
          }

          setState(() {
            _availableTimeSlots = availableTimes..sort();
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading time slots: $e')),
      );
      setState(() => _isLoading = false);
    }
  }

  Future<void> _submitBooking() async {
    if (_fullNameController.text.isEmpty ||
        _emailController.text.isEmpty ||
        _phoneController.text.isEmpty ||
        _selectedSport == null ||
        _selectedTrainer == null ||
        _selectedDay == null ||
        _selectedTimeSlot == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please fill in all fields')),
      );
      return;
    }

    setState(() => _isLoading = true);
    try {
      final response = await http.post(
        Uri.parse('https://backend2.hemmasportacademy.com/booking/insert_visit_booking.php'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: json.encode({
          'sport_name': _selectedSport,
          'trainer_name': _selectedTrainer,
          'training_day': _selectedDay,
          'training_time': _selectedTimeSlot,
          'full_name': _fullNameController.text,
          'email': _emailController.text,
          'phone_number': _phoneController.text,
        }),
      );

      if (response.statusCode == 200) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Booking successful!')),
        );
        // Clear form
        setState(() {
          _selectedSport = null;
          _selectedTrainer = null;
          _selectedDay = null;
          _selectedTimeSlot = null;
          _fullNameController.clear();
          _emailController.clear();
          _phoneController.clear();
        });
        _loadSports(); // Reload the form
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'Failed to create booking');
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error creating booking: $e')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Visit Booking'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Sport Selection
                  Container(
                    margin: const EdgeInsets.only(bottom: 16.0),
                    padding: const EdgeInsets.symmetric(horizontal: 12.0),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                    child: DropdownButton<String>(
                      isExpanded: true,
                      value: _selectedSport,
                      hint: const Text('Select Sport'),
                      underline: Container(),
                      items: _sportsList.map((sport) {
                        return DropdownMenuItem(
                          value: sport,
                          child: Text(sport),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedSport = value;
                          _selectedTrainer = null;
                          _selectedDay = null;
                          _trainersList = [];
                          _availableDays = [];
                        });
                        if (value != null) {
                          _loadTrainers();
                        }
                      },
                    ),
                  ),

                  // Trainer Selection
                  if (_selectedSport != null)
                    Container(
                      margin: const EdgeInsets.only(bottom: 16.0),
                      padding: const EdgeInsets.symmetric(horizontal: 12.0),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                      child: DropdownButton<String>(
                        isExpanded: true,
                        value: _selectedTrainer,
                        hint: const Text('Select Trainer'),
                        underline: Container(),
                        items: _trainersList.map((trainer) {
                          return DropdownMenuItem(
                            value: trainer,
                            child: Text(trainer),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedTrainer = value;
                            _selectedDay = null;
                            _availableDays = [];
                          });
                          if (value != null) {
                            _loadAvailableDays();
                          }
                        },
                      ),
                    ),

                  // Available Days
                  if (_selectedTrainer != null && _availableDays.isNotEmpty)
                    Container(
                      margin: const EdgeInsets.only(bottom: 16.0),
                      padding: const EdgeInsets.symmetric(horizontal: 12.0),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                      child: DropdownButton<String>(
                        isExpanded: true,
                        value: _selectedDay,
                        hint: const Text('Select Day'),
                        underline: Container(),
                        items: _availableDays.map((day) {
                          return DropdownMenuItem(
                            value: day,
                            child: Text(day),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedDay = value;
                            _selectedTimeSlot = null;
                          });
                          if (value != null) {
                            _loadTimeSlots();
                          }
                        },
                      ),
                    ),

                  // Time Slots
                  if (_selectedDay != null && _availableTimeSlots.isNotEmpty)
                    Container(
                      margin: const EdgeInsets.only(bottom: 16.0),
                      padding: const EdgeInsets.symmetric(horizontal: 12.0),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                      child: DropdownButton<String>(
                        isExpanded: true,
                        value: _selectedTimeSlot,
                        hint: const Text('Select Time'),
                        underline: Container(),
                        items: _availableTimeSlots.map((time) {
                          return DropdownMenuItem(
                            value: time,
                            child: Text(time),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedTimeSlot = value;
                          });
                        },
                      ),
                    ),

                  // Personal Information Fields
                  if (_selectedTimeSlot != null) ...[
                    const SizedBox(height: 20),
                    const Text(
                      'Personal Information',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: _fullNameController,
                      decoration: InputDecoration(
                        labelText: 'Full Name',
                        hintText: 'Enter your full name',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8.0),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 16,
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: _emailController,
                      keyboardType: TextInputType.emailAddress,
                      decoration: InputDecoration(
                        labelText: 'Email',
                        hintText: 'Enter your email address',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8.0),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 16,
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: _phoneController,
                      keyboardType: TextInputType.phone,
                      decoration: InputDecoration(
                        labelText: 'Phone Number',
                        hintText: 'Enter your phone number',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8.0),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 16,
                        ),
                      ),
                    ),
                    const SizedBox(height: 24),
                    ElevatedButton(
                      onPressed: _submitBooking,
                      child: const Text('Book Visit'),
                    ),
                  ],
                ],
              ),
            ),
    );
  }
}