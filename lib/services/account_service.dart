import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import '../models/account.dart';

class AccountService {
  final String baseUrl;
  
  AccountService({required this.baseUrl});
  
  // Get all accounts
  Future<List<Account>> getAccounts() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/accounts'),
        headers: {'Content-Type': 'application/json'},
      );
      
      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body)['data'];
        return data.map((json) => Account.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load accounts: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching accounts: $e');
      return [];
    }
  }
  
  // Get account by ID
  Future<Account?> getAccountById(int id) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/accounts/$id'),
        headers: {'Content-Type': 'application/json'},
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body)['data'];
        return Account.fromJson(data);
      } else {
        throw Exception('Failed to load account: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching account $id: $e');
      return null;
    }
  }
  
  // Create new account
  Future<Account?> createAccount(Account account) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/accounts'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode(account.toJson()),
      );
      
      if (response.statusCode == 201) {
        final data = json.decode(response.body)['data'];
        return Account.fromJson(data);
      } else {
        throw Exception('Failed to create account: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error creating account: $e');
      return null;
    }
  }
  
  // Update account
  Future<Account?> updateAccount(Account account) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/accounts/${account.id}'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode(account.toJson()),
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body)['data'];
        return Account.fromJson(data);
      } else {
        throw Exception('Failed to update account: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error updating account: $e');
      return null;
    }
  }
  
  // Delete account
  Future<bool> deleteAccount(int id) async {
    try {
      final response = await http.delete(
        Uri.parse('$baseUrl/accounts/$id'),
        headers: {'Content-Type': 'application/json'},
      );
      
      return response.statusCode == 200 || response.statusCode == 204;
    } catch (e) {
      debugPrint('Error deleting account $id: $e');
      return false;
    }
  }
}