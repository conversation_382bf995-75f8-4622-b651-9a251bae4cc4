// ignore_for_file: avoid_print, unused_element

import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../constants/api_constants.dart';

/// Centralized API service class for all HTTP requests
/// Provides type-safe methods for each endpoint with consistent error handling
class ApiService {
  static final ApiService _instance = ApiService._internal();
  factory ApiService() => _instance;
  ApiService._internal();

  /// HTTP client instance
  final http.Client _client = http.Client();

  /// Default headers for all requests
  Map<String, String> get _defaultHeaders => {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  /// Handles HTTP response and returns parsed data or throws an exception
  dynamic _handleResponse(http.Response response) {
    if (kDebugMode) {
      print('API Response: ${response.statusCode} - ${response.body}');
    }

    if (response.statusCode >= 200 && response.statusCode < 300) {
      try {
        if (response.body.isEmpty) {
          return null;
        }
        return json.decode(response.body);
      } catch (e) {
        if (kDebugMode) {
          print('Error parsing response: $e');
        }
        throw ApiException('Failed to parse response', response.statusCode);
      }
    } else {
      throw ApiException(
        'Request failed with status ${response.statusCode}',
        response.statusCode,
      );
    }
  }

  /// Generic GET request
  Future<dynamic> _get(String url, {Map<String, String>? queryParams}) async {
    try {
      final uri = Uri.parse(url).replace(queryParameters: queryParams);
      final response = await _client
          .get(uri, headers: _defaultHeaders)
          .timeout(
            const Duration(milliseconds: ApiConstants.connectionTimeout),
          );
      return _handleResponse(response);
    } on SocketException {
      throw ApiException('No internet connection', 0);
    } on HttpException {
      throw ApiException('HTTP error occurred', 0);
    } catch (e) {
      if (e is ApiException) rethrow;
      throw ApiException('Unexpected error: $e', 0);
    }
  }

  /// Generic POST request
  Future<dynamic> _post(String url, {Map<String, dynamic>? body}) async {
    try {
      final response = await _client
          .post(
            Uri.parse(url),
            headers: _defaultHeaders,
            body: body != null ? json.encode(body) : null,
          )
          .timeout(
            const Duration(milliseconds: ApiConstants.connectionTimeout),
          );
      return _handleResponse(response);
    } on SocketException {
      throw ApiException('No internet connection', 0);
    } on HttpException {
      throw ApiException('HTTP error occurred', 0);
    } catch (e) {
      if (e is ApiException) rethrow;
      throw ApiException('Unexpected error: $e', 0);
    }
  }

  /// Generic PUT request
  Future<dynamic> _put(String url, {Map<String, dynamic>? body}) async {
    try {
      final response = await _client
          .put(
            Uri.parse(url),
            headers: _defaultHeaders,
            body: body != null ? json.encode(body) : null,
          )
          .timeout(
            const Duration(milliseconds: ApiConstants.connectionTimeout),
          );
      return _handleResponse(response);
    } on SocketException {
      throw ApiException('No internet connection', 0);
    } on HttpException {
      throw ApiException('HTTP error occurred', 0);
    } catch (e) {
      if (e is ApiException) rethrow;
      throw ApiException('Unexpected error: $e', 0);
    }
  }

  /// Generic DELETE request
  Future<dynamic> _delete(String url, {Map<String, dynamic>? body}) async {
    try {
      final response = await _client
          .delete(
            Uri.parse(url),
            headers: _defaultHeaders,
            body: body != null ? json.encode(body) : null,
          )
          .timeout(
            const Duration(milliseconds: ApiConstants.connectionTimeout),
          );
      return _handleResponse(response);
    } on SocketException {
      throw ApiException('No internet connection', 0);
    } on HttpException {
      throw ApiException('HTTP error occurred', 0);
    } catch (e) {
      if (e is ApiException) rethrow;
      throw ApiException('Unexpected error: $e', 0);
    }
  }

  // ============================================================================
  // CUSTOMER MANAGEMENT APIs
  // ============================================================================

  /// Fetches all customer data including customer details, sports, and payments
  Future<Map<String, dynamic>> fetchAllCustomerData() async {
    final data = await _get(ApiConstants.customersSelectAll);
    return {
      'customers': data['customers'] ?? [],
      'customer_sports': data['customer_sports'] ?? [],
      'payments': data['payments'] ?? [],
    };
  }

  /// Gets all customers
  Future<List<dynamic>> getCustomers() async {
    return await _get(ApiConstants.customersSelect);
  }

  /// Gets a specific customer by ID
  Future<Map<String, dynamic>?> getCustomerById(String customerId) async {
    final data = await _get(
      ApiConstants.customersSelect,
      queryParams: {'id': customerId},
    );
    if (data is List && data.isNotEmpty) {
      return data.first;
    }
    return null;
  }

  /// Creates a new customer
  Future<Map<String, dynamic>> createCustomer(
    Map<String, dynamic> customerData,
  ) async {
    return await _post(
      '${ApiConstants.customersCrud}&action=insert',
      body: customerData,
    );
  }

  /// Updates an existing customer
  Future<Map<String, dynamic>> updateCustomer(
    Map<String, dynamic> customerData,
  ) async {
    return await _post(
      '${ApiConstants.customersCrud}&action=update',
      body: customerData,
    );
  }

  // ============================================================================
  // SPORTS MANAGEMENT APIs
  // ============================================================================

  /// Gets all sports
  Future<List<dynamic>> getSports() async {
    return await _get(ApiConstants.sportsSelect);
  }

  /// Gets sports using CRUD endpoint
  Future<List<dynamic>> getSportsCrud() async {
    return await _get('${ApiConstants.sportsCrud}&action=select');
  }

  // ============================================================================
  // STADIUM MANAGEMENT APIs
  // ============================================================================

  /// Gets all stadiums
  Future<List<dynamic>> getStadiums() async {
    return await _get(ApiConstants.stadiumsSelect);
  }

  /// Creates a new stadium
  Future<Map<String, dynamic>> createStadium(
    Map<String, dynamic> stadiumData,
  ) async {
    return await _post(ApiConstants.stadiumsInsert, body: stadiumData);
  }

  /// Updates an existing stadium
  Future<Map<String, dynamic>> updateStadium(
    Map<String, dynamic> stadiumData,
  ) async {
    return await _post(ApiConstants.stadiumsUpdate, body: stadiumData);
  }

  /// Deletes a stadium
  Future<Map<String, dynamic>> deleteStadium(String stadiumId) async {
    return await _post(ApiConstants.stadiumsDelete, body: {'id': stadiumId});
  }

  // ============================================================================
  // STADIUM BOOKING APIs
  // ============================================================================

  /// Gets all stadium bookings
  Future<List<dynamic>> getStadiumBookings() async {
    return await _get(ApiConstants.stadiumBookingsSelect);
  }

  /// Gets stadium bookings by criteria
  Future<List<dynamic>> getStadiumBookingsByCriteria(
    Map<String, dynamic> criteria,
  ) async {
    return await _post(ApiConstants.stadiumBookingsSelect, body: criteria);
  }

  /// Creates a new stadium booking
  Future<Map<String, dynamic>> createStadiumBooking(
    Map<String, dynamic> bookingData,
  ) async {
    return await _post(ApiConstants.stadiumBookingsInsert, body: bookingData);
  }

  /// Updates a stadium booking
  Future<Map<String, dynamic>> updateStadiumBooking(
    Map<String, dynamic> bookingData,
  ) async {
    return await _post(ApiConstants.stadiumBookingsUpdate, body: bookingData);
  }

  /// Deletes a stadium booking
  Future<Map<String, dynamic>> deleteStadiumBooking(String bookingId) async {
    return await _delete(
      ApiConstants.stadiumBookingsDelete,
      body: {'id': bookingId},
    );
  }

  // ============================================================================
  // VISIT BOOKING APIs
  // ============================================================================

  /// Gets all visit bookings
  Future<List<dynamic>> getVisitBookings() async {
    return await _get(ApiConstants.visitBookingsSelect);
  }

  /// Creates a new visit booking
  Future<Map<String, dynamic>> createVisitBooking(
    Map<String, dynamic> bookingData,
  ) async {
    return await _post(ApiConstants.visitBookingsInsert, body: bookingData);
  }

  // ============================================================================
  // EMPLOYEE MANAGEMENT APIs
  // ============================================================================

  /// Gets all employees
  Future<List<dynamic>> getEmployees() async {
    return await _get(ApiConstants.employeesSelect);
  }

  // ============================================================================
  // PAYROLL APIs
  // ============================================================================

  /// Gets all payroll records
  Future<List<dynamic>> getPayrollRecords() async {
    return await _get(ApiConstants.payrollSelect);
  }

  /// Creates a new payroll record
  Future<Map<String, dynamic>> createPayrollRecord(
    Map<String, dynamic> payrollData,
  ) async {
    return await _post(ApiConstants.payrollInsert, body: payrollData);
  }

  /// Updates a payroll record
  Future<Map<String, dynamic>> updatePayrollRecord(
    Map<String, dynamic> payrollData,
  ) async {
    return await _post(ApiConstants.payrollUpdate, body: payrollData);
  }

  /// Deletes a payroll record
  Future<Map<String, dynamic>> deletePayrollRecord(String payrollId) async {
    return await _post(
      ApiConstants.payrollDelete,
      body: {'id': int.parse(payrollId)},
    );
  }

  // ============================================================================
  // TRAINER MANAGEMENT APIs
  // ============================================================================

  /// Gets all trainers
  Future<List<dynamic>> getTrainers() async {
    return await _get(ApiConstants.trainersSelect);
  }

  // ============================================================================
  // TIME PLANNING APIs
  // ============================================================================

  /// Gets sports time plan/schedule
  Future<List<dynamic>> getSportTimePlan() async {
    return await _get(ApiConstants.sportTimePlan);
  }

  /// Gets trainer-trainee relationships
  Future<List<dynamic>> getTrainerTrainees() async {
    return await _get(ApiConstants.trainerTrainees);
  }

  // ============================================================================
  // GENERAL LEDGER APIs
  // ============================================================================

  /// Gets general ledger customer data
  Future<List<dynamic>> getGeneralLedgerData() async {
    return await _get(ApiConstants.generalLedgerSelect);
  }

  // ============================================================================
  // PROMO CODE APIs
  // ============================================================================

  /// Gets all promo codes
  Future<List<dynamic>> getPromoCodes() async {
    return await _get(ApiConstants.promoCodesSelect);
  }

  /// Creates a new promo code
  Future<Map<String, dynamic>> createPromoCode(
    Map<String, dynamic> promoCodeData,
  ) async {
    return await _post(ApiConstants.promoCodesInsert, body: promoCodeData);
  }

  /// Updates a promo code
  Future<Map<String, dynamic>> updatePromoCode(
    Map<String, dynamic> promoCodeData,
  ) async {
    return await _post(ApiConstants.promoCodesUpdate, body: promoCodeData);
  }

  /// Deletes a promo code
  Future<Map<String, dynamic>> deletePromoCode(String promoCodeId) async {
    return await _post(
      ApiConstants.promoCodesDelete,
      body: {'id': promoCodeId},
    );
  }

  /// Gets promo code eligibility
  Future<List<dynamic>> getPromoCodeEligibility(String promoCodeId) async {
    return await _get(
      ApiConstants.promoCodeEligibilitySelect,
      queryParams: {'promo_code_id': promoCodeId},
    );
  }

  /// Adds promo code eligibility
  Future<Map<String, dynamic>> addPromoCodeEligibility(
    Map<String, dynamic> eligibilityData,
  ) async {
    return await _post(
      ApiConstants.promoCodeEligibilityInsert,
      body: eligibilityData,
    );
  }

  /// Removes promo code eligibility
  Future<Map<String, dynamic>> removePromoCodeEligibility(
    String eligibilityId,
  ) async {
    return await _post(
      ApiConstants.promoCodeEligibilityDelete,
      body: {'id': eligibilityId},
    );
  }

  /// Gets promo code usages
  Future<List<dynamic>> getPromoCodeUsages(String promoCodeId) async {
    return await _get(
      ApiConstants.promoCodeUsagesSelect,
      queryParams: {'promo_code_id': promoCodeId},
    );
  }

  /// Adds promo code usage
  Future<Map<String, dynamic>> addPromoCodeUsage(
    Map<String, dynamic> usageData,
  ) async {
    return await _post(ApiConstants.promoCodeUsagesInsert, body: usageData);
  }

  /// Removes promo code usage
  Future<Map<String, dynamic>> removePromoCodeUsage(String usageId) async {
    return await _post(
      ApiConstants.promoCodeUsagesDelete,
      body: {'id': usageId},
    );
  }

  /// Dispose the HTTP client
  void dispose() {
    _client.close();
  }
}

/// Custom exception class for API errors
class ApiException implements Exception {
  final String message;
  final int statusCode;

  ApiException(this.message, this.statusCode);

  @override
  String toString() => 'ApiException: $message (Status: $statusCode)';
}
