import 'dart:async';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

enum ApiHealthStatus { unknown, healthy, unhealthy, checking }

// Enum to identify screens uniquely
enum ScreenIdentifier {
  manageBookings,
  customerList,
  employeeList,
  generalLedger,
  payroll,
  sportsSchedule,
  stadiumRegistration,
  trainerList,
  trainerTrainee,
  visitBooking,
  bookingList,
  stadiumBooking,
  allCardsAndCharts,
}

// Map screens to their primary health-check API endpoints
const Map<ScreenIdentifier, String> screenApiEndpoints = {
  ScreenIdentifier.manageBookings:
      'https://backend2.hemmasportacademy.com/stadium_bookings/select_stadium_bookings.php',
  ScreenIdentifier.customerList:
      'https://backend2.hemmasportacademy.com/customers/select_all.php', // Updated to working endpoint
  ScreenIdentifier.employeeList:
      'https://backend2.hemmasportacademy.com/employees/select_employee.php',
  ScreenIdentifier.generalLedger:
      'https://backend2.hemmasportacademy.com/gledger/select_customer.php', // Choosing one representative API
  ScreenIdentifier.payroll:
      'https://backend2.hemmasportacademy.com/payroll/select_payroll.php',
  ScreenIdentifier.sportsSchedule:
      'https://backend2.hemmasportacademy.com/timeplan/sport_time_plan.php',
  ScreenIdentifier.stadiumRegistration:
      'https://backend2.hemmasportacademy.com/stadiums/select_stadiums.php',
  ScreenIdentifier.trainerList:
      'https://backend2.hemmasportacademy.com/trainers/get_trainers.php',
  ScreenIdentifier.trainerTrainee:
      'https://backend2.hemmasportacademy.com/timeplan/trainer_trainees.php',
  ScreenIdentifier.visitBooking:
      'https://backend2.hemmasportacademy.com/booking/insert_visit_booking.php', // Using insert as a check, or could use sport_time_plan
  ScreenIdentifier.bookingList:
      'https://backend2.hemmasportacademy.com/booking/select_visit_bookings.php',
  ScreenIdentifier.stadiumBooking:
      'https://backend2.hemmasportacademy.com/stadium_bookings/select_stadium_bookings.php',
  ScreenIdentifier.allCardsAndCharts:
      'https://backend2.hemmasportacademy.com/api/status/all_cards_and_charts.php', // Placeholder URL
};

// Helper to get display names for screens from localizations
String getScreenNameForStatus(
  ScreenIdentifier identifier,
  AppLocalizations l10n,
) {
  switch (identifier) {
    case ScreenIdentifier.manageBookings:
      return l10n.manageBookings;
    case ScreenIdentifier.customerList:
      return l10n.customers;
    case ScreenIdentifier.employeeList:
      return l10n.employees;
    case ScreenIdentifier.generalLedger:
      return l10n.generalLedger;
    case ScreenIdentifier.payroll:
      return l10n.payroll;
    case ScreenIdentifier.sportsSchedule:
      return l10n.sportsSchedule;
    case ScreenIdentifier.stadiumRegistration:
      return l10n.stadiumRegistration;
    case ScreenIdentifier.trainerList:
      return l10n.trainers;
    case ScreenIdentifier.trainerTrainee:
      return l10n.trainerTrainee;
    case ScreenIdentifier.visitBooking:
      return l10n.visitBooking;
    case ScreenIdentifier.bookingList:
      return l10n.bookings;
    // Fallback name
    case ScreenIdentifier.stadiumBooking:
      return l10n.stadiumBooking;
    case ScreenIdentifier.allCardsAndCharts:
      return l10n.allCardsAndCharts;
  }
}

class ApiStatusService with ChangeNotifier {
  final Map<ScreenIdentifier, ApiHealthStatus> _apiStatuses = {};
  Timer? _timer;

  ApiStatusService() {
    // Initialize all statuses to unknown
    for (var screen in ScreenIdentifier.values) {
      _apiStatuses[screen] = ApiHealthStatus.unknown;
    }
    // Start periodic checks
    _fetchAllStatuses();
    _timer = Timer.periodic(const Duration(minutes: 1), (timer) {
      _fetchAllStatuses();
    });
  }

  Map<ScreenIdentifier, ApiHealthStatus> get apiStatuses => _apiStatuses;

  Future<void> _checkApiStatus(ScreenIdentifier screen) async {
    final endpoint = screenApiEndpoints[screen];
    if (endpoint == null) {
      _apiStatuses[screen] =
          ApiHealthStatus.unknown; // Or a specific 'notConfigured' status
      notifyListeners();
      return;
    }

    _apiStatuses[screen] = ApiHealthStatus.checking;
    notifyListeners();

    try {
      final response = await http
          .get(Uri.parse(endpoint))
          .timeout(const Duration(seconds: 10));
      if (response.statusCode >= 200 && response.statusCode < 300) {
        _apiStatuses[screen] = ApiHealthStatus.healthy;
      } else {
        _apiStatuses[screen] = ApiHealthStatus.unhealthy;
      }
    } catch (e) {
      _apiStatuses[screen] = ApiHealthStatus.unhealthy;
    }
    notifyListeners();
  }

  Future<void> _fetchAllStatuses() async {
    for (var screen in ScreenIdentifier.values) {
      // Don't re-check if already checking, or stagger them
      if (_apiStatuses[screen] != ApiHealthStatus.checking) {
        _checkApiStatus(screen); // No await here to run in parallel
      }
    }
  }

  void forceRefreshAll() {
    // Set all to checking to give immediate feedback on UI if desired
    // for (var screen in ScreenIdentifier.values) {
    //   _apiStatuses[screen] = ApiHealthStatus.checking;
    // }
    // notifyListeners();
    _fetchAllStatuses();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }
}
