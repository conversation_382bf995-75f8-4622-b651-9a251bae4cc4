import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';
import 'secure_api_service.dart';
import '../utils/validators.dart';
import '../utils/security_utils.dart';

/// Authentication service with secure token management
class AuthService {
  static const String _tokenKey = 'auth_token';
  static const String _refreshTokenKey = 'refresh_token';
  static const String _userDataKey = 'user_data';
  static const String _lastLoginKey = 'last_login';

  final SecureApiService _apiService;
  UserData? _currentUser;

  AuthService(this._apiService);

  /// Check if user is authenticated
  bool get isAuthenticated => _currentUser != null && _hasValidToken();

  /// Get current user data
  UserData? get currentUser => _currentUser;

  /// Login with email and password
  Future<AuthResult> login(String email, String password) async {
    try {
      // Validate inputs
      final emailError = CustomerValidators.validateEmail(email);
      if (emailError != null) {
        return AuthResult.failure(emailError);
      }

      if (password.isEmpty) {
        return AuthResult.failure('Password is required');
      }

      if (password.length < 6) {
        return AuthResult.failure('Password must be at least 6 characters');
      }

      // Sanitize inputs
      final sanitizedEmail = CustomerValidators.sanitizeInput(email);

      // Make login request
      final response = await _apiService.securePost('/auth/login', {
        'email': sanitizedEmail,
        'password': password,
        'device_info': await _getDeviceInfo(),
      });

      if (response['success'] == true) {
        final authToken = response['access_token'] as String?;
        final refreshToken = response['refresh_token'] as String?;
        final userData = response['user'] as Map<String, dynamic>?;

        if (authToken == null || refreshToken == null || userData == null) {
          return AuthResult.failure('Invalid response from server');
        }

        // Validate token format
        if (!SecurityUtils.isValidSessionToken(authToken)) {
          return AuthResult.failure('Invalid token format');
        }

        // Store tokens securely
        await _storeTokens(authToken, refreshToken);

        // Store user data
        _currentUser = UserData.fromJson(userData);
        await _storeUserData(_currentUser!);

        // Set tokens in API service
        _apiService.setAuthTokens(authToken, refreshToken);

        // Record login time
        await _recordLoginTime();

        return AuthResult.success(_currentUser!);
      } else {
        final message = response['message'] as String? ?? 'Login failed';
        return AuthResult.failure(message);
      }
    } catch (e) {
      _logAuthEvent('Login failed', email, e);
      return AuthResult.failure('Login failed: ${e.toString()}');
    }
  }

  /// Logout user
  Future<void> logout() async {
    try {
      // Notify server about logout
      if (_currentUser != null) {
        await _apiService.securePost('/auth/logout', {
          'user_id': _currentUser!.id,
        });
      }
    } catch (e) {
      // Continue with logout even if server call fails
      _logAuthEvent('Logout server call failed', _currentUser?.email ?? '', e);
    }

    // Clear local data
    await _clearAuthData();
    _currentUser = null;
  }

  /// Refresh authentication token
  Future<bool> refreshAuthToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final storedRefreshToken = prefs.getString(_refreshTokenKey);

      if (storedRefreshToken == null) {
        return false;
      }

      final success = await _apiService.refreshAuthToken();
      if (success) {
        await _recordLoginTime();
        return true;
      }
    } catch (e) {
      _logAuthEvent('Token refresh failed', '', e);
    }

    return false;
  }

  /// Initialize authentication from stored data
  Future<bool> initializeAuth() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final authToken = prefs.getString(_tokenKey);
      final refreshToken = prefs.getString(_refreshTokenKey);
      final userDataJson = prefs.getString(_userDataKey);

      if (authToken == null || refreshToken == null || userDataJson == null) {
        return false;
      }

      // Validate token format
      if (!SecurityUtils.isValidSessionToken(authToken)) {
        await _clearAuthData();
        return false;
      }

      // Check if tokens are expired
      if (await _isTokenExpired()) {
        final refreshed = await refreshAuthToken();
        if (!refreshed) {
          await _clearAuthData();
          return false;
        }
      }

      // Restore user data
      final userData = jsonDecode(userDataJson) as Map<String, dynamic>;
      _currentUser = UserData.fromJson(userData);

      // Set tokens in API service
      _apiService.setAuthTokens(authToken, refreshToken);

      return true;
    } catch (e) {
      _logAuthEvent('Auth initialization failed', '', e);
      await _clearAuthData();
      return false;
    }
  }

  /// Change user password
  Future<AuthResult> changePassword(
    String currentPassword,
    String newPassword,
  ) async {
    try {
      if (_currentUser == null) {
        return AuthResult.failure('User not authenticated');
      }

      // Validate new password
      final passwordError = CustomerValidators.validatePassword(newPassword);
      if (passwordError != null) {
        return AuthResult.failure(passwordError);
      }

      final response = await _apiService.securePost('/auth/change-password', {
        'current_password': currentPassword,
        'new_password': newPassword,
        'user_id': _currentUser!.id,
      });

      if (response['success'] == true) {
        return AuthResult.success(_currentUser!);
      } else {
        final message =
            response['message'] as String? ?? 'Password change failed';
        return AuthResult.failure(message);
      }
    } catch (e) {
      _logAuthEvent('Password change failed', _currentUser?.email ?? '', e);
      return AuthResult.failure('Password change failed: ${e.toString()}');
    }
  }

  /// Store tokens securely
  Future<void> _storeTokens(String authToken, String refreshToken) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_tokenKey, authToken);
    await prefs.setString(_refreshTokenKey, refreshToken);
  }

  /// Store user data
  Future<void> _storeUserData(UserData userData) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userDataKey, jsonEncode(userData.toJson()));
  }

  /// Clear authentication data
  Future<void> _clearAuthData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_tokenKey);
    await prefs.remove(_refreshTokenKey);
    await prefs.remove(_userDataKey);
    await prefs.remove(_lastLoginKey);
  }

  /// Record login time
  Future<void> _recordLoginTime() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_lastLoginKey, DateTime.now().millisecondsSinceEpoch);
  }

  /// Check if token is expired
  Future<bool> _isTokenExpired() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final authToken = prefs.getString(_tokenKey);

      if (authToken == null) {
        return true;
      }

      // Check JWT token expiration
      if (SecurityUtils.isTokenExpired(authToken)) {
        return true;
      }

      // Also check last login time as fallback (24 hours)
      final lastLogin = prefs.getInt(_lastLoginKey);
      if (lastLogin != null) {
        final lastLoginTime = DateTime.fromMillisecondsSinceEpoch(lastLogin);
        final now = DateTime.now();
        final difference = now.difference(lastLoginTime);

        if (difference.inHours > 24) {
          return true;
        }
      }

      return false;
    } catch (e) {
      // If we can't determine expiration, assume expired for security
      return true;
    }
  }

  /// Check if user has valid token
  bool _hasValidToken() {
    if (_currentUser == null) return false;

    try {
      // Get stored token from SharedPreferences synchronously is not possible
      // So we'll do a basic validation here and rely on initializeAuth for full validation
      return true; // Basic check - user exists
    } catch (e) {
      return false;
    }
  }

  /// Get device information for security
  Future<Map<String, String>> _getDeviceInfo() async {
    return {
      'platform': defaultTargetPlatform.name,
      'app_version': '1.0.0',
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// Log authentication events
  void _logAuthEvent(String event, String email, dynamic error) {
    if (kDebugMode) {
      print('Auth Event: $event');
      print('Email: ${SecurityUtils.maskSensitiveData(email)}');
      print('Error: ${error.toString()}');
    }
  }
}

/// User data model
class UserData {
  final String id;
  final String email;
  final String name;
  final String role;
  final List<String> permissions;

  UserData({
    required this.id,
    required this.email,
    required this.name,
    required this.role,
    required this.permissions,
  });

  factory UserData.fromJson(Map<String, dynamic> json) {
    // Validate required fields
    final id = json['id']?.toString();
    final email = json['email']?.toString();
    final name = json['name']?.toString();

    if (id == null || id.isEmpty) {
      throw ArgumentError('User ID is required');
    }

    if (email == null || email.isEmpty) {
      throw ArgumentError('User email is required');
    }

    if (name == null || name.isEmpty) {
      throw ArgumentError('User name is required');
    }

    return UserData(
      id: id,
      email: email,
      name: name,
      role: json['role']?.toString() ?? 'user',
      permissions: List<String>.from(json['permissions'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'name': name,
      'role': role,
      'permissions': permissions,
    };
  }

  bool hasPermission(String permission) {
    return permissions.contains(permission) || role == 'admin';
  }
}

/// Authentication result
class AuthResult {
  final bool success;
  final String? error;
  final UserData? user;

  AuthResult._(this.success, this.error, this.user);

  factory AuthResult.success(UserData user) {
    return AuthResult._(true, null, user);
  }

  factory AuthResult.failure(String error) {
    return AuthResult._(false, error, null);
  }
}
