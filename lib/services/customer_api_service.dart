// ignore_for_file: avoid_print

import 'package:dio/dio.dart';
import 'package:hemmaerp/models/customers.dart';
import 'package:hemmaerp/models/sport.dart';

class CustomerApiService {
  final String baseUrl;
  final Duration timeout;
  final _dio = Dio();

  CustomerApiService({
    required this.baseUrl,
    required this.timeout,
  }) {
    _dio.options.baseUrl = baseUrl;
    _dio.options.connectTimeout = timeout;
    _dio.options.receiveTimeout = timeout;
  }

  Future<List<Sport>> fetchSports() async {
    try {
      final response = await _dio.get('/crud.php?table=sports&action=select');
      if (response.statusCode == 200) {
        final List<dynamic> data = response.data;
        return data.map((json) => Sport.fromJson(json)).toList();
      }
      throw Exception('Failed to fetch sports: ${response.statusCode}');
    } catch (e) {
      throw Exception('Error fetching sports: $e');
    }
  }

  Future<Customer> saveCustomer(Map<String, dynamic> customerData, {bool isUpdate = false}) async {
    final endpoint = '/crud.php?table=customers&action=${isUpdate ? 'update' : 'insert'}';
    try {
      final response = await _dio.post(
        endpoint,
        data: customerData,
      );
      
      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          return Customer.fromJson(response.data);
        } else {
          throw Exception('Invalid response format from server');
        }
      }
      throw Exception('Failed to save customer: ${response.statusCode}');
    } catch (e) {
      print('Error saving customer: $e'); // Add logging
      throw Exception('Error saving customer: $e');
    }
  }
}