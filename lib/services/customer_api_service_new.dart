import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:hemmaerp/models/customers.dart';

class CustomerApiServiceNew {
  final String baseUrl;
  final Duration timeout;

  CustomerApiServiceNew({
    required this.baseUrl,
    this.timeout = const Duration(seconds: 30),
  });

  Future<List<Customer>> fetchCustomers() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/crud.php?table=customers&action=select'),
      ).timeout(timeout);

      if (response.statusCode == 200) {
        final List<dynamic> jsonData = json.decode(response.body);
        return jsonData.map((json) => Customer.fromJson(json)).toList();
      } else {
        throw Exception('Failed to fetch customers: ${response.statusCode}');
      }
    } catch (e) {
      if (e is http.ClientException) {
        throw Exception('Network error: Please check your internet connection');
      } else if (e is FormatException) {
        throw Exception('Invalid response format from server');
      } else {
        throw Exception('Error fetching customers: $e');
      }
    }
  }

  Future<Customer> createCustomer(Map<String, dynamic> customerData) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/crud.php?table=customers&action=insert'),
        body: {
          ...customerData,
          'status': customerData['status'] ?? 'Active',
          'sport_activity': customerData['sport_activity'] ?? '',
          'sport_level': customerData['sport_level'] ?? '',
          'trainer': customerData['trainer'] ?? '',
          'subscription_type': customerData['subscription_type'] ?? '',
          'subscription_duration': customerData['subscription_duration'] ?? '',
          'number_of_sessions': customerData['number_of_sessions']?.toString() ?? '0',
          'training_times': customerData['training_times'] ?? '',
        },
      ).timeout(timeout);

      if (response.statusCode == 200) {
        // Extract ID from response text or parse JSON response
        final idMatch = RegExp(r'Inserted ID: (\d+)').firstMatch(response.body);
        if (idMatch != null) {
          final id = int.parse(idMatch.group(1)!);
          return Customer.fromJson({...customerData, 'id': id});
        }
        throw Exception('Failed to extract customer ID from response');
      } else {
        throw Exception('Failed to create customer: ${response.statusCode}');
      }
    } catch (e) {
      if (e is http.ClientException) {
        throw Exception('Network error: Please check your internet connection');
      } else if (e is FormatException) {
        throw Exception('Invalid response format from server');
      } else {
        throw Exception('Error creating customer: $e');
      }
    }
  }

  Future<Customer> updateCustomer(Map<String, dynamic> customerData) async {
    if (!customerData.containsKey('id')) {
      throw Exception('Customer ID is required for update');
    }

    try {
      final response = await http.post(
        Uri.parse('$baseUrl/crud.php?table=customers&action=update'),
        body: {
          ...customerData,
          'id': customerData['id'].toString(),
          'status': customerData['status'] ?? 'Active',
          'sport_activity': customerData['sport_activity'] ?? '',
          'sport_level': customerData['sport_level'] ?? '',
          'trainer': customerData['trainer'] ?? '',
          'subscription_type': customerData['subscription_type'] ?? '',
          'subscription_duration': customerData['subscription_duration'] ?? '',
          'number_of_sessions': customerData['number_of_sessions']?.toString() ?? '0',
          'training_times': customerData['training_times'] ?? '',
        },
      ).timeout(timeout);

      if (response.statusCode == 200) {
        return Customer.fromJson(customerData);
      } else {
        throw Exception('Failed to update customer: ${response.statusCode}');
      }
    } catch (e) {
      if (e is http.ClientException) {
        throw Exception('Network error: Please check your internet connection');
      } else if (e is FormatException) {
        throw Exception('Invalid response format from server');
      } else {
        throw Exception('Error updating customer: $e');
      }
    }
  }

  Future<void> deleteCustomer(int customerId) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/crud.php?table=customers&action=delete'),
        body: {'id': customerId.toString()},
      ).timeout(timeout);

      if (response.statusCode != 200) {
        throw Exception('Failed to delete customer: ${response.statusCode}');
      }
    } catch (e) {
      if (e is http.ClientException) {
        throw Exception('Network error: Please check your internet connection');
      } else {
        throw Exception('Error deleting customer: $e');
      }
    }
  }
}