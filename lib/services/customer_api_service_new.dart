import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:hemmaerp/models/customers.dart';

class CustomerApiServiceNew {
  final String baseUrl;
  final Duration timeout;

  CustomerApiServiceNew({
    required this.baseUrl,
    this.timeout = const Duration(seconds: 30),
  });

  Future<List<Customer>> fetchCustomers() async {
    try {
      final response = await http
          .get(Uri.parse('$baseUrl/crud.php?table=customers&action=select'))
          .timeout(timeout);

      if (response.statusCode == 200) {
        final List<dynamic> jsonData = json.decode(response.body);
        return jsonData.map((json) => Customer.fromJson(json)).toList();
      } else {
        throw Exception('Failed to fetch customers: ${response.statusCode}');
      }
    } catch (e) {
      if (e is http.ClientException) {
        throw Exception('Network error: Please check your internet connection');
      } else if (e is FormatException) {
        throw Exception('Invalid response format from server');
      } else {
        throw Exception('Error fetching customers: $e');
      }
    }
  }

  Future<Customer> createCustomer(Map<String, dynamic> customerData) async {
    try {
      final response = await http
          .post(
            Uri.parse('$baseUrl/crud.php?table=customers&action=insert'),
            body: {
              ...customerData,
              'status': customerData['status'] ?? 'Active',
              'sport_activity': customerData['sport_activity'] ?? '',
              'sport_level': customerData['sport_level'] ?? '',
              'trainer': customerData['trainer'] ?? '',
              'subscription_type': customerData['subscription_type'] ?? '',
              'subscription_duration':
                  customerData['subscription_duration'] ?? '',
              'number_of_sessions':
                  customerData['number_of_sessions']?.toString() ?? '0',
              'training_times': customerData['training_times'] ?? '',
            },
          )
          .timeout(timeout);

      if (response.statusCode == 200) {
        // Extract ID from response text or parse JSON response
        final idMatch = RegExp(r'Inserted ID: (\d+)').firstMatch(response.body);
        if (idMatch != null && idMatch.group(1) != null) {
          final idString = idMatch.group(1)!;
          final id = int.parse(idString);
          return Customer.fromJson({...customerData, 'id': id});
        }
        throw Exception('Failed to extract customer ID from response');
      } else {
        throw Exception('Failed to create customer: ${response.statusCode}');
      }
    } catch (e) {
      if (e is http.ClientException) {
        throw Exception('Network error: Please check your internet connection');
      } else if (e is FormatException) {
        throw Exception('Invalid response format from server');
      } else {
        throw Exception('Error creating customer: $e');
      }
    }
  }

  Future<Customer> updateCustomer(Map<String, dynamic> customerData) async {
    if (!customerData.containsKey('id')) {
      throw Exception('Customer ID is required for update');
    }

    try {
      final response = await http
          .post(
            Uri.parse('$baseUrl/crud.php?table=customers&action=update'),
            body: {
              ...customerData,
              'id': customerData['id'].toString(),
              'status': customerData['status'] ?? 'Active',
              'sport_activity': customerData['sport_activity'] ?? '',
              'sport_level': customerData['sport_level'] ?? '',
              'trainer': customerData['trainer'] ?? '',
              'subscription_type': customerData['subscription_type'] ?? '',
              'subscription_duration':
                  customerData['subscription_duration'] ?? '',
              'number_of_sessions':
                  customerData['number_of_sessions']?.toString() ?? '0',
              'training_times': customerData['training_times'] ?? '',
            },
          )
          .timeout(timeout);

      if (response.statusCode == 200) {
        return Customer.fromJson(customerData);
      } else {
        throw Exception('Failed to update customer: ${response.statusCode}');
      }
    } catch (e) {
      if (e is http.ClientException) {
        throw Exception('Network error: Please check your internet connection');
      } else if (e is FormatException) {
        throw Exception('Invalid response format from server');
      } else {
        throw Exception('Error updating customer: $e');
      }
    }
  }

  Future<void> deleteCustomer(int customerId) async {
    try {
      final response = await http
          .post(
            Uri.parse('$baseUrl/crud.php?table=customers&action=delete'),
            body: {'id': customerId.toString()},
          )
          .timeout(timeout);

      if (response.statusCode != 200) {
        throw Exception('Failed to delete customer: ${response.statusCode}');
      }
    } catch (e) {
      if (e is http.ClientException) {
        throw Exception('Network error: Please check your internet connection');
      } else {
        throw Exception('Error deleting customer: $e');
      }
    }
  }

  /// Create multiple customers for family accounts
  /// Returns a list of created customers with their assigned IDs
  Future<List<Customer>> createFamilyMembers(
    List<Map<String, dynamic>> familyMembersData,
  ) async {
    List<Customer> createdCustomers = [];
    List<String> errors = [];

    for (int i = 0; i < familyMembersData.length; i++) {
      try {
        final customerData = familyMembersData[i];
        final createdCustomer = await createCustomer(customerData);
        createdCustomers.add(createdCustomer);
      } catch (e) {
        errors.add('Failed to create family member ${i + 1}: $e');
        // Continue with other family members even if one fails
      }
    }

    // If some customers were created but others failed, we still return the successful ones
    // but include error information
    if (errors.isNotEmpty) {
      final errorMessage = errors.join('\n');
      if (createdCustomers.isEmpty) {
        // If no customers were created, throw an exception
        throw Exception('Failed to create any family members:\n$errorMessage');
      } else {
        // If some were created, log the errors but don't throw
        // You might want to handle this differently based on your requirements
        print('Some family members failed to create:\n$errorMessage');
      }
    }

    return createdCustomers;
  }

  /// Create a family account with parent and children
  /// Returns a map with 'parent' and 'children' keys
  Future<Map<String, dynamic>> createFamilyAccount({
    required Map<String, dynamic> parentData,
    required List<Map<String, dynamic>> childrenData,
  }) async {
    try {
      // First, create the parent record (though it might not be stored separately)
      // For family accounts, we typically store each child as a separate customer record

      List<Customer> createdChildren = [];
      List<String> errors = [];

      // Create each child with shared parent information
      for (int i = 0; i < childrenData.length; i++) {
        try {
          final childData = Map<String, dynamic>.from(childrenData[i]);

          // Ensure shared parent information is included
          childData.addAll({
            'email': parentData['email'],
            'phone': parentData['phone'],
            'address': parentData['address'],
            'nationalId': parentData['nationalId'],
            'fullAddress': parentData['fullAddress'] ?? parentData['address'],
            'parentPhone': parentData['phone'],
            'accountType': 'Family',
            'status': 'active',
            'taxEnabled': false,
          });

          final createdChild = await createCustomer(childData);
          createdChildren.add(createdChild);
        } catch (e) {
          errors.add('Failed to create child ${i + 1}: $e');
        }
      }

      if (createdChildren.isEmpty) {
        throw Exception(
          'Failed to create any family members:\n${errors.join('\n')}',
        );
      }

      if (errors.isNotEmpty) {
        print('Some family members failed to create:\n${errors.join('\n')}');
      }

      return {
        'parent': parentData,
        'children': createdChildren,
        'errors': errors,
        'successCount': createdChildren.length,
        'totalCount': childrenData.length,
      };
    } catch (e) {
      throw Exception('Error creating family account: $e');
    }
  }

  /// Update multiple family members
  Future<List<Customer>> updateFamilyMembers(
    List<Map<String, dynamic>> familyMembersData,
  ) async {
    List<Customer> updatedCustomers = [];
    List<String> errors = [];

    for (int i = 0; i < familyMembersData.length; i++) {
      try {
        final customerData = familyMembersData[i];
        if (customerData.containsKey('id') && customerData['id'] != null) {
          final updatedCustomer = await updateCustomer(customerData);
          updatedCustomers.add(updatedCustomer);
        } else {
          // If no ID, create new customer
          final createdCustomer = await createCustomer(customerData);
          updatedCustomers.add(createdCustomer);
        }
      } catch (e) {
        errors.add('Failed to update family member ${i + 1}: $e');
      }
    }

    if (errors.isNotEmpty) {
      final errorMessage = errors.join('\n');
      if (updatedCustomers.isEmpty) {
        throw Exception('Failed to update any family members:\n$errorMessage');
      } else {
        print('Some family members failed to update:\n$errorMessage');
      }
    }

    return updatedCustomers;
  }
}
