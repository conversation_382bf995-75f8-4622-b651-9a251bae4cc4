// ignore_for_file: avoid_print

import 'dart:convert';
import 'package:http/http.dart' as http;

class CustomerDataService {
  static const String baseUrl = 'https://backend2.hemmasportacademy.com';
  
  /// Fetches all customer data including customer details, sports, and payments
  static Future<Map<String, dynamic>> fetchAllData() async {
    final response = await http.get(
      Uri.parse('$baseUrl/customers/select_all.php'),
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      
      return {
        'customers': data['customers'] ?? [],
        'customer_sports': data['customer_sports'] ?? [],
        'payments': data['payments'] ?? []
      };
    } else {
      throw Exception('Failed to fetch data: ${response.statusCode}');
    }
  }
  
  /// Prints all customer data to console (for debugging)
  static Future<void> printAllCustomerData() async {
    try {
      final data = await fetchAllData();
      
      print("Customers: ${data['customers']}");
      print("Sports: ${data['customer_sports']}");
      print("Payments: ${data['payments']}");
    } catch (e) {
      print("Error fetching customer data: $e");
    }
  }
}