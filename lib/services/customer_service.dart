import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:hemmaerp/models/customers.dart';
import 'package:hemmaerp/models/customer_sport.dart';
import 'package:hemmaerp/models/sport.dart';

class CustomerService {
  final String baseUrl;
  
  // Constructor that accepts baseUrl parameter
  CustomerService({required this.baseUrl});
  
  // Get all customers
  Future<List<Customer>> getCustomers() async {
    final response = await http.get(Uri.parse('$baseUrl/crud.php?table=customers&action=select'));
    
    if (response.statusCode == 200) {
      List<dynamic> data = json.decode(response.body);
      return data.map((json) => Customer.fromJson(json)).toList();
    } else {
      throw Exception('Failed to load customers: ${response.statusCode}');
    }
  }
  
  // Create new customer
  Future<Customer> createCustomer(Customer customer) async {
    final response = await http.post(
      Uri.parse('$baseUrl/crud.php?table=customers&action=insert'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode(customer.toJson()),
    );
    
    if (response.statusCode == 200) {
      Map<String, dynamic> data = json.decode(response.body);
      // Return customer with new ID
      return Customer.fromJson({...customer.toJson(), 'id': data['id']});
    } else {
      throw Exception('Failed to create customer: ${response.statusCode}');
    }
  }
  
  // Update customer
  Future<Customer> updateCustomer(Customer customer) async {
    final response = await http.post(
      Uri.parse('$baseUrl/crud.php?table=customers&action=update'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode(customer.toJson()),
    );
    
    if (response.statusCode == 200) {
      return customer;
    } else {
      throw Exception('Failed to update customer: ${response.statusCode}');
    }
  }
  
  // Delete customer
  Future<void> deleteCustomer(int id) async {
    final response = await http.post(
      Uri.parse('$baseUrl/crud.php?table=customers&action=delete'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode({'id': id}),
    );
    
    if (response.statusCode != 200) {
      throw Exception('Failed to delete customer: ${response.statusCode}');
    }
  }
  
  // Get all sports
  Future<List<Sport>> getSports() async {
    final response = await http.get(Uri.parse('$baseUrl?table=sports&action=select'));
    
    if (response.statusCode == 200) {
      List<dynamic> data = json.decode(response.body);
      return data.map((json) => Sport.fromJson(json)).toList();
    } else {
      throw Exception('Failed to load sports: ${response.statusCode}');
    }
  }
  
  // Create customer sport relationship
  Future<CustomerSport> createCustomerSport(CustomerSport customerSport) async {
    final response = await http.post(
      Uri.parse('$baseUrl?table=customer_sports&action=insert'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode(customerSport.toJson()),
    );
    
    if (response.statusCode == 200) {
      Map<String, dynamic> data = json.decode(response.body);
      if (data.containsKey('id')) {
        // Return customer sport with new ID
        return CustomerSport(
          id: data['id'],
          customerId: customerSport.customerId,
          sportId: customerSport.sportId,  // Add this parameter
          sportName: customerSport.sportName,
          sportLevel: customerSport.sportLevel,
          trainer: customerSport.trainer,
          subscriptionType: customerSport.subscriptionType,
          subscriptionDuration: customerSport.subscriptionDuration,
          numberOfSessions: customerSport.numberOfSessions,
          fees: customerSport.fees,
          uniformIncluded: customerSport.uniformIncluded,
          uniformPrice: customerSport.uniformPrice,
          trainingTime: customerSport.trainingTime,  // Add this parameter
          trainingDays: customerSport.trainingDays,  // Add this parameter
        );
      } else {
        throw Exception('Failed to create customer sport: No ID returned');
      }
    } else {
      throw Exception('Failed to create customer sport: ${response.statusCode}');
    }
  }
  
  // Get customer sports
  Future<List<CustomerSport>> getCustomerSports(int customerId) async {
    final response = await http.get(
      Uri.parse('$baseUrl?table=customer_sports&action=select&customerId=$customerId'),
    );
    
    if (response.statusCode == 200) {
      List<dynamic> data = json.decode(response.body);
      return data.map((json) => CustomerSport.fromJson(json)).toList();
    } else {
      throw Exception('Failed to load customer sports: ${response.statusCode}');
    }
  }
  
  // Delete customer sport
  Future<void> deleteCustomerSport(int id) async {
    final response = await http.post(
      Uri.parse('$baseUrl?table=customer_sports&action=delete'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode({'id': id}),
    );
    
    if (response.statusCode != 200) {
      throw Exception('Failed to delete customer sport: ${response.statusCode}');
    }
  }
}