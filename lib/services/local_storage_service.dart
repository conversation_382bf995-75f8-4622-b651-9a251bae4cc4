import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

class LocalStorageService {
  Future<void> saveCustomer(Map<String, dynamic> customerData) async {
    final prefs = await SharedPreferences.getInstance();
    
    final String? offlineCustomersJson = prefs.getString('offline_customers');
    List<Map<String, dynamic>> offlineCustomers = [];
    
    if (offlineCustomersJson != null) {
      final List<dynamic> decoded = json.decode(offlineCustomersJson);
      offlineCustomers = decoded.cast<Map<String, dynamic>>();
    }
    
    final int index = offlineCustomers.indexWhere((c) => c['id'] == customerData['id']);
    if (index >= 0) {
      offlineCustomers[index] = customerData;
    } else {
      offlineCustomers.add(customerData);
    }
    
    await prefs.setString('offline_customers', json.encode(offlineCustomers));
  }

  Future<void> saveSports(int customerId, List<Map<String, dynamic>> sportsData) async {
    final prefs = await SharedPreferences.getInstance();
    final key = 'offline_sports_$customerId';
    await prefs.setString(key, json.encode(sportsData));
  }
}