import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import '../models/report.dart';

class ReportService {
  final String baseUrl;
  
  ReportService({required this.baseUrl});
  
  // Get all reports
  Future<List<Report>> getReports() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/reports'),
        headers: {'Content-Type': 'application/json'},
      );
      
      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body)['data'];
        return data.map((json) => Report.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load reports: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching reports: $e');
      return [];
    }
  }
  
  // Get report by ID
  Future<Report?> getReportById(String id) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/reports/$id'),
        headers: {'Content-Type': 'application/json'},
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body)['data'];
        return Report.fromJson(data);
      } else {
        throw Exception('Failed to load report: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching report $id: $e');
      return null;
    }
  }
  
  // Generate financial report
  Future<Report?> generateFinancialReport({
    required String title,
    required DateTime periodStart,
    required DateTime periodEnd,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/reports/financial'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'title': title,
          'periodStart': periodStart.toIso8601String(),
          'periodEnd': periodEnd.toIso8601String(),
        }),
      );
      
      if (response.statusCode == 201) {
        final data = json.decode(response.body)['data'];
        return Report.fromJson(data);
      } else {
        throw Exception('Failed to generate financial report: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error generating financial report: $e');
      return null;
    }
  }
  
  // Generate attendance report
  Future<Report?> generateAttendanceReport({
    required String title,
    required DateTime periodStart,
    required DateTime periodEnd,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/reports/attendance'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'title': title,
          'periodStart': periodStart.toIso8601String(),
          'periodEnd': periodEnd.toIso8601String(),
        }),
      );
      
      if (response.statusCode == 201) {
        final data = json.decode(response.body)['data'];
        return Report.fromJson(data);
      } else {
        throw Exception('Failed to generate attendance report: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error generating attendance report: $e');
      return null;
    }
  }
  
  // Download report
  Future<String?> downloadReport(String id) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/reports/$id/download'),
        headers: {'Content-Type': 'application/json'},
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['downloadUrl'];
      } else {
        throw Exception('Failed to download report: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error downloading report $id: $e');
      return null;
    }
  }
}