// ignore_for_file: unused_import, unused_element

import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import '../utils/validators.dart';
import '../utils/security_utils.dart';
import '../models/customers.dart';

/// Secure API service with authentication, validation, and error handling
class SecureApiService {
  final String baseUrl;
  final Duration timeout;
  String? _authToken;
  String? _refreshToken;

  SecureApiService({
    required this.baseUrl,
    this.timeout = const Duration(seconds: 30),
  });

  /// Set authentication tokens
  void setAuthTokens(String authToken, String refreshToken) {
    _authToken = authToken;
    _refreshToken = refreshToken;
  }

  /// Get secure headers with authentication
  Map<String, String> get _secureHeaders {
    final headers = <String, String>{
      'Content-Type': 'application/json; charset=utf-8',
      'Accept': 'application/json',
      'User-Agent': 'HemmaERP-Flutter/1.0.0',
      'X-Requested-With': 'XMLHttpRequest',
    };

    if (_authToken != null) {
      headers['Authorization'] = 'Bearer $_authToken';
    }

    return headers;
  }

  /// Secure GET request with validation
  Future<Map<String, dynamic>> secureGet(
    String endpoint, {
    Map<String, String>? queryParams,
  }) async {
    try {
      // Validate endpoint
      if (!_isValidEndpoint(endpoint)) {
        throw SecurityException('Invalid endpoint format');
      }

      final uri = Uri.parse('$baseUrl$endpoint');
      final secureUri =
          queryParams != null
              ? uri.replace(queryParameters: _sanitizeQueryParams(queryParams))
              : uri;

      final response = await http
          .get(secureUri, headers: _secureHeaders)
          .timeout(timeout);

      return _handleResponse(response);
    } catch (e) {
      _logSecurityEvent('GET request failed', endpoint, e);
      rethrow;
    }
  }

  /// Secure POST request with validation
  Future<Map<String, dynamic>> securePost(
    String endpoint,
    Map<String, dynamic> data,
  ) async {
    try {
      // Validate endpoint
      if (!_isValidEndpoint(endpoint)) {
        throw SecurityException('Invalid endpoint format');
      }

      // Validate and sanitize data
      final sanitizedData = _sanitizeRequestData(data);

      final response = await http
          .post(
            Uri.parse('$baseUrl$endpoint'),
            headers: _secureHeaders,
            body: jsonEncode(sanitizedData),
          )
          .timeout(timeout);

      return _handleResponse(response);
    } catch (e) {
      _logSecurityEvent('POST request failed', endpoint, e);
      rethrow;
    }
  }

  /// Secure PUT request with validation
  Future<Map<String, dynamic>> securePut(
    String endpoint,
    Map<String, dynamic> data,
  ) async {
    try {
      if (!_isValidEndpoint(endpoint)) {
        throw SecurityException('Invalid endpoint format');
      }

      final sanitizedData = _sanitizeRequestData(data);

      final response = await http
          .put(
            Uri.parse('$baseUrl$endpoint'),
            headers: _secureHeaders,
            body: jsonEncode(sanitizedData),
          )
          .timeout(timeout);

      return _handleResponse(response);
    } catch (e) {
      _logSecurityEvent('PUT request failed', endpoint, e);
      rethrow;
    }
  }

  /// Handle HTTP response securely
  Map<String, dynamic> _handleResponse(http.Response response) {
    // Check for authentication errors
    if (response.statusCode == 401) {
      _handleAuthenticationError();
      throw AuthenticationException('Authentication failed');
    }

    if (response.statusCode == 403) {
      throw AuthorizationException('Access denied');
    }

    if (response.statusCode < 200 || response.statusCode >= 300) {
      throw ApiException(
        'HTTP ${response.statusCode}: ${response.reasonPhrase}',
        response.statusCode,
      );
    }

    try {
      final data = jsonDecode(response.body) as Map<String, dynamic>;
      return data;
    } catch (e) {
      throw ApiException('Invalid JSON response', response.statusCode);
    }
  }

  /// Validate endpoint format
  bool _isValidEndpoint(String endpoint) {
    // Check for path traversal attempts
    if (endpoint.contains('..') || endpoint.contains('//')) {
      return false;
    }

    // Check for valid endpoint pattern
    final endpointRegex = RegExp(r'^/[a-zA-Z0-9/_\-\.]+$');
    return endpointRegex.hasMatch(endpoint);
  }

  /// Sanitize query parameters
  Map<String, String> _sanitizeQueryParams(Map<String, String> params) {
    final sanitized = <String, String>{};

    for (final entry in params.entries) {
      final key = CustomerValidators.sanitizeInput(entry.key);
      final value = CustomerValidators.sanitizeInput(entry.value);

      // Validate query parameter
      if (CustomerValidators.validateSearchQuery(value) == null) {
        sanitized[key] = value;
      }
    }

    return sanitized;
  }

  /// Sanitize request data
  Map<String, dynamic> _sanitizeRequestData(Map<String, dynamic> data) {
    final sanitized = <String, dynamic>{};

    for (final entry in data.entries) {
      final key = entry.key;
      final value = entry.value;

      if (value is String) {
        sanitized[key] = CustomerValidators.sanitizeInput(value);
      } else if (value is num || value is bool) {
        sanitized[key] = value;
      } else if (value is List || value is Map) {
        // For complex types, convert to JSON and back to ensure safety
        try {
          sanitized[key] = jsonDecode(jsonEncode(value));
        } catch (e) {
          // Skip invalid data
          continue;
        }
      }
    }

    return sanitized;
  }

  /// Handle authentication errors
  void _handleAuthenticationError() {
    // Clear tokens
    _authToken = null;
    _refreshToken = null;

    // Trigger re-authentication flow
    // This would typically navigate to login screen
  }

  /// Log security events (without sensitive data)
  void _logSecurityEvent(String event, String endpoint, dynamic error) {
    if (kDebugMode) {
      print('Security Event: $event');
      print('Endpoint: ${SecurityUtils.maskSensitiveData(endpoint)}');
      print('Error: ${error.toString()}');
    }

    // In production, send to security monitoring service
    // _sendToSecurityMonitoring(event, endpoint, error);
  }

  /// Refresh authentication token
  Future<bool> refreshAuthToken() async {
    if (_refreshToken == null) {
      return false;
    }

    try {
      final response = await http
          .post(
            Uri.parse('$baseUrl/auth/refresh'),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $_refreshToken',
            },
            body: jsonEncode({'refresh_token': _refreshToken}),
          )
          .timeout(timeout);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        _authToken = data['access_token'];
        _refreshToken = data['refresh_token'];
        return true;
      }
    } catch (e) {
      _logSecurityEvent('Token refresh failed', '/auth/refresh', e);
    }

    return false;
  }

  /// Validate SSL certificate (for production)
  bool _validateCertificate(X509Certificate cert, String host, int port) {
    // Implement certificate pinning for production
    // For now, return true for development
    return true;
  }
}

/// Custom exception classes for better error handling
class SecurityException implements Exception {
  final String message;
  SecurityException(this.message);

  @override
  String toString() => 'SecurityException: $message';
}

class AuthenticationException implements Exception {
  final String message;
  AuthenticationException(this.message);

  @override
  String toString() => 'AuthenticationException: $message';
}

class AuthorizationException implements Exception {
  final String message;
  AuthorizationException(this.message);

  @override
  String toString() => 'AuthorizationException: $message';
}

class ApiException implements Exception {
  final String message;
  final int statusCode;

  ApiException(this.message, this.statusCode);

  @override
  String toString() => 'ApiException: $message (Status: $statusCode)';
}
