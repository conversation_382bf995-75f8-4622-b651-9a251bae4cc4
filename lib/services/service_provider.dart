import 'package:flutter/material.dart';
import 'account_service.dart';
import 'customer_service.dart';
import 'transaction_service.dart';
import 'report_service.dart';
import 'trainee_service.dart';

class ServiceProvider extends InheritedWidget {
  final String apiBaseUrl;
  
  // Services
  late final AccountService accountService;
  late final CustomerService customerService;
  late final TransactionService transactionService;
  late final ReportService reportService;
  late final TraineeService traineeService;
  
  ServiceProvider({
    super.key,
    required super.child,
    required this.apiBaseUrl,
  }) {
    // Initialize services
    accountService = AccountService(baseUrl: apiBaseUrl);
    customerService = CustomerService(baseUrl: apiBaseUrl);
    transactionService = TransactionService(baseUrl: apiBaseUrl);
    reportService = ReportService(baseUrl: apiBaseUrl);
    traineeService = TraineeService(baseUrl: apiBaseUrl);
  }
  
  static ServiceProvider of(BuildContext context) {
    final ServiceProvider? result = context.dependOnInheritedWidgetOfExactType<ServiceProvider>();
    assert(result != null, 'No ServiceProvider found in context');
    return result!;
  }
  
  @override
  bool updateShouldNotify(ServiceProvider oldWidget) {
    return apiBaseUrl != oldWidget.apiBaseUrl;
  }
}

// Extension method for BuildContext to easily access services
extension ServiceProviderExtension on BuildContext {
  AccountService get accountService => ServiceProvider.of(this).accountService;
  CustomerService get customerService => ServiceProvider.of(this).customerService;
  TransactionService get transactionService => ServiceProvider.of(this).transactionService;
  ReportService get reportService => ServiceProvider.of(this).reportService;
  TraineeService get traineeService => ServiceProvider.of(this).traineeService;
}