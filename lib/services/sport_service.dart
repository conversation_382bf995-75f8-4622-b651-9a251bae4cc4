// ignore_for_file: avoid_print

import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:hemmaerp/models/sport.dart';

class SportService {
  final String baseUrl;

  SportService({required this.baseUrl});

  Future<List<Sport>> getSports() async {
    final response = await http.get(
      Uri.parse('$baseUrl/crud.php?table=sports&action=select'),
    );

    if (response.statusCode == 200) {
      final List<dynamic> data = json.decode(response.body);
      return data.map((json) => Sport.fromJson(json)).toList();
    } else {
      throw Exception('Failed to load sports: ${response.statusCode}');
    }
  }

  Future<Sport> getSportById(int id) async {
    final response = await http.get(
      Uri.parse('$baseUrl/crud.php?table=sports&action=select&id=$id'),
    );

    if (response.statusCode == 200) {
      final List<dynamic> data = json.decode(response.body);
      if (data.isEmpty) {
        throw Exception('Sport not found');
      }
      return Sport.fromJson(data[0]);
    } else {
      throw Exception('Failed to load sport: ${response.statusCode}');
    }
  }

  Future<Sport> createSport(Sport sport) async {
    final response = await http.post(
      Uri.parse('$baseUrl/crud.php?table=sports&action=insert'),
      headers: {'Content-Type': 'application/x-www-form-urlencoded'},
      body: sport.toJson(),
    );

    if (response.statusCode == 200) {
      final Map<String, dynamic> data = json.decode(response.body);
      if (data['id'] != null) {
        return sport.copyWith(id: int.parse(data['id'].toString()));
      }
      throw Exception('Failed to get new sport ID');
    } else {
      throw Exception('Failed to create sport: ${response.statusCode}');
    }
  }

  Future<void> updateSport(Sport sport) async {
    if (sport.id == null) {
      throw Exception('Sport ID cannot be null for update operation');
    }

    final response = await http.post(
      Uri.parse('$baseUrl/crud.php?table=sports&action=update'),
      headers: {'Content-Type': 'application/x-www-form-urlencoded'},
      body: sport.toJson(),
    );

    if (response.statusCode != 200) {
      throw Exception('Failed to update sport: ${response.statusCode}');
    }
  }

  Future<void> deleteSport(int id) async {
    final response = await http.post(
      Uri.parse('$baseUrl/sports/delete_sport.php'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode({'id': id}),
    );

    if (response.statusCode != 200) {
      throw Exception('Failed to delete sport');
    }
  }


  Future<void> refundSport(int id) async {
    final response = await http.post(
      Uri.parse('$baseUrl/sports/refund_sport.php'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode({'id': id}),
    );

    if (response.statusCode != 200) {
      throw Exception('Failed to refund sport');
    }
  }
}