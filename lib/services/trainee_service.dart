import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import '../models/trainee.dart';

class TraineeService {
  final String baseUrl;
  
  TraineeService({required this.baseUrl});
  
  // Get all trainees
  Future<List<Trainee>> getTrainees() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/trainees'),
        headers: {'Content-Type': 'application/json'},
      );
      
      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body)['data'];
        return data.map((json) => Trainee.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load trainees: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching trainees: $e');
      return [];
    }
  }
  
  // Get trainee by ID
  Future<Trainee?> getTraineeById(int id) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/trainees/$id'),
        headers: {'Content-Type': 'application/json'},
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body)['data'];
        return Trainee.fromJson(data);
      } else {
        throw Exception('Failed to load trainee: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching trainee $id: $e');
      return null;
    }
  }
  
  // Create new trainee
  Future<Trainee?> createTrainee(Trainee trainee) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/trainees'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode(trainee.toJson()),
      );
      
      if (response.statusCode == 201) {
        final data = json.decode(response.body)['data'];
        return Trainee.fromJson(data);
      } else {
        throw Exception('Failed to create trainee: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error creating trainee: $e');
      return null;
    }
  }
  
  // Update trainee
  Future<Trainee?> updateTrainee(Trainee trainee) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/trainees/${trainee.id}'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode(trainee.toJson()),
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body)['data'];
        return Trainee.fromJson(data);
      } else {
        throw Exception('Failed to update trainee: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error updating trainee: $e');
      return null;
    }
  }
  
  // Get trainees by trainer ID
  Future<List<Trainee>> getTraineesByTrainerId(int trainerId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/trainees/trainer/$trainerId'),
        headers: {'Content-Type': 'application/json'},
      );
      
      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body)['data'];
        return data.map((json) => Trainee.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load trainer\'s trainees: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching trainer\'s trainees: $e');
      return [];
    }
  }
  
  // Get trainees by training program
  Future<List<Trainee>> getTraineesByProgram(String program) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/trainees/program/$program'),
        headers: {'Content-Type': 'application/json'},
      );
      
      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body)['data'];
        return data.map((json) => Trainee.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load trainees by program: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching trainees by program: $e');
      return [];
    }
  }
}