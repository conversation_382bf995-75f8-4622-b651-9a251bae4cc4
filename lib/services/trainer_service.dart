import 'dart:convert';
import 'package:http/http.dart' as http;

class TrainerService {
  static const String baseUrl = 'https://backend2.hemmasportacademy.com';
  
  Future<List<Map<String, dynamic>>> getTrainers() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/trainers/get_trainers.php'),
      );
      
      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(json.decode(response.body));
      } else {
        throw Exception('Failed to load trainers: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching trainers: $e');
    }
  }
}