import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import '../models/transactions.dart';

class TransactionService {
  final String baseUrl;
  
  TransactionService({required this.baseUrl});
  
  // Get all transactions
  Future<List<Transaction>> getTransactions({int page = 1, int limit = 20}) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/transactions?page=$page&limit=$limit'),
        headers: {'Content-Type': 'application/json'},
      );
      
      if (response.statusCode == 200) {
        final jsonResponse = json.decode(response.body);
        return parseTransactionsResponse(jsonResponse);
      } else {
        throw Exception('Failed to load transactions: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching transactions: $e');
      return [];
    }
  }
  
  // Get transaction by ID
  Future<Transaction?> getTransactionById(int id) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/transactions/$id'),
        headers: {'Content-Type': 'application/json'},
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body)['data'];
        return Transaction.fromJson(data);
      } else {
        throw Exception('Failed to load transaction: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching transaction $id: $e');
      return null;
    }
  }
  
  // Create new transaction
  Future<Transaction?> createTransaction(Transaction transaction) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/transactions'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode(transaction.toJson()),
      );
      
      if (response.statusCode == 201) {
        final data = json.decode(response.body)['data'];
        return Transaction.fromJson(data);
      } else {
        throw Exception('Failed to create transaction: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error creating transaction: $e');
      return null;
    }
  }
  
  // Get transactions by customer ID
  Future<List<Transaction>> getTransactionsByCustomerId(int customerId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/transactions/customer/$customerId'),
        headers: {'Content-Type': 'application/json'},
      );
      
      if (response.statusCode == 200) {
        final jsonResponse = json.decode(response.body);
        return parseTransactionsResponse(jsonResponse);
      } else {
        throw Exception('Failed to load customer transactions: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching customer transactions: $e');
      return [];
    }
  }
  
  // Get transactions by type
  Future<List<Transaction>> getTransactionsByType(String type) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/transactions/type/$type'),
        headers: {'Content-Type': 'application/json'},
      );
      
      if (response.statusCode == 200) {
        final jsonResponse = json.decode(response.body);
        return parseTransactionsResponse(jsonResponse);
      } else {
        throw Exception('Failed to load transactions by type: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching transactions by type: $e');
      return [];
    }
  }
}