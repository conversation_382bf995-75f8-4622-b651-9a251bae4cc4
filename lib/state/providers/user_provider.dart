import 'package:flutter_riverpod/flutter_riverpod.dart';

final userProvider = StateNotifierProvider<UserNotifier, UserState>((ref) {
  return UserNotifier();
});

class UserState {
  bool isLoading;
  String? name;
  String? email;
  String? avatarUrl;
  String? error;

  UserState({
    this.isLoading = false,
    this.name,
    this.email,
    this.avatarUrl,
    this.error,
  });
}

class UserNotifier extends StateNotifier<UserState> {
  UserNotifier() : super(UserState());

  Future<void> loadProfile() async {
    state = UserState(isLoading: true);
    // Add profile loading logic here
  }
}