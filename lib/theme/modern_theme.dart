import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class ModernTheme {
  // Modern Color Palette
  static const Color primaryBlue = Color(0xFF2563EB);
  static const Color primaryBlueDark = Color(0xFF1D4ED8);
  static const Color secondaryTeal = Color(0xFF0D9488);
  static const Color accentOrange = Color(0xFFF59E0B);

  // Semantic Colors
  static const Color successGreen = Color(0xFF10B981);
  static const Color warningAmber = Color(0xFFF59E0B);
  static const Color errorRed = Color(0xFFEF4444);
  static const Color infoBlue = Color(0xFF3B82F6);

  // Neutral Colors
  static const Color neutral50 = Color(0xFFFAFAFA);
  static const Color neutral100 = Color(0xFFF5F5F5);
  static const Color neutral200 = Color(0xFFE5E5E5);
  static const Color neutral300 = Color(0xFFD4D4D4);
  static const Color neutral400 = Color(0xFFA3A3A3);
  static const Color neutral500 = Color(0xFF737373);
  static const Color neutral600 = Color(0xFF525252);
  static const Color neutral700 = Color(0xFF404040);
  static const Color neutral800 = Color(0xFF262626);
  static const Color neutral900 = Color(0xFF171717);

  // Light Theme
  static ThemeData lightTheme = ThemeData(
    useMaterial3: true,
    brightness: Brightness.light,
    colorScheme: const ColorScheme.light(
      primary: primaryBlue,
      onPrimary: Colors.white,
      primaryContainer: Color(0xFFDEEAFF),
      onPrimaryContainer: Color(0xFF001D36),
      secondary: secondaryTeal,
      onSecondary: Colors.white,
      secondaryContainer: Color(0xFFA7F3D0),
      onSecondaryContainer: Color(0xFF002114),
      tertiary: accentOrange,
      onTertiary: Colors.white,
      tertiaryContainer: Color(0xFFFFE4B5),
      onTertiaryContainer: Color(0xFF2D1B00),
      error: errorRed,
      onError: Colors.white,
      errorContainer: Color(0xFFFFDAD6),
      onErrorContainer: Color(0xFF410002),
      background: neutral50,
      onBackground: neutral900,
      surface: Colors.white,
      onSurface: neutral900,
      surfaceVariant: neutral100,
      onSurfaceVariant: neutral700,
      outline: neutral300,
      outlineVariant: neutral200,
      shadow: Colors.black26,
      scrim: Colors.black54,
      inverseSurface: neutral800,
      onInverseSurface: neutral100,
      inversePrimary: Color(0xFF9CC5FF),
    ),

    // App Bar Theme
    appBarTheme: const AppBarTheme(
      elevation: 0,
      scrolledUnderElevation: 1,
      backgroundColor: Colors.white,
      foregroundColor: neutral900,
      surfaceTintColor: primaryBlue,
      titleTextStyle: TextStyle(
        color: neutral900,
        fontSize: 20,
        fontWeight: FontWeight.w600,
        letterSpacing: -0.5,
      ),
      systemOverlayStyle: SystemUiOverlayStyle.dark,
    ),

    // Card Theme
    cardTheme: CardTheme(
      elevation: 2,
      shadowColor: Colors.black12,
      surfaceTintColor: primaryBlue,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
    ),

    // Elevated Button Theme
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        elevation: 2,
        shadowColor: Colors.black26,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        textStyle: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w600,
          letterSpacing: 0.5,
        ),
      ),
    ),

    // Outlined Button Theme
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        side: const BorderSide(color: neutral300),
        textStyle: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w600,
          letterSpacing: 0.5,
        ),
      ),
    ),

    // Text Button Theme
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        textStyle: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w600,
          letterSpacing: 0.5,
        ),
      ),
    ),

    // Input Decoration Theme
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: neutral50,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: neutral300),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: neutral300),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: primaryBlue, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: errorRed),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: errorRed, width: 2),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      labelStyle: const TextStyle(
        color: neutral600,
        fontSize: 14,
        fontWeight: FontWeight.w500,
      ),
      hintStyle: const TextStyle(color: neutral400, fontSize: 14),
      errorStyle: const TextStyle(
        color: errorRed,
        fontSize: 12,
        fontWeight: FontWeight.w500,
      ),
    ),

    // Chip Theme
    chipTheme: ChipThemeData(
      backgroundColor: neutral100,
      selectedColor: primaryBlue.withOpacity(0.12),
      secondarySelectedColor: secondaryTeal.withOpacity(0.12),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      labelStyle: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
      secondaryLabelStyle: const TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w500,
        color: Colors.white,
      ),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      elevation: 0,
      pressElevation: 1,
    ),

    // Data Table Theme
    dataTableTheme: DataTableThemeData(
      headingRowColor: MaterialStateProperty.all(neutral50),
      dataRowColor: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.selected)) {
          return primaryBlue.withOpacity(0.08);
        }
        return null;
      }),
      headingTextStyle: const TextStyle(
        color: neutral700,
        fontSize: 12,
        fontWeight: FontWeight.w600,
        letterSpacing: 0.5,
      ),
      dataTextStyle: const TextStyle(
        color: neutral800,
        fontSize: 14,
        fontWeight: FontWeight.w400,
      ),
      horizontalMargin: 16,
      columnSpacing: 24,
      dividerThickness: 1,
    ),

    // Floating Action Button Theme
    floatingActionButtonTheme: const FloatingActionButtonThemeData(
      elevation: 4,
      highlightElevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.all(Radius.circular(16)),
      ),
    ),

    // Bottom Navigation Bar Theme
    bottomNavigationBarTheme: const BottomNavigationBarThemeData(
      elevation: 8,
      backgroundColor: Colors.white,
      selectedItemColor: primaryBlue,
      unselectedItemColor: neutral400,
      type: BottomNavigationBarType.fixed,
      selectedLabelStyle: TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
      unselectedLabelStyle: TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w400,
      ),
    ),

    // Typography
    textTheme: const TextTheme(
      displayLarge: TextStyle(
        fontSize: 57,
        fontWeight: FontWeight.w400,
        letterSpacing: -0.25,
        color: neutral900,
      ),
      displayMedium: TextStyle(
        fontSize: 45,
        fontWeight: FontWeight.w400,
        letterSpacing: 0,
        color: neutral900,
      ),
      displaySmall: TextStyle(
        fontSize: 36,
        fontWeight: FontWeight.w400,
        letterSpacing: 0,
        color: neutral900,
      ),
      headlineLarge: TextStyle(
        fontSize: 32,
        fontWeight: FontWeight.w600,
        letterSpacing: 0,
        color: neutral900,
      ),
      headlineMedium: TextStyle(
        fontSize: 28,
        fontWeight: FontWeight.w600,
        letterSpacing: 0,
        color: neutral900,
      ),
      headlineSmall: TextStyle(
        fontSize: 24,
        fontWeight: FontWeight.w600,
        letterSpacing: 0,
        color: neutral900,
      ),
      titleLarge: TextStyle(
        fontSize: 22,
        fontWeight: FontWeight.w600,
        letterSpacing: 0,
        color: neutral900,
      ),
      titleMedium: TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w600,
        letterSpacing: 0.15,
        color: neutral900,
      ),
      titleSmall: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w600,
        letterSpacing: 0.1,
        color: neutral900,
      ),
      bodyLarge: TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w400,
        letterSpacing: 0.5,
        color: neutral800,
      ),
      bodyMedium: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w400,
        letterSpacing: 0.25,
        color: neutral800,
      ),
      bodySmall: TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w400,
        letterSpacing: 0.4,
        color: neutral600,
      ),
      labelLarge: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w600,
        letterSpacing: 0.1,
        color: neutral700,
      ),
      labelMedium: TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w600,
        letterSpacing: 0.5,
        color: neutral700,
      ),
      labelSmall: TextStyle(
        fontSize: 11,
        fontWeight: FontWeight.w600,
        letterSpacing: 0.5,
        color: neutral600,
      ),
    ),
  );

  // Dark Theme - Enhanced for professional appearance
  static ThemeData darkTheme = ThemeData(
    useMaterial3: true,
    brightness: Brightness.dark,
    colorScheme: const ColorScheme.dark(
      // Primary colors - Original dark blue
      primary: Color(0xFF2563EB), // Original dark blue
      onPrimary: Color(0xFFFFFFFF), // White text on primary
      primaryContainer: Color(0xFF1E40AF), // Darker blue container
      onPrimaryContainer: Color(0xFFDEEAFF), // Light text on container
      // Secondary colors - Complementary blue tones
      secondary: Color(0xFF3B82F6), // Lighter blue
      onSecondary: Color(0xFFFFFFFF), // White text on secondary
      secondaryContainer: Color(0xFF1E3A8A), // Dark blue container
      onSecondaryContainer: Color(0xFFDBEAFE), // Light blue text
      // Tertiary colors - Accent blue
      tertiary: Color(0xFF60A5FA), // Light blue accent
      onTertiary: Color(0xFF1E3A8A), // Dark blue for text
      tertiaryContainer: Color(0xFF1E40AF), // Blue container
      onTertiaryContainer: Color(0xFFDBEAFE), // Light blue text
      // Error colors - Professional red
      error: Color(0xFFEF4444), // Red
      onError: Color(0xFFFFFFFF), // White text on red
      errorContainer: Color(0xFFDC2626), // Dark red container
      onErrorContainer: Color(0xFFFEE2E2), // Light red text
      // Background and surface colors - Dark blue theme
      background: Color(0xFF0F172A), // Very dark blue
      onBackground: Color(0xFFF1F5F9), // Light text
      surface: Color(0xFF1E293B), // Dark blue surface
      onSurface: Color(0xFFF1F5F9), // Light text on surface
      surfaceVariant: Color(0xFF334155), // Blue-gray surface variant
      onSurfaceVariant: Color(0xFFCBD5E1), // Light blue-gray text
      // Outline and border colors
      outline: Color(0xFF475569), // Blue-gray borders
      outlineVariant: Color(0xFF334155), // Subtle blue-gray borders
      // Shadow and overlay colors
      shadow: Color(0xFF000000), // Pure black for shadows
      scrim: Color(0xFF000000), // Pure black for scrims
      // Inverse colors for contrast
      inverseSurface: Color(0xFFF1F5F9), // Light surface for contrast
      onInverseSurface: Color(0xFF1E293B), // Dark text on light surface
      inversePrimary: Color(0xFF2563EB), // Original primary for inverse
    ),

    // Dark App Bar Theme - Original Dark Blue
    appBarTheme: const AppBarTheme(
      elevation: 0,
      scrolledUnderElevation: 2,
      backgroundColor: Color(0xFF1E293B), // Match surface color
      foregroundColor: Color(0xFFF1F5F9), // Match onSurface color
      surfaceTintColor: Color(0xFF2563EB), // Match primary color
      titleTextStyle: TextStyle(
        color: Color(0xFFF1F5F9),
        fontSize: 20,
        fontWeight: FontWeight.w600,
        letterSpacing: -0.5,
      ),
      iconTheme: IconThemeData(color: Color(0xFFF1F5F9), size: 24),
      actionsIconTheme: IconThemeData(color: Color(0xFFF1F5F9), size: 24),
      systemOverlayStyle: SystemUiOverlayStyle.light,
    ),

    // Dark Card Theme - Original Dark Blue
    cardTheme: CardTheme(
      elevation: 2,
      shadowColor: Colors.black.withOpacity(0.3),
      surfaceTintColor: Color(0xFF2563EB), // Match primary
      color: Color(0xFF1E293B), // Match surface
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      clipBehavior: Clip.antiAlias,
    ),

    // Dark Input Decoration Theme - Enhanced
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: Color(0xFF334155), // Match surfaceVariant
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: Color(0xFF475569)), // Match outline
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: Color(0xFF475569)),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(
          color: Color(0xFF2563EB),
          width: 2,
        ), // Match primary
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: Color(0xFFEF4444)), // Match error
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: Color(0xFFEF4444), width: 2),
      ),
      disabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(
          color: Color(0xFF334155),
        ), // Match outlineVariant
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
      labelStyle: const TextStyle(
        color: Color(0xFFCBD5E1), // Match onSurfaceVariant
        fontSize: 14,
        fontWeight: FontWeight.w500,
      ),
      hintStyle: const TextStyle(color: Color(0xFFCBD5E1), fontSize: 14),
      errorStyle: const TextStyle(
        color: Color(0xFFEF4444),
        fontSize: 12,
        fontWeight: FontWeight.w500,
      ),
      helperStyle: const TextStyle(color: Color(0xFFCBD5E1), fontSize: 12),
    ),

    // Dark Data Table Theme - Enhanced
    dataTableTheme: DataTableThemeData(
      headingRowColor: MaterialStateProperty.all(
        Color(0xFF334155),
      ), // Match surfaceVariant
      dataRowColor: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.selected)) {
          return Color(
            0xFF2563EB,
          ).withOpacity(0.15); // Match primary with opacity
        }
        if (states.contains(MaterialState.hovered)) {
          return Color(0xFF334155).withOpacity(0.5);
        }
        return null;
      }),
      headingTextStyle: const TextStyle(
        color: Color(0xFFCBD5E1), // Match onSurfaceVariant
        fontSize: 12,
        fontWeight: FontWeight.w600,
        letterSpacing: 0.5,
      ),
      dataTextStyle: const TextStyle(
        color: Color(0xFFF1F5F9), // Match onSurface
        fontSize: 14,
        fontWeight: FontWeight.w400,
      ),
      horizontalMargin: 16,
      columnSpacing: 24,
      dividerThickness: 1,
      decoration: BoxDecoration(
        color: Color(0xFF1E293B), // Match surface
        borderRadius: BorderRadius.circular(12),
      ),
    ),

    // Dark Bottom Navigation Bar Theme - Enhanced
    bottomNavigationBarTheme: const BottomNavigationBarThemeData(
      elevation: 8,
      backgroundColor: Color(0xFF1E293B), // Match surface
      selectedItemColor: Color(0xFF2563EB), // Match primary
      unselectedItemColor: Color(0xFFCBD5E1), // Match onSurfaceVariant
      type: BottomNavigationBarType.fixed,
      selectedLabelStyle: TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w600,
        letterSpacing: 0.5,
      ),
      unselectedLabelStyle: TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w400,
        letterSpacing: 0.5,
      ),
      selectedIconTheme: IconThemeData(color: Color(0xFF2563EB), size: 24),
      unselectedIconTheme: IconThemeData(color: Color(0xFFCBD5E1), size: 24),
    ),

    // Dark Typography - Enhanced with new color scheme
    textTheme: const TextTheme(
      displayLarge: TextStyle(
        fontSize: 57,
        fontWeight: FontWeight.w400,
        letterSpacing: -0.25,
        color: Color(0xFFF1F5F9), // Match onSurface
      ),
      displayMedium: TextStyle(
        fontSize: 45,
        fontWeight: FontWeight.w400,
        letterSpacing: 0,
        color: Color(0xFFF1F5F9),
      ),
      displaySmall: TextStyle(
        fontSize: 36,
        fontWeight: FontWeight.w400,
        letterSpacing: 0,
        color: Color(0xFFF1F5F9),
      ),
      headlineLarge: TextStyle(
        fontSize: 32,
        fontWeight: FontWeight.w600,
        letterSpacing: 0,
        color: Color(0xFFF1F5F9),
      ),
      headlineMedium: TextStyle(
        fontSize: 28,
        fontWeight: FontWeight.w600,
        letterSpacing: 0,
        color: Color(0xFFF1F5F9),
      ),
      headlineSmall: TextStyle(
        fontSize: 24,
        fontWeight: FontWeight.w600,
        letterSpacing: 0,
        color: Color(0xFFF1F5F9),
      ),
      titleLarge: TextStyle(
        fontSize: 22,
        fontWeight: FontWeight.w600,
        letterSpacing: 0,
        color: Color(0xFFF1F5F9),
      ),
      titleMedium: TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w600,
        letterSpacing: 0.15,
        color: Color(0xFFF1F5F9),
      ),
      titleSmall: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w600,
        letterSpacing: 0.1,
        color: Color(0xFFF1F5F9),
      ),
      bodyLarge: TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w400,
        letterSpacing: 0.5,
        color: Color(0xFFE2E8F0), // Slightly muted for body text
      ),
      bodyMedium: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w400,
        letterSpacing: 0.25,
        color: Color(0xFFE2E8F0),
      ),
      bodySmall: TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w400,
        letterSpacing: 0.4,
        color: Color(0xFFCBD5E1), // Match onSurfaceVariant
      ),
      labelLarge: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w600,
        letterSpacing: 0.1,
        color: Color(0xFFE2E8F0),
      ),
      labelMedium: TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w600,
        letterSpacing: 0.5,
        color: Color(0xFFE2E8F0),
      ),
      labelSmall: TextStyle(
        fontSize: 11,
        fontWeight: FontWeight.w600,
        letterSpacing: 0.5,
        color: Color(0xFFCBD5E1),
      ),
    ),

    // Enhanced Button Themes for Dark Mode
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: Color(0xFF2563EB), // Match primary
        foregroundColor: Color(0xFFFFFFFF), // Match onPrimary
        disabledBackgroundColor: Color(0xFF334155), // Match surfaceVariant
        disabledForegroundColor: Color(0xFFCBD5E1), // Match onSurfaceVariant
        shadowColor: Colors.black.withOpacity(0.3),
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        textStyle: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w600,
          letterSpacing: 0.5,
        ),
      ),
    ),

    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: Color(0xFF2563EB), // Match primary
        disabledForegroundColor: Color(0xFFCBD5E1), // Match onSurfaceVariant
        side: const BorderSide(color: Color(0xFF2563EB), width: 1.5),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        textStyle: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w600,
          letterSpacing: 0.5,
        ),
      ),
    ),

    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: Color(0xFF2563EB), // Match primary
        disabledForegroundColor: Color(0xFFCBD5E1), // Match onSurfaceVariant
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        textStyle: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w600,
          letterSpacing: 0.5,
        ),
      ),
    ),

    // Enhanced Divider Theme
    dividerTheme: const DividerThemeData(
      color: Color(0xFF334155), // Match outlineVariant
      thickness: 1,
      space: 16,
    ),

    // Enhanced Tab Bar Theme
    tabBarTheme: const TabBarTheme(
      labelColor: Color(0xFF2563EB), // Match primary
      unselectedLabelColor: Color(0xFFCBD5E1), // Match onSurfaceVariant
      indicatorColor: Color(0xFF2563EB),
      indicatorSize: TabBarIndicatorSize.label,
      labelStyle: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w600,
        letterSpacing: 0.5,
      ),
      unselectedLabelStyle: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w400,
        letterSpacing: 0.5,
      ),
    ),

    // Enhanced Checkbox Theme
    checkboxTheme: CheckboxThemeData(
      fillColor: MaterialStateProperty.resolveWith<Color>((states) {
        if (states.contains(MaterialState.selected)) {
          return Color(0xFF2563EB); // Match primary
        }
        return Colors.transparent;
      }),
      checkColor: MaterialStateProperty.all(
        Color(0xFFFFFFFF),
      ), // Match onPrimary
      overlayColor: MaterialStateProperty.all(
        Color(0xFF2563EB).withOpacity(0.1),
      ),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
    ),

    // Enhanced Radio Theme
    radioTheme: RadioThemeData(
      fillColor: MaterialStateProperty.resolveWith<Color>((states) {
        if (states.contains(MaterialState.selected)) {
          return Color(0xFF2563EB); // Match primary
        }
        return Color(0xFF475569); // Match outline
      }),
      overlayColor: MaterialStateProperty.all(
        Color(0xFF2563EB).withOpacity(0.1),
      ),
    ),

    // Enhanced Switch Theme
    switchTheme: SwitchThemeData(
      thumbColor: MaterialStateProperty.resolveWith<Color>((states) {
        if (states.contains(MaterialState.selected)) {
          return Color(0xFF2563EB); // Match primary
        }
        return Color(0xFFCBD5E1); // Match onSurfaceVariant
      }),
      trackColor: MaterialStateProperty.resolveWith<Color>((states) {
        if (states.contains(MaterialState.selected)) {
          return Color(0xFF2563EB).withOpacity(0.5);
        }
        return Color(0xFF475569); // Match outline
      }),
    ),

    // Enhanced Slider Theme
    sliderTheme: SliderThemeData(
      activeTrackColor: Color(0xFF2563EB), // Match primary
      inactiveTrackColor: Color(0xFF475569), // Match outline
      thumbColor: Color(0xFF2563EB),
      overlayColor: Color(0xFF2563EB).withOpacity(0.1),
      valueIndicatorColor: Color(0xFF2563EB),
      valueIndicatorTextStyle: const TextStyle(
        color: Color(0xFFFFFFFF), // Match onPrimary
        fontSize: 12,
        fontWeight: FontWeight.w600,
      ),
    ),
  );

  // Animation Durations
  static const Duration fastAnimation = Duration(milliseconds: 150);
  static const Duration normalAnimation = Duration(milliseconds: 300);
  static const Duration slowAnimation = Duration(milliseconds: 500);

  // Spacing System
  static const double spacing2xs = 2.0;
  static const double spacingXs = 4.0;
  static const double spacingSm = 8.0;
  static const double spacingMd = 16.0;
  static const double spacingLg = 24.0;
  static const double spacingXl = 32.0;
  static const double spacing2xl = 48.0;
  static const double spacing3xl = 64.0;

  // Border Radius System
  static const double radiusXs = 4.0;
  static const double radiusSm = 6.0;
  static const double radiusMd = 8.0;
  static const double radiusLg = 12.0;
  static const double radiusXl = 16.0;
  static const double radius2xl = 24.0;
  static const double radiusFull = 9999.0;

  // Elevation System
  static const double elevationNone = 0.0;
  static const double elevationSm = 1.0;
  static const double elevationMd = 2.0;
  static const double elevationLg = 4.0;
  static const double elevationXl = 8.0;
  static const double elevation2xl = 16.0;
}
