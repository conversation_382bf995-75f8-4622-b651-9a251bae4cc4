import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;

/// Utility class for handling API requests and responses
class ApiUtils {
  /// Handles HTTP response and returns parsed data or throws an exception
  static dynamic handleResponse(http.Response response) {
    if (response.statusCode >= 200 && response.statusCode < 300) {
      try {
        return json.decode(response.body);
      } catch (e) {
        debugPrint('Error parsing response: $e');
        throw Exception('Failed to parse response');
      }
    } else {
      final message = _getErrorMessage(response);
      throw Exception(message);
    }
  }

  /// Extracts error message from response
  static String _getErrorMessage(http.Response response) {
    try {
      final data = json.decode(response.body);
      return data['message'] ?? data['error'] ?? 'Error ${response.statusCode}';
    } catch (e) {
      return 'Error ${response.statusCode}: ${response.reasonPhrase}';
    }
  }

  /// Creates standard headers for API requests
  static Map<String, String> getHeaders({String? token}) {
    final headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }

    return headers;
  }

  /// Safely encodes query parameters
  static String encodeQueryParameters(Map<String, dynamic> params) {
    return params.entries
        .map((e) => '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value.toString())}')
        .join('&');
  }

  /// Handles API errors and returns a user-friendly message
  static String getErrorMessage(dynamic error) {
    if (error is Exception) {
      return error.toString().replaceAll('Exception: ', '');
    }
    return error?.toString() ?? 'An unknown error occurred';
  }
}