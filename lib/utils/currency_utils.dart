import 'package:intl/intl.dart';

/// Utility class for currency operations
class CurrencyUtils {
  /// Format a number as currency
  static String formatCurrency(double amount, {String currencyCode = '﷼', String locale = 'en_US'}) {
    final formatter = NumberFormat.currency(
      locale: locale,
      symbol: currencyCode,
      decimalDigits: 2,
    );
    return formatter.format(amount);
  }

  /// Format a number as currency without symbol
  static String formatNumber(double amount, {int decimalDigits = 2, String locale = 'en_US'}) {
    final formatter = NumberFormat.decimalPattern(locale)
      ..minimumFractionDigits = decimalDigits
      ..maximumFractionDigits = decimalDigits;
    return formatter.format(amount);
  }

  /// Parse currency string to double
  static double parseCurrency(String amount) {
    // Remove currency symbols, spaces, and commas
    final cleanedAmount = amount.replaceAll(RegExp(r'[^\d\.\-]'), '');
    return double.tryParse(cleanedAmount) ?? 0.0;
  }

  /// Calculate discount amount
  static double calculateDiscount(double amount, double discountPercentage) {
    return amount * (discountPercentage / 100);
  }

  /// Calculate amount after discount
  static double calculateAmountAfterDiscount(double amount, double discountPercentage) {
    return amount - calculateDiscount(amount, discountPercentage);
  }

  /// Calculate tax amount
  static double calculateTax(double amount, double taxPercentage) {
    return amount * (taxPercentage / 100);
  }

  /// Calculate amount with tax
  static double calculateAmountWithTax(double amount, double taxPercentage) {
    return amount + calculateTax(amount, taxPercentage);
  }

  /// Format percentage
  static String formatPercentage(double percentage, {int decimalDigits = 1, String locale = 'en_US'}) {
    final formatter = NumberFormat.percentPattern(locale)
      ..minimumFractionDigits = decimalDigits
      ..maximumFractionDigits = decimalDigits;
    return formatter.format(percentage / 100);
  }
}