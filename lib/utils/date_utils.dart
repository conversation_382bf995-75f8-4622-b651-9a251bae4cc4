import 'package:intl/intl.dart';

/// Utility class for date operations
class DateTimeUtils {
  /// Format date to display format (e.g., "01/01/2023")
  static String formatDate(DateTime? date, {String format = 'dd/MM/yyyy'}) {
    if (date == null) return '';
    return DateFormat(format).format(date);
  }

  /// Parse string to DateTime
  static DateTime? parseDate(String? dateStr) {
    if (dateStr == null || dateStr.isEmpty) return null;
    try {
      return DateTime.parse(dateStr);
    } catch (e) {
      return null;
    }
  }

  /// Calculate age from birthdate
  static int calculateAge(DateTime birthDate) {
    final today = DateTime.now();
    int age = today.year - birthDate.year;
    if (today.month < birthDate.month || 
        (today.month == birthDate.month && today.day < birthDate.day)) {
      age--;
    }
    return age;
  }

  /// Check if a date is in the past
  static bool isPast(DateTime date) {
    return date.isBefore(DateTime.now());
  }

  /// Check if a date is in the future
  static bool isFuture(DateTime date) {
    return date.isAfter(DateTime.now());
  }

  /// Get the difference between two dates in days
  static int daysBetween(DateTime from, DateTime to) {
    return to.difference(from).inDays;
  }

  /// Format a date range (e.g., "01/01/2023 - 31/01/2023")
  static String formatDateRange(DateTime? start, DateTime? end, {String format = 'dd/MM/yyyy'}) {
    if (start == null || end == null) return '';
    return '${formatDate(start, format: format)} - ${formatDate(end, format: format)}';
  }

  /// Get the first day of the month for a given date
  static DateTime getFirstDayOfMonth(DateTime date) {
    return DateTime(date.year, date.month, 1);
  }

  /// Get the last day of the month for a given date
  static DateTime getLastDayOfMonth(DateTime date) {
    return DateTime(date.year, date.month + 1, 0);
  }

  /// Get a list of dates between two dates
  static List<DateTime> getDaysInRange(DateTime start, DateTime end) {
    final days = <DateTime>[];
    for (int i = 0; i <= daysBetween(start, end); i++) {
      days.add(start.add(Duration(days: i)));
    }
    return days;
  }
}