// ignore_for_file: unused_import, depend_on_referenced_packages

import 'dart:io';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

/// Utility class for file operations
class FileUtils {
  /// Get file extension from file path
  static String getFileExtension(String filePath) {
    return path.extension(filePath).toLowerCase();
  }

  /// Get file name from file path
  static String getFileName(String filePath) {
    return path.basename(filePath);
  }

  /// Get file size in human-readable format
  static String getFileSize(File file, {int decimals = 1}) {
    final bytes = file.lengthSync();
    if (bytes <= 0) return '0 B';
    
    const suffixes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
    final i = (log(bytes) / log(1024)).floor();
    return '${(bytes / pow(1024, i)).toStringAsFixed(decimals)} ${suffixes[i]}';
  }

  /// Check if file is an image
  static bool isImage(String filePath) {
    final ext = getFileExtension(filePath);
    return ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'].contains(ext);
  }

  /// Check if file is a document
  static bool isDocument(String filePath) {
    final ext = getFileExtension(filePath);
    return ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt'].contains(ext);
  }

  /// Get temporary directory path
  static Future<String> getTempDirectoryPath() async {
    final directory = await getTemporaryDirectory();
    return directory.path;
  }

  /// Get application documents directory path
  static Future<String> getDocumentsDirectoryPath() async {
    final directory = await getApplicationDocumentsDirectory();
    return directory.path;
  }

  /// Create a unique file name
  static String createUniqueFileName(String fileName) {
    final name = path.basenameWithoutExtension(fileName);
    final extension = path.extension(fileName);
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return '$name-$timestamp$extension';
  }

  /// Get MIME type from file extension
  static String getMimeType(String filePath) {
    final ext = getFileExtension(filePath);
    switch (ext) {
      case '.jpg':
      case '.jpeg':
        return 'image/jpeg';
      case '.png':
        return 'image/png';
      case '.pdf':
        return 'application/pdf';
      case '.doc':
        return 'application/msword';
      case '.docx':
        return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      case '.xls':
        return 'application/vnd.ms-excel';
      case '.xlsx':
        return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      default:
        return 'application/octet-stream';
    }
  }
}