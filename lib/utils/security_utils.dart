import 'dart:convert';
import 'dart:math';

/// Security utilities for authentication and data protection
class SecurityUtils {
  /// Validate JWT token format and structure
  static bool isValidSessionToken(String token) {
    if (token.isEmpty) return false;

    try {
      // JWT tokens have 3 parts separated by dots
      final parts = token.split('.');
      if (parts.length != 3) return false;

      // Each part should be base64 encoded (except signature which we won't validate)
      for (int i = 0; i < 2; i++) {
        // Only validate header and payload, not signature
        final part = parts[i];
        if (part.isEmpty) return false;

        // Try to decode each part
        try {
          base64Url.decode(_addPadding(part));
        } catch (e) {
          return false;
        }
      }

      // Validate header and payload structure
      final header = _decodeJwtPart(parts[0]);
      final payload = _decodeJwtPart(parts[1]);

      if (header == null || payload == null) return false;

      // Check for required JWT fields
      if (!header.containsKey('typ') || !header.containsKey('alg')) {
        return false;
      }

      if (!payload.containsKey('exp') || !payload.containsKey('iat')) {
        return false;
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  /// Check if JWT token is expired
  static bool isTokenExpired(String token) {
    if (!isValidSessionToken(token)) return true;

    try {
      final parts = token.split('.');
      final payload = _decodeJwtPart(parts[1]);

      if (payload == null || !payload.containsKey('exp')) {
        return true;
      }

      final exp = payload['exp'] as int;
      final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;

      return now >= exp;
    } catch (e) {
      return true;
    }
  }

  /// Extract expiration time from JWT token
  static DateTime? getTokenExpiration(String token) {
    if (!isValidSessionToken(token)) return null;

    try {
      final parts = token.split('.');
      final payload = _decodeJwtPart(parts[1]);

      if (payload == null || !payload.containsKey('exp')) {
        return null;
      }

      final exp = payload['exp'] as int;
      return DateTime.fromMillisecondsSinceEpoch(exp * 1000);
    } catch (e) {
      return null;
    }
  }

  /// Extract user ID from JWT token
  static String? getUserIdFromToken(String token) {
    if (!isValidSessionToken(token)) return null;

    try {
      final parts = token.split('.');
      final payload = _decodeJwtPart(parts[1]);

      if (payload == null) return null;

      // Try different common field names for user ID
      return payload['sub']?.toString() ??
          payload['user_id']?.toString() ??
          payload['id']?.toString();
    } catch (e) {
      return null;
    }
  }

  /// Mask sensitive data for logging
  static String maskSensitiveData(String data) {
    if (data.isEmpty) return data;

    // Email masking
    if (data.contains('@')) {
      final parts = data.split('@');
      if (parts.length == 2) {
        final username = parts[0];
        final domain = parts[1];

        if (username.length <= 2) {
          return '${username[0]}***@$domain';
        } else {
          return '${username.substring(0, 2)}***@$domain';
        }
      }
    }

    // Phone number masking
    if (RegExp(r'^\+?[\d\s\-\(\)]+$').hasMatch(data)) {
      if (data.length <= 4) {
        return '***${data.substring(data.length - 1)}';
      } else {
        return '***${data.substring(data.length - 4)}';
      }
    }

    // General data masking (show first 2 and last 2 characters)
    if (data.length <= 4) {
      return '***';
    } else if (data.length <= 6) {
      return '${data[0]}***${data[data.length - 1]}';
    } else {
      return '${data.substring(0, 2)}***${data.substring(data.length - 2)}';
    }
  }

  /// Generate secure random string
  static String generateSecureRandomString(int length) {
    const chars =
        'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = Random.secure();

    return List.generate(
      length,
      (index) => chars[random.nextInt(chars.length)],
    ).join();
  }

  /// Validate API key format
  static bool isValidApiKey(String apiKey) {
    if (apiKey.isEmpty) return false;

    // API keys should be at least 32 characters and contain only alphanumeric characters
    if (apiKey.length < 32) return false;

    final apiKeyRegex = RegExp(r'^[a-zA-Z0-9]+$');
    return apiKeyRegex.hasMatch(apiKey);
  }

  /// Check for common security threats in input
  static bool containsSecurityThreats(String input) {
    if (input.isEmpty) return false;

    // Check for SQL injection patterns
    final sqlPatterns = [
      RegExp(
        r'(\bUNION\b|\bSELECT\b|\bINSERT\b|\bUPDATE\b|\bDELETE\b|\bDROP\b)',
        caseSensitive: false,
      ),
      RegExp(r'(--|#|\/\*|\*\/)', caseSensitive: false),
      RegExp(r'(\bOR\b|\bAND\b)\s+\d+\s*=\s*\d+', caseSensitive: false),
    ];

    // Check for XSS patterns
    final xssPatterns = [
      RegExp(r'<script[^>]*>.*?</script>', caseSensitive: false),
      RegExp(r'javascript:', caseSensitive: false),
      RegExp(r'on\w+\s*=', caseSensitive: false),
    ];

    // Check for path traversal
    final pathTraversalPatterns = [
      RegExp(r'\.\./', caseSensitive: false),
      RegExp(r'\.\.\\', caseSensitive: false),
    ];

    final allPatterns = [
      ...sqlPatterns,
      ...xssPatterns,
      ...pathTraversalPatterns,
    ];

    for (final pattern in allPatterns) {
      if (pattern.hasMatch(input)) {
        return true;
      }
    }

    return false;
  }

  /// Sanitize input for security
  static String sanitizeForSecurity(String input) {
    if (input.isEmpty) return input;

    // Remove or escape dangerous characters
    return input
        .replaceAll('<', '')
        .replaceAll('>', '')
        .replaceAll('"', '')
        .replaceAll("'", '')
        .replaceAll(';', '')
        .replaceAll('\\', '')
        .replaceAll('--', '')
        .replaceAll('/*', '')
        .replaceAll('*/', '')
        .trim();
  }

  /// Decode JWT part (header or payload)
  static Map<String, dynamic>? _decodeJwtPart(String part) {
    try {
      final decoded = base64Url.decode(_addPadding(part));
      final jsonString = utf8.decode(decoded);
      return jsonDecode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      return null;
    }
  }

  /// Add padding to base64 string if needed
  static String _addPadding(String base64) {
    final padding = 4 - (base64.length % 4);
    if (padding != 4) {
      return base64 + ('=' * padding);
    }
    return base64;
  }

  /// Validate password complexity
  static bool isStrongPassword(String password) {
    if (password.length < 8) return false;

    // Check for at least one uppercase letter
    if (!password.contains(RegExp(r'[A-Z]'))) return false;

    // Check for at least one lowercase letter
    if (!password.contains(RegExp(r'[a-z]'))) return false;

    // Check for at least one digit
    if (!password.contains(RegExp(r'[0-9]'))) return false;

    // Check for at least one special character
    if (!password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) return false;

    return true;
  }

  /// Generate hash for data integrity checking
  static String generateDataHash(String data) {
    final bytes = utf8.encode(data);
    final hash = bytes.fold<int>(0, (prev, byte) => prev + byte);
    return hash.toRadixString(16);
  }
}
