/// Utility class for string operations
class StringUtils {
  /// Capitalize the first letter of each word in a string
  static String capitalize(String? text) {
    if (text == null || text.isEmpty) return text ?? '';
    return text
        .split(' ')
        .map((word) {
          if (word.isEmpty) return word;
          return word[0].toUpperCase() + word.substring(1).toLowerCase();
        })
        .join(' ');
  }

  /// Truncate a string to a maximum length and add ellipsis if needed
  static String truncate(String? text, int maxLength) {
    if (text == null || text.length <= maxLength) return text ?? '';
    return '${text.substring(0, maxLength)}...';
  }

  /// Check if a string is null, empty, or contains only whitespace
  static bool isNullOrEmpty(String? text) {
    return text == null || text.trim().isEmpty;
  }

  /// Convert a string to camelCase
  static String toCamelCase(String? text) {
    if (text == null || text.isEmpty) return text ?? '';
    final words = text.split(RegExp(r'[_\s-]+'));
    final firstWord = words.first.toLowerCase();
    final remainingWords = words.skip(1).map((word) {
      if (word.isEmpty) return word;
      return word[0].toUpperCase() + word.substring(1).toLowerCase();
    });
    return [firstWord, ...remainingWords].join('');
  }

  /// Convert a string to snake_case
  static String toSnakeCase(String? text) {
    if (text == null || text.isEmpty) return text ?? '';
    return text
        .replaceAllMapped(
          RegExp(r'[A-Z]'),
          (match) => '_${match.group(0) ?? ''}',
        )
        .toLowerCase()
        .replaceAll(RegExp(r'[_\s-]+'), '_')
        .replaceAll(RegExp(r'^_'), '');
  }

  /// Extract initials from a name (e.g., "John Doe" -> "JD")
  static String getInitials(String? name) {
    if (name == null || name.isEmpty) return '';
    final parts = name.split(' ').where((part) => part.isNotEmpty).toList();
    if (parts.isEmpty) return '';
    if (parts.length == 1) return parts[0][0].toUpperCase();
    return parts[0][0].toUpperCase() + parts.last[0].toUpperCase();
  }

  /// Mask a string (e.g., for credit card numbers)
  static String mask(
    String? text, {
    int visibleChars = 4,
    String maskChar = '*',
  }) {
    if (text == null || text.length <= visibleChars) return text ?? '';
    final visible = text.substring(text.length - visibleChars);
    return '${maskChar * (text.length - visibleChars)}$visible';
  }
}
