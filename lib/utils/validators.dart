/// Validation utilities for customer data and security
class CustomerValidators {
  /// Validate email format
  static String? validateEmail(String email) {
    if (email.isEmpty) {
      return 'Email is required';
    }

    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );

    if (!emailRegex.hasMatch(email)) {
      return 'Please enter a valid email address';
    }

    return null;
  }

  /// Validate phone number format
  static String? validatePhone(String phone) {
    if (phone.isEmpty) {
      return 'Phone number is required';
    }

    // Remove all non-digit characters for validation
    final digitsOnly = phone.replaceAll(RegExp(r'[^\d]'), '');

    if (digitsOnly.length < 8 || digitsOnly.length > 15) {
      return 'Please enter a valid phone number';
    }

    return null;
  }

  /// Validate name format
  static String? validateName(String name) {
    if (name.isEmpty) {
      return 'Name is required';
    }

    if (name.length < 2) {
      return 'Name must be at least 2 characters';
    }

    if (name.length > 50) {
      return 'Name must be less than 50 characters';
    }

    // Check for valid characters (letters, spaces, hyphens, apostrophes)
    final nameRegex = RegExp(r"^[a-zA-Z\s\-']+$");
    if (!nameRegex.hasMatch(name)) {
      return 'Name can only contain letters, spaces, hyphens, and apostrophes';
    }

    return null;
  }

  /// Validate national ID format
  static String? validateNationalId(String nationalId) {
    if (nationalId.isEmpty) {
      return 'National ID is required';
    }

    // Remove all non-digit characters
    final digitsOnly = nationalId.replaceAll(RegExp(r'[^\d]'), '');

    if (digitsOnly.length < 8 || digitsOnly.length > 20) {
      return 'Please enter a valid national ID';
    }

    return null;
  }

  /// Validate search query for security
  static String? validateSearchQuery(String query) {
    if (query.isEmpty) {
      return null; // Empty queries are allowed
    }

    // Check for potentially dangerous characters
    if (query.contains('<') ||
        query.contains('>') ||
        query.contains('"') ||
        query.contains("'") ||
        query.contains(';') ||
        query.contains('\\')) {
      return 'Search query contains invalid characters';
    }

    if (query.length > 100) {
      return 'Search query is too long';
    }

    return null;
  }

  /// Sanitize input to prevent XSS and injection attacks
  static String sanitizeInput(String input) {
    if (input.isEmpty) return input;

    // Remove or escape potentially dangerous characters
    return input
        .replaceAll('<', '&lt;')
        .replaceAll('>', '&gt;')
        .replaceAll('"', '&quot;')
        .replaceAll("'", '&#x27;')
        .replaceAll('&', '&amp;')
        .replaceAll('/', '&#x2F;')
        .trim();
  }

  /// Validate password strength
  static String? validatePassword(String password) {
    if (password.isEmpty) {
      return 'Password is required';
    }

    if (password.length < 8) {
      return 'Password must be at least 8 characters';
    }

    if (password.length > 128) {
      return 'Password must be less than 128 characters';
    }

    // Check for at least one uppercase letter
    if (!password.contains(RegExp(r'[A-Z]'))) {
      return 'Password must contain at least one uppercase letter';
    }

    // Check for at least one lowercase letter
    if (!password.contains(RegExp(r'[a-z]'))) {
      return 'Password must contain at least one lowercase letter';
    }

    // Check for at least one digit
    if (!password.contains(RegExp(r'[0-9]'))) {
      return 'Password must contain at least one number';
    }

    // Check for at least one special character
    if (!password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) {
      return 'Password must contain at least one special character';
    }

    return null;
  }

  /// Validate required field
  static String? validateRequired(String value, String fieldName) {
    if (value.isEmpty) {
      return '$fieldName is required';
    }
    return null;
  }

  /// Validate numeric input
  static String? validateNumeric(String value, String fieldName) {
    if (value.isEmpty) {
      return '$fieldName is required';
    }

    if (double.tryParse(value) == null) {
      return '$fieldName must be a valid number';
    }

    return null;
  }

  /// Validate positive number
  static String? validatePositiveNumber(String value, String fieldName) {
    final numericError = validateNumeric(value, fieldName);
    if (numericError != null) return numericError;

    final number = double.parse(value);
    if (number <= 0) {
      return '$fieldName must be greater than zero';
    }

    return null;
  }

  /// Validate date format (YYYY-MM-DD)
  static String? validateDate(String date) {
    if (date.isEmpty) {
      return 'Date is required';
    }

    try {
      final parsedDate = DateTime.parse(date);

      // Check if date is not in the future (for birthdates)
      if (parsedDate.isAfter(DateTime.now())) {
        return 'Date cannot be in the future';
      }

      return null;
    } catch (e) {
      return 'Please enter a valid date (YYYY-MM-DD)';
    }
  }

  /// Validate age based on birthdate
  static String? validateAge(
    String birthdate, {
    int minAge = 0,
    int maxAge = 120,
  }) {
    final dateError = validateDate(birthdate);
    if (dateError != null) return dateError;

    try {
      final birthDate = DateTime.parse(birthdate);
      final today = DateTime.now();

      int age = today.year - birthDate.year;
      if (today.month < birthDate.month ||
          (today.month == birthDate.month && today.day < birthDate.day)) {
        age--;
      }

      if (age < minAge) {
        return 'Age must be at least $minAge years';
      }

      if (age > maxAge) {
        return 'Age must be less than $maxAge years';
      }

      return null;
    } catch (e) {
      return 'Invalid birthdate';
    }
  }

  /// Validate URL format
  static String? validateUrl(String url) {
    if (url.isEmpty) return null; // Optional field

    try {
      final uri = Uri.parse(url);
      if (!uri.hasScheme || (!uri.scheme.startsWith('http'))) {
        return 'Please enter a valid URL starting with http:// or https://';
      }
      return null;
    } catch (e) {
      return 'Please enter a valid URL';
    }
  }
}
