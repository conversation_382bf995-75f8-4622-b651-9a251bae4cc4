import 'package:flutter/material.dart';
// import '../../constants/colors.dart'; // AppColors will be replaced by Theme.of(context)

class CustomIconButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback onPressed;
  final Color? color;
  final Color? backgroundColor;
  final double size;
  final String? tooltip;
  final EdgeInsetsGeometry padding;
  final double borderRadius;
  final bool hasBorder;
  final Color? borderColor;

  const CustomIconButton({
    super.key,
    required this.icon,
    required this.onPressed,
    this.color,
    this.backgroundColor,
    this.size = 24,
    this.tooltip,
    this.padding = const EdgeInsets.all(8),
    this.borderRadius = 8,
    this.hasBorder = false,
    this.borderColor,
  });

  @override
  Widget build(BuildContext context) {
    return Tooltip(
      message: tooltip ?? '',
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(borderRadius),
        child: Container(
          padding: padding,
          decoration: BoxDecoration(
            color: backgroundColor,
            borderRadius: BorderRadius.circular(borderRadius),
            border: hasBorder
                ? Border.all(
                    color: borderColor ?? Theme.of(context).colorScheme.outline,
                    width: 1,
                  )
                : null,
          ),
          child: Icon(
            icon,
            color: color ?? Theme.of(context).colorScheme.onSurface.withOpacity(0.7), // Default icon color
            size: size,
          ),
        ),
      ),
    );
  }
}