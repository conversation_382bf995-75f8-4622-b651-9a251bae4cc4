import 'package:flutter/material.dart';

class AppIconButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback onPressed;
  final Color? color;
  final Color? backgroundColor;
  final double size;
  final String? tooltip;
  final bool showBadge;
  final int badgeCount;
  final Color? badgeColor; // Made nullable

  const AppIconButton({
    super.key,
    required this.icon,
    required this.onPressed,
    this.color,
    this.backgroundColor,
    this.size = 24.0,
    this.tooltip,
    this.showBadge = false,
    this.badgeCount = 0,
    this.badgeColor, // Default removed
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final actualBadgeColor = badgeColor ?? theme.colorScheme.error; // Resolve badge color
    
    Widget button = IconButton(
      icon: Icon(
        icon,
        color: color ?? theme.iconTheme.color,
        size: size,
      ),
      onPressed: onPressed,
      tooltip: tooltip,
      splashRadius: 24.0,
      padding: EdgeInsets.zero,
      constraints: const BoxConstraints(),
    );

    if (backgroundColor != null) {
      button = Container(
        decoration: BoxDecoration(
          color: backgroundColor,
          shape: BoxShape.circle,
        ),
        child: button,
      );
    }

    if (showBadge) {
      return Stack(
        clipBehavior: Clip.none,
        children: [
          button,
          if (badgeCount > 0)
            Positioned(
              top: -5,
              right: -5,
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: actualBadgeColor, // Use resolved badge color
                  shape: BoxShape.circle,
                ),
                constraints: const BoxConstraints(
                  minWidth: 16,
                  minHeight: 16,
                ),
                child: Center(
                  child: Text(
                    badgeCount > 9 ? '9+' : badgeCount.toString(),
                    style: TextStyle(
                      color: theme.colorScheme.onError, // Text color on error background
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ),
        ],
      );
    }

    return button;
  }
}