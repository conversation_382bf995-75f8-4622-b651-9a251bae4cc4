import 'package:flutter/material.dart';

class IconButtonWidget extends StatelessWidget {
  final IconData icon;
  final VoidCallback onPressed;
  final Color? color;
  final Color? backgroundColor;
  final double size;
  final String? tooltip;
  final bool showBadge;
  final String? badgeText;
  final EdgeInsetsGeometry padding;

  const IconButtonWidget({
    super.key,
    required this.icon,
    required this.onPressed,
    this.color,
    this.backgroundColor,
    this.size = 24.0,
    this.tooltip,
    this.showBadge = false,
    this.badgeText,
    this.padding = const EdgeInsets.all(8.0),
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    Widget iconButton = IconButton(
      icon: Icon(
        icon,
        color: color ?? theme.iconTheme.color,
        size: size,
      ),
      onPressed: onPressed,
      tooltip: tooltip,
      padding: padding,
      style: IconButton.styleFrom(
        backgroundColor: backgroundColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.0),
        ),
      ),
    );

    if (showBadge) {
      return Stack(
        clipBehavior: Clip.none,
        children: [
          iconButton,
          Positioned(
            top: 0,
            right: 0,
            child: Container(
              padding: const EdgeInsets.all(2),
              decoration: BoxDecoration(
                color: theme.colorScheme.error, // Replaced Colors.red
                borderRadius: BorderRadius.circular(10),
              ),
              constraints: const BoxConstraints(
                minWidth: 16,
                minHeight: 16,
              ),
              child: Center(
                child: Text(
                  badgeText ?? '',
                  style: TextStyle(
                    color: theme.colorScheme.onError, // Replaced Colors.white
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ),
        ],
      );
    }

    return iconButton;
  }
}