// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';

class CustomCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry padding;
  final EdgeInsetsGeometry margin;
  final double borderRadius;
  final Color? backgroundColor;
  final Color? borderColor;
  final double elevation;
  final VoidCallback? onTap;
  final bool showShadow;

  const CustomCard({
    super.key,
    required this.child,
    this.padding = const EdgeInsets.all(16),
    this.margin = const EdgeInsets.all(0),
    this.borderRadius = 8.0,
    this.backgroundColor,
    this.borderColor,
    this.elevation = 1.0,
    this.onTap,
    this.showShadow = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: margin,
        decoration: BoxDecoration(
          color: backgroundColor ?? theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(borderRadius),
          border:
              borderColor != null
                  ? Border.all(color: borderColor!)
                  : Border.all(
                    color: theme.colorScheme.outline.withOpacity(0.2),
                    width: 1,
                  ),
          boxShadow:
              showShadow
                  ? [
                    BoxShadow(
                      color: theme.colorScheme.shadow.withOpacity(0.15),
                      blurRadius: elevation * 4,
                      offset: Offset(0, elevation * 1.5),
                    ),
                  ]
                  : null,
        ),
        child: Padding(padding: padding, child: child),
      ),
    );
  }
}
