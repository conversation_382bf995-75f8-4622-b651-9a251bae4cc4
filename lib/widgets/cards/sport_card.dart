// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import '../../models/sport.dart';

class SportCard extends StatelessWidget {
  final Sport sport;
  final VoidCallback onTap;
  final VoidCallback onViewDetails;

  const SportCard({
    Key? key,
    required this.sport,
    required this.onTap,
    required this.onViewDetails,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        title: Text(sport.name),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Fees: \$${sport.fees.toStringAsFixed(2)}'),
            Text('Sessions: ${sport.number_of_sessions}'),
            Text('Time: ${sport.training_time}'),
            Text('Trainers: ${sport.trainers}'),
          ],
        ),
        trailing: IconButton(
          icon: const Icon(Icons.info),
          onPressed: onViewDetails,
        ),
        onTap: onTap,
      ),
    );
  }
}