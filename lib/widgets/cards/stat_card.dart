// ignore_for_file: deprecated_member_use, prefer_const_constructors_in_immutables

import 'package:flutter/material.dart';
import '../../constants/text_styles.dart';
import '../../constants/colors.dart'; // Import ModernAppColors for success color
class StatCard extends StatelessWidget {
  late final String title;
  late final String value;
  late final String? subtitle;
  late final IconData icon;
  late final Color iconColor;
  late final Color backgroundColor;
  late final bool isUp;
  late final String? changePercentage;
  late final VoidCallback? onTap;
  late final double borderRadius;
  late final EdgeInsetsGeometry padding;

  StatCard({
    super.key,
    required this.title,
    required this.value,
    this.subtitle,
    required this.icon,
    required this.iconColor,
    required this.backgroundColor,
    this.isUp = true,
    this.changePercentage,
    this.onTap,
    this.borderRadius = 12.0,
    this.padding = const EdgeInsets.all(16.0),
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Card(
      elevation: 0,
      color: theme.cardColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius),
        side: BorderSide(
          color: theme.dividerColor,
          width: 1.0,
        ),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(borderRadius),
        child: Padding(
          padding: padding,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    title,
                    style: TextStyles.labelMedium.copyWith(
                      color: theme.colorScheme.onSurface.withOpacity(0.6),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: backgroundColor.withOpacity(0.2),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      icon,
                      color: iconColor,
                      size: 20,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                value,
                style: TextStyles.headlineSmall.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              if (subtitle != null || changePercentage != null)
                const SizedBox(height: 8),
              if (subtitle != null)
                Text(
                  subtitle!,
                  style: TextStyles.bodySmall.copyWith(
                    color: theme.colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
              if (changePercentage != null)
                Row(
                  children: [
                    Icon(
                      isUp ? Icons.arrow_upward : Icons.arrow_downward,
                      color: isUp ? ModernAppColors.success : theme.colorScheme.error, // Replaced Colors.green and Colors.red
                      size: 14,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '$changePercentage%',
                      style: TextStyles.bodySmall.copyWith(
                        color: isUp ? ModernAppColors.success : theme.colorScheme.error, // Replaced Colors.green and Colors.red
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      isUp ? 'increase' : 'decrease',
                      style: TextStyles.bodySmall.copyWith(
                        color: theme.colorScheme.onSurface.withOpacity(0.7),
                      ),
                    ),
                  ],
                ),
            ],
          ),
        ),
      ),
    );
  }
}