// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import '../../constants/text_styles.dart';
import '../../models/trainee.dart';
import '../status/status_badge.dart';
import '../../constants/colors.dart'; // Import ModernAppColors
class TraineeCard extends StatelessWidget {
  final Trainee trainee;
  final VoidCallback? onTap;
  final VoidCallback? onViewDetails;

  const TraineeCard({
    super.key,
    required this.trainee,
    this.onTap,
    this.onViewDetails,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: <PERSON>umn(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  CircleAvatar(
                    radius: 24,
                    backgroundColor: theme.primaryColor.withOpacity(0.2),
                    child: Text(
                      _getInitials(trainee.name),
                      style: TextStyles.titleMedium.copyWith(
                        color: theme.primaryColor,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          trainee.name,
                          style: TextStyles.titleMedium.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          trainee.trainingProgram,
                          style: TextStyles.bodySmall.copyWith(
                            color: theme.colorScheme.onSurface.withOpacity(0.7),
                          ),
                        ),
                      ],
                    ),
                  ),
                  StatusBadge(
                    text: trainee.skillLevel,
                    color: _getSkillLevelColor(trainee.skillLevel),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Icon(
                    Icons.person,
                    size: 16,
                    color: theme.colorScheme.onSurface.withOpacity(0.7),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'Trainer ID: ${trainee.trainerId}',
                    style: TextStyles.bodyMedium,
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(
                    Icons.calendar_today,
                    size: 16,
                    color: theme.colorScheme.onSurface.withOpacity(0.7),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'Joined: ${trainee.joinDate}',
                    style: TextStyles.bodyMedium,
                  ),
                ],
              ),
              if (onViewDetails != null)
                Padding(
                  padding: const EdgeInsets.only(top: 12),
                  child: Align(
                    alignment: Alignment.centerRight,
                    child: TextButton(
                      onPressed: onViewDetails,
                      child: Text(
                        'View Details',
                        style: TextStyles.labelMedium.copyWith(
                          color: theme.primaryColor,
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
  
  String _getInitials(String name) {
    if (name.isEmpty) return '';
    
    final nameParts = name.split(' ');
    if (nameParts.length == 1) {
      return nameParts[0][0].toUpperCase();
    }
    
    return '${nameParts[0][0]}${nameParts[1][0]}'.toUpperCase();
  }
  
  Color _getSkillLevelColor(String skillLevel) {
    switch (skillLevel.toLowerCase()) {
      case 'beginner':
        return ModernAppColors.success; // Replaced Colors.green
      case 'intermediate':
        return ModernAppColors.info;    // Replaced Colors.blue
      case 'advanced':
        return ModernAppColors.accent;  // Replaced Colors.purple with a distinct palette color
      case 'expert':
        return ModernAppColors.error;   // Replaced Colors.red
      default:
        return ModernAppColors.disabledColor; // Replaced Colors.grey
    }
  }
}