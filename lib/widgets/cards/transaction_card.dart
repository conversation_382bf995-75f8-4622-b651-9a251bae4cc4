// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../constants/text_styles.dart';
import '../../models/transactions.dart';
import '../status/status_badge.dart';
import '../../constants/colors.dart'; // Import ModernAppColors
class TransactionCard extends StatelessWidget {
  final Transaction transaction;
  final VoidCallback? onTap;
  final VoidCallback? onViewDetails;

  const TransactionCard({
    super.key,
    required this.transaction,
    this.onTap,
    this.onViewDetails,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isIncome = transaction.type.toLowerCase() == 'income';
    final dateFormat = DateFormat('dd MMM yyyy');
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: (isIncome ? ModernAppColors.success : theme.colorScheme.error).withOpacity(0.2), // Replaced Colors
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      isIncome ? Icons.arrow_downward : Icons.arrow_upward,
                      color: isIncome ? ModernAppColors.success : theme.colorScheme.error, // Replaced Colors
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          transaction.description,
                          style: TextStyles.titleMedium.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          transaction.date.isNotEmpty 
                              ? dateFormat.format(DateTime.parse(transaction.date))
                              : 'N/A',
                          style: TextStyles.bodySmall.copyWith(
                            color: theme.colorScheme.onSurface.withOpacity(0.7),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        '${isIncome ? '+' : '-'} SAR ${transaction.amount.toStringAsFixed(2)}',
                        style: TextStyles.titleMedium.copyWith(
                          color: isIncome ? ModernAppColors.success : theme.colorScheme.error, // Replaced Colors
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      StatusBadge(
                        text: transaction.status,
                        color: _getStatusColor(transaction.status),
                        fontSize: 10,
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      ),
                    ],
                  ),
                ],
              ),
              if (transaction.customerName.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(top: 12),
                  child: Row(
                    children: [
                      Icon(
                        Icons.person,
                        size: 16,
                        color: theme.colorScheme.onSurface.withOpacity(0.7),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        transaction.customerName,
                        style: TextStyles.bodyMedium,
                      ),
                    ],
                  ),
                ),
              if (onViewDetails != null)
                Padding(
                  padding: const EdgeInsets.only(top: 12),
                  child: Align(
                    alignment: Alignment.centerRight,
                    child: TextButton(
                      onPressed: onViewDetails,
                      child: Text(
                        'View Details',
                        style: TextStyles.labelMedium.copyWith(
                          color: theme.primaryColor,
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
  
  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
      case 'paid':
        return ModernAppColors.success; // Replaced Colors.green
      case 'pending':
        return ModernAppColors.warning; // Replaced Colors.orange
      case 'cancelled':
        return ModernAppColors.error;   // Replaced Colors.red
      default:
        return ModernAppColors.info;    // Replaced Colors.blue
    }
  }
}