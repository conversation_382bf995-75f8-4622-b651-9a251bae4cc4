import 'package:flutter/material.dart';
import '../../constants/colors.dart';

class RatingWidget extends StatelessWidget {
  final int rating;
  final int maxRating;
  final Function(int)? onRatingChanged;
  final double iconSize;
  final Color? activeColor; // Made nullable
  final Color? inactiveColor; // Made nullable
  
  const RatingWidget({
    super.key,
    this.rating = 0,
    this.maxRating = 5,
    this.onRatingChanged,
    this.iconSize = 24.0,
    this.activeColor, // <PERSON><PERSON>ult removed
    this.inactiveColor, // Default removed
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    // Resolve colors using ModernAppColors for semantic match or theme for general cases
    // ModernAppColors.warning is Amber, matching Colors.amber
    // theme.hintColor is a suitable replacement for Colors.grey for inactive state
    final actualActiveColor = activeColor ?? ModernAppColors.warning; 
    final actualInactiveColor = inactiveColor ?? theme.hintColor;

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(maxRating, (index) {
        return GestureDetector(
          onTap: onRatingChanged != null ? () => onRatingChanged!(index + 1) : null,
          child: Icon(
            index < rating ? Icons.star : Icons.star_border,
            size: iconSize,
            color: index < rating ? actualActiveColor : actualInactiveColor,
          ),
        );
      }),
    );
  }
}