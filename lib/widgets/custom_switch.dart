// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';

/// A custom switch widget that provides a styled toggle switch.
/// 
/// This widget wraps the Flutter Switch widget with custom styling
/// to match the application's design language.
class CustomSwitch extends StatelessWidget {
  final bool value;
  final ValueChanged<bool> onChanged;
  final Color? activeColor;
  final Color? inactiveColor;
  final Color? activeTrackColor;
  final Color? inactiveTrackColor;
  final Widget? activeThumbChild;
  final Widget? inactiveThumbChild;

  const CustomSwitch({
    Key? key,
    required this.value,
    required this.onChanged,
    this.activeColor,
    this.inactiveColor,
    this.activeTrackColor,
    this.inactiveTrackColor,
    this.activeThumbChild,
    this.inactiveThumbChild,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Switch(
      value: value,
      onChanged: onChanged,
      activeColor: activeColor ?? theme.colorScheme.primary,
      inactiveThumbColor: inactiveColor ?? theme.disabledColor, // Replaced Colors.grey.shade400
      activeTrackColor: activeTrackColor ?? theme.colorScheme.primary.withOpacity(0.5),
      inactiveTrackColor: inactiveColor ?? theme.disabledColor.withOpacity(0.5), // Replaced Colors.grey.shade300
    );
  }
}