import 'package:flutter/material.dart';
import 'dart:io';
import '../../constants/text_styles.dart';
import '../../utils/file_utils.dart';

class FilePickerWidget extends StatelessWidget {
  final File? file;
  final Function(File) onFilePicked;
  final VoidCallback onRemove;
  final String label;
  final List<String>? allowedExtensions;

  const FilePickerWidget({
    super.key,
    this.file,
    required this.onFilePicked,
    required this.onRemove,
    this.label = 'Select File',
    this.allowedExtensions,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label, style: TextStyles.titleMedium),
        const SizedBox(height: 8),
        if (file == null)
          _buildPickButton(context)
        else
          _buildFilePreview(context),
      ],
    );
  }

  Widget _buildPickButton(BuildContext context) {
    return InkWell(
      onTap: () => _pickFile(context),
      child: Container(
        width: double.infinity,
        height: 100,
        decoration: BoxDecoration(
          border: Border.all(color: Theme.of(context).colorScheme.outline), // Replaced Colors.grey
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.upload_file, size: 32),
            const SizedBox(height: 8),
            Text(
              'Click to select a file',
              style: TextStyles.bodyMedium,
            ),
            if (allowedExtensions != null && allowedExtensions!.isNotEmpty)
              Text(
                'Allowed: ${allowedExtensions!.join(', ')}',
                style: TextStyles.bodySmall.copyWith(color: Theme.of(context).hintColor), // Replaced Colors.grey
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilePreview(BuildContext context) {
    final fileName = FileUtils.getFileName(file!.path);
    final fileSize = FileUtils.getFileSize(file!);
    final isImage = FileUtils.isImage(file!.path);

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        border: Border.all(color: Theme.of(context).colorScheme.outline), // Replaced Colors.grey
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          if (isImage)
            ClipRRect(
              borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
              child: Image.file(
                file!,
                height: 150,
                width: double.infinity,
                fit: BoxFit.cover,
              ),
            ),
          Padding(
            padding: const EdgeInsets.all(12.0),
            child: Row(
              children: [
                Icon(
                  isImage ? Icons.image : Icons.insert_drive_file,
                  size: 24,
                  color: Theme.of(context).colorScheme.primary, // Replaced Colors.blue
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        fileName,
                        style: TextStyles.bodyMedium,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      Text(
                        fileSize,
                        style: TextStyles.bodySmall.copyWith(color: Theme.of(context).hintColor), // Replaced Colors.grey
                      ),
                    ],
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.delete, color: Theme.of(context).colorScheme.error), // Replaced Colors.red
                  onPressed: onRemove,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _pickFile(BuildContext context) async {
    // This is a placeholder. In a real implementation, you would use a file picker package
    // like file_picker or image_picker to select files from the device.
    // For now, we'll just show a message.
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('File picking functionality would be implemented here'),
      ),
    );
    
    // In a real implementation, you would do something like:
    // final result = await FilePicker.platform.pickFiles(
    //   type: FileType.custom,
    //   allowedExtensions: allowedExtensions,
    // );
    // if (result != null) {
    //   final file = File(result.files.single.path!);
    //   onFilePicked(file);
    // }
  }
}