// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../constants/text_styles.dart';

class CustomDatePicker extends StatelessWidget {
  final String label;
  final String hint;
  final DateTime? selectedDate;
  final Function(DateTime) onDateSelected;
  final String? Function(String?)? validator;
  final DateTime? firstDate;
  final DateTime? lastDate;
  final bool enabled;
  final TextEditingController? controller;

  const CustomDatePicker({
    super.key,
    required this.label,
    required this.hint,
    required this.selectedDate,
    required this.onDateSelected,
    this.validator,
    this.firstDate,
    this.lastDate,
    this.enabled = true,
    this.controller,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final TextEditingController dateController = controller ?? TextEditingController(
      text: selectedDate != null ? DateFormat('dd/MM/yyyy').format(selectedDate!) : '',
    );
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyles.labelMedium.copyWith(
            color: theme.colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: dateController,
          readOnly: true,
          enabled: enabled,
          validator: validator,
          onTap: enabled ? () async {
            final DateTime now = DateTime.now();
            final DateTime? picked = await showDatePicker(
              context: context,
              initialDate: selectedDate ?? now,
              firstDate: firstDate ?? DateTime(now.year - 100),
              lastDate: lastDate ?? DateTime(now.year + 100),
              builder: (context, child) {
                return Theme(
                  data: Theme.of(context).copyWith(
                    colorScheme: ColorScheme.light(
                      primary: theme.primaryColor,
                      onPrimary: theme.colorScheme.onPrimary,
                      surface: theme.cardColor,
                      onSurface: theme.colorScheme.onSurface,
                    ),
                  ),
                  child: child!,
                );
              },
            );
            
            if (picked != null) {
              dateController.text = DateFormat('dd/MM/yyyy').format(picked);
              onDateSelected(picked);
            }
          } : null,
          decoration: InputDecoration(
            hintText: hint,
            prefixIcon: const Icon(Icons.calendar_today, size: 20),
            suffixIcon: const Icon(Icons.arrow_drop_down),
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: theme.dividerColor),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: theme.dividerColor),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: theme.primaryColor, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: theme.colorScheme.error),
            ),
            filled: true,
            fillColor: enabled ? Colors.transparent : theme.disabledColor.withOpacity(0.05),
          ),
          style: TextStyles.bodyMedium,
        ),
      ],
    );
  }
}