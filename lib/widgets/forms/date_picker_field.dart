// ignore_for_file: use_super_parameters, deprecated_member_use, unused_import

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../constants/text_styles.dart';

class DatePickerField extends StatefulWidget {
  final String label;
  final String? hint;
  final TextEditingController? controller;
  final String? initialValue;
  final Function(String)? onChanged;
  final String? Function(String?)? validator;
  final bool isRequired;
  final bool showAge;

  const DatePickerField({
    Key? key,
    required this.label,
    this.hint,
    this.controller,
    this.initialValue,
    this.onChanged,
    this.validator,
    this.isRequired = false,
    this.showAge = true,
  }) : super(key: key);

  @override
  State<DatePickerField> createState() => _DatePickerFieldState();
}

class _DatePickerFieldState extends State<DatePickerField> {
  late TextEditingController _controller;
  String? _calculatedAge;

  @override
  void initState() {
    super.initState();
    _controller =
        widget.controller ?? TextEditingController(text: widget.initialValue);
    if (_controller.text.isNotEmpty) {
      _calculateAge(_controller.text);
    }
  }

  void _calculateAge(String dateString) {
    try {
      final birthDate = DateTime.parse(dateString);
      final now = DateTime.now();
      int age = now.year - birthDate.year;

      // Check if birthday hasn't occurred this year yet
      if (now.month < birthDate.month ||
          (now.month == birthDate.month && now.day < birthDate.day)) {
        age--;
      }

      setState(() {
        _calculatedAge = '$age years old';
      });
    } catch (e) {
      setState(() {
        _calculatedAge = null;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(
          controller: _controller,
          readOnly: true,
          validator: widget.validator,
          decoration: InputDecoration(
            labelText: widget.isRequired ? '${widget.label} *' : widget.label,
            hintText: widget.hint,
            prefixIcon: const Icon(Icons.calendar_today),
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 12,
              vertical: 8,
            ),
          ),
          onTap: () async {
            final DateTime? picked = await showDatePicker(
              context: context,
              initialDate: DateTime.now(),
              firstDate: DateTime(1950),
              lastDate: DateTime.now(),
              builder: (context, child) {
                return Theme(
                  data: Theme.of(context).copyWith(
                    colorScheme: Theme.of(context).colorScheme.copyWith(
                      primary: Theme.of(context).primaryColor,
                    ),
                  ),
                  child: child!,
                );
              },
            );
            if (picked != null) {
              final String formattedDate =
                  '${picked.year}-${picked.month.toString().padLeft(2, '0')}-${picked.day.toString().padLeft(2, '0')}';
              _controller.text = formattedDate;
              _calculateAge(formattedDate);
              if (widget.onChanged != null) {
                widget.onChanged!(formattedDate);
              }
            }
          },
        ),
        if (widget.showAge && _calculatedAge != null)
          Padding(
            padding: const EdgeInsets.only(top: 4, left: 12),
            child: Text(
              _calculatedAge!,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.primary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
      ],
    );
  }
}
