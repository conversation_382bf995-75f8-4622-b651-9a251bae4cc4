// ignore_for_file: use_super_parameters, deprecated_member_use, unused_import

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../constants/text_styles.dart';

class DatePickerField extends StatelessWidget {
  final String label;
  final String? hint;
  final TextEditingController? controller;
  final String? initialValue;
  final Function(String)? onChanged;
  final String? Function(String?)? validator;
  final bool isRequired;

  const DatePickerField({
    Key? key,
    required this.label,
    this.hint,
    this.controller,
    this.initialValue,
    this.onChanged,
    this.validator,
    this.isRequired = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Create a local controller if initialValue is provided
    final TextEditingController localController =
        controller ?? TextEditingController(text: initialValue);

    return TextFormField(
      controller: localController,
      readOnly: true,
      validator: validator,
      decoration: InputDecoration(
        labelText: isRequired ? '$label *' : label,
        hintText: hint,
        prefixIcon: const Icon(Icons.calendar_today),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
      ),
      onTap: () async {
        final DateTime? picked = await showDatePicker(
          context: context,
          initialDate: DateTime.now(),
          firstDate: DateTime(1950),
          lastDate: DateTime.now(),
        );
        if (picked != null) {
          final String formattedDate =
              '${picked.year}-${picked.month.toString().padLeft(2, '0')}-${picked.day.toString().padLeft(2, '0')}';
          localController.text = formattedDate;
          if (onChanged != null) {
            onChanged!(formattedDate);
          }
        }
      },
    );
  }
}
