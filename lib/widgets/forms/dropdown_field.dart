// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
// import 'package:hemmaerp/constants/colors.dart'; // AppColors will be replaced by Theme.of(context)
import '../../constants/text_styles.dart';

class DropdownField<T> extends StatelessWidget {
  final String label;
  final String? hint;
  final T? value;
  final List<DropdownMenuItem<T>> items;
  final void Function(T?)? onChanged;
  final bool isRequired;
  final String? Function(T?)? validator;
  final bool isExpanded;
  final EdgeInsetsGeometry contentPadding;
  final bool readOnly;

  const DropdownField({
    super.key,
    required this.label,
    this.hint,
    this.value,
    required this.items,
    this.onChanged,
    this.isRequired = false,
    this.validator,
    this.isExpanded = true,
    this.contentPadding = const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
    this.readOnly = false,
  });

  @override
  Widget build(BuildContext context) {
    Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              label,
              style: TextStyles.labelMedium.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            if (isRequired)
              Text(
                ' *',
                style: TextStyles.labelMedium.copyWith(
                  color: Theme.of(context).colorScheme.error, // Replaced Colors.red
                  fontWeight: FontWeight.bold,
                ),
              ),
          ],
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<T>(
          value: value,
          items: items,
          onChanged: readOnly ? null : onChanged,
          validator: validator ?? (T? value) {
            if (isRequired && value == null) {
              return 'This field is required';
            }
            return null;
          },
          isExpanded: isExpanded,
          icon: const Icon(Icons.arrow_drop_down),
          style: TextStyles.bodyMedium,
          decoration: InputDecoration(
            hintText: hint,
            // The InputDecorationTheme in app_theme.dart already defines hintStyle,
            // labelStyle, floatingLabelStyle, borders, fillColor.
            // Ideally, these should be inherited.
            // For this refactor, direct AppColor replacements are made.
            // A follow-up could be to remove these overrides if they match the global theme.
            hintStyle: TextStyles.bodyMedium.copyWith(
              color: Theme.of(context).hintColor,
            ),
            contentPadding: contentPadding,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: BorderSide(
                color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: BorderSide(
                color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: BorderSide(
                color: Theme.of(context).colorScheme.primary,
                width: 1.5,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: BorderSide(
                color: Theme.of(context).colorScheme.error,
                width: 1,
              ),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: BorderSide(
                color: Theme.of(context).colorScheme.error,
                width: 1.5,
              ),
            ),
            filled: true,
            fillColor: readOnly ? Theme.of(context).disabledColor.withOpacity(0.1) : Colors.transparent,
            labelStyle: TextStyle(color: Theme.of(context).inputDecorationTheme.labelStyle?.color ?? Theme.of(context).colorScheme.onSurface.withOpacity(0.7)),
            floatingLabelStyle: TextStyle(color: Theme.of(context).colorScheme.primary),
          ),
        ),
      ],
    );
  }
}