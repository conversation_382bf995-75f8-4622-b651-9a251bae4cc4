import 'package:flutter/material.dart';

class FormRow extends StatelessWidget {
  final List<Widget> children;
  final CrossAxisAlignment crossAxisAlignment;
  final MainAxisAlignment mainAxisAlignment;
  final double spacing;
  final bool expandedByDefault;

  const FormRow({
    super.key,
    required this.children,
    this.crossAxisAlignment = CrossAxisAlignment.start,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.spacing = 16.0,
    this.expandedByDefault = true,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: crossAxisAlignment,
      mainAxisAlignment: mainAxisAlignment,
      children: [
        for (int i = 0; i < children.length; i++) ...[          
          if (expandedByDefault && children[i] is! Expanded)
            Expanded(
              child: children[i],
            )
          else
            children[i],
          if (i < children.length - 1)
            SizedBox(width: spacing),
        ],
      ],
    );
  }
}