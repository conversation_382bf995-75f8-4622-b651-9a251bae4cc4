import 'package:flutter/material.dart';

class CircularProgress extends StatelessWidget {
  final double? value;
  final Color? color;
  final double strokeWidth;
  
  const CircularProgress({
    super.key,
    this.value,
    this.color,
    this.strokeWidth = 4.0,
  });

  @override
  Widget build(BuildContext context) {
    return CircularProgressIndicator(
      value: value,
      strokeWidth: strokeWidth,
      valueColor: AlwaysStoppedAnimation<Color>(
        color ?? Theme.of(context).primaryColor,
      ),
    );
  }
}