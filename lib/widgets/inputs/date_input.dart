// ignore_for_file: unused_import, no_leading_underscores_for_local_identifiers, deprecated_member_use

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../constants/text_styles.dart';
import '../../utils/date_utils.dart' as app_date_utils;

class DateInput extends StatelessWidget {
  final String label;
  final String? hint;
  final DateTime? value;
  final ValueChanged<DateTime>? onChanged;
  final String? errorText;
  final bool enabled;
  final DateTime? firstDate;
  final DateTime? lastDate;
  final String format;
  final FormFieldValidator<String>? validator;
  final AutovalidateMode autovalidateMode;
  final TextEditingController? controller;

  const DateInput({
    super.key,
    required this.label,
    this.hint,
    this.value,
    this.onChanged,
    this.errorText,
    this.enabled = true,
    this.firstDate,
    this.lastDate,
    this.format = 'dd/MM/yyyy',
    this.validator,
    this.autovalidateMode = AutovalidateMode.onUserInteraction,
    this.controller,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final TextEditingController _controller = controller ?? TextEditingController(
      text: value != null ? DateFormat(format).format(value!) : '',
    );
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          label,
          style: TextStyles.labelMedium.copyWith(
            color: theme.colorScheme.onSurface.withOpacity(0.8),
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _controller,
          readOnly: true,
          enabled: enabled,
          validator: validator,
          autovalidateMode: autovalidateMode,
          style: TextStyles.bodyMedium,
          onTap: enabled ? () => _selectDate(context, _controller) : null,
          decoration: InputDecoration(
            hintText: hint ?? 'Select date',
            errorText: errorText,
            suffixIcon: const Icon(Icons.calendar_today),
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: theme.dividerColor,
                width: 1.0,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: theme.dividerColor,
                width: 1.0,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: theme.primaryColor,
                width: 1.5,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: theme.colorScheme.error,
                width: 1.0,
              ),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: theme.colorScheme.error,
                width: 1.5,
              ),
            ),
            filled: true,
            fillColor: enabled ? Colors.transparent : theme.disabledColor.withOpacity(0.05),
          ),
        ),
      ],
    );
  }

  Future<void> _selectDate(BuildContext context, TextEditingController controller) async {
    final DateTime now = DateTime.now();
    final DateTime initialDate = value ?? now;
    final DateTime firstAllowedDate = firstDate ?? DateTime(now.year - 100, 1, 1);
    final DateTime lastAllowedDate = lastDate ?? DateTime(now.year + 100, 12, 31);

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: firstAllowedDate,
      lastDate: lastAllowedDate,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: Theme.of(context).primaryColor,
              onPrimary: Theme.of(context).colorScheme.onPrimary, // Replaced Colors.white
              onSurface: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      controller.text = DateFormat(format).format(picked);
      if (onChanged != null) {
        onChanged!(picked);
      }
    }
  }
}