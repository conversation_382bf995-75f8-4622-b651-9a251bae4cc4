// ignore_for_file: no_leading_underscores_for_local_identifiers

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../constants/text_styles.dart';

class DatePickerField extends StatelessWidget {
  final String label;
  final String? hint;
  final DateTime? selectedDate;
  final Function(DateTime) onDateSelected;
  final DateTime? firstDate;
  final DateTime? lastDate;
  final String? Function(String?)? validator;
  final bool isEnabled;
  final String dateFormat;
  final TextEditingController? controller;

  const DatePickerField({
    super.key,
    required this.label,
    this.hint,
    required this.selectedDate,
    required this.onDateSelected,
    this.firstDate,
    this.lastDate,
    this.validator,
    this.isEnabled = true,
    this.dateFormat = 'dd/MM/yyyy',
    this.controller,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final TextEditingController _controller =
        controller ??
        TextEditingController(
          text:
              selectedDate != null
                  ? DateFormat(dateFormat).format(selectedDate!)
                  : '',
        );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          label,
          style: TextStyles.labelMedium.copyWith(
            color: theme.colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _controller,
          readOnly: true,
          enabled: isEnabled,
          validator: validator,
          style: TextStyles.bodyMedium,
          decoration: InputDecoration(
            hintText: hint ?? 'Select date',
            hintStyle: TextStyles.bodyMedium.copyWith(color: theme.hintColor),
            prefixIcon: const Icon(Icons.calendar_today),
            suffixIcon:
                isEnabled
                    ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _controller.clear();
                        onDateSelected(DateTime(0));
                      },
                    )
                    : null,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 16,
            ),
            filled: true,
            fillColor: theme.inputDecorationTheme.fillColor,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: theme.dividerColor),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: theme.dividerColor),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: theme.primaryColor),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.red),
            ),
          ),
          onTap:
              isEnabled
                  ? () async {
                    final DateTime? picked = await showDatePicker(
                      context: context,
                      initialDate: selectedDate ?? DateTime.now(),
                      firstDate: firstDate ?? DateTime(1900),
                      lastDate: lastDate ?? DateTime(2100),
                      builder: (context, child) {
                        return Theme(
                          data: Theme.of(context).copyWith(
                            colorScheme:
                                theme.brightness == Brightness.dark
                                    ? ColorScheme.dark(
                                      primary: theme.colorScheme.primary,
                                      onPrimary: theme.colorScheme.onPrimary,
                                      surface: theme.colorScheme.surface,
                                      onSurface: theme.colorScheme.onSurface,
                                      background: theme.colorScheme.background,
                                      onBackground:
                                          theme.colorScheme.onBackground,
                                    )
                                    : ColorScheme.light(
                                      primary: theme.colorScheme.primary,
                                      onPrimary: theme.colorScheme.onPrimary,
                                      surface: theme.colorScheme.surface,
                                      onSurface: theme.colorScheme.onSurface,
                                      background: theme.colorScheme.background,
                                      onBackground:
                                          theme.colorScheme.onBackground,
                                    ),
                          ),
                          child: child!,
                        );
                      },
                    );
                    if (picked != null) {
                      _controller.text = DateFormat(dateFormat).format(picked);
                      onDateSelected(picked);
                    }
                  }
                  : null,
        ),
      ],
    );
  }
}
