import 'package:flutter/material.dart';
import '../../constants/text_styles.dart';

class SearchBarWidget extends StatelessWidget {
  final TextEditingController controller;
  final String hintText;
  final Function(String) onChanged;
  final VoidCallback? onClear;
  final bool autofocus;
  final Color? backgroundColor;
  final double borderRadius;
  final EdgeInsetsGeometry padding;
  final bool showLeadingIcon;

  const SearchBarWidget({
    super.key,
    required this.controller,
    required this.hintText,
    required this.onChanged,
    this.onClear,
    this.autofocus = false,
    this.backgroundColor,
    this.borderRadius = 8.0,
    this.padding = const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
    this.showLeadingIcon = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      decoration: BoxDecoration(
        color: backgroundColor ?? theme.cardColor,
        borderRadius: BorderRadius.circular(borderRadius),
        border: Border.all(color: theme.dividerColor),
      ),
      padding: padding,
      child: Row(
        children: [
          if (showLeadingIcon) ...[
            Icon(
              Icons.search,
              color: theme.hintColor,
              size: 20,
            ),
            const SizedBox(width: 8),
          ],
          Expanded(
            child: TextField(
              controller: controller,
              onChanged: onChanged,
              autofocus: autofocus,
              style: TextStyles.bodyMedium,
              decoration: InputDecoration(
                hintText: hintText,
                hintStyle: TextStyles.bodyMedium.copyWith(
                  color: theme.hintColor,
                ),
                border: InputBorder.none,
                isDense: true,
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ),
          if (controller.text.isNotEmpty)
            GestureDetector(
              onTap: () {
                controller.clear();
                onChanged('');
                if (onClear != null) {
                  onClear!();
                }
              },
              child: Icon(
                Icons.clear,
                color: theme.hintColor,
                size: 20,
              ),
            ),
        ],
      ),
    );
  }
}