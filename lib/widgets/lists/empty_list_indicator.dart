import 'package:flutter/material.dart';
import '../../constants/text_styles.dart';

class EmptyListIndicator extends StatelessWidget {
  final String message;
  final IconData icon;
  final VoidCallback? onActionPressed;
  final String? actionLabel;

  const EmptyListIndicator({
    super.key,
    this.message = 'No items found',
    this.icon = Icons.inbox,
    this.onActionPressed,
    this.actionLabel,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: Theme.of(context).hintColor, // Replaced Colors.grey.shade400
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: TextStyles.titleMedium.copyWith(
              color: Theme.of(context).textTheme.titleMedium?.color?.withOpacity(0.7), // Replaced Colors.grey.shade600
            ),
            textAlign: TextAlign.center,
          ),
          if (onActionPressed != null && actionLabel != null) ...[
            const SizedBox(height: 16),
            TextButton.icon(
              onPressed: onActionPressed,
              icon: const Icon(Icons.add),
              label: Text(actionLabel!),
            ),
          ],
        ],
      ),
    );
  }
}