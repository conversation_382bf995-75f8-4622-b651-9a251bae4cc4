import 'package:flutter/material.dart';
import '../../theme/modern_theme.dart';

enum ModernButtonSize { small, medium, large }
enum ModernButtonVariant { filled, outlined, text, ghost }

class ModernButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final ModernButtonSize size;
  final ModernButtonVariant variant;
  final IconData? icon;
  final bool iconAfter;
  final bool isLoading;
  final bool isFullWidth;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final Color? borderColor;

  const ModernButton({
    Key? key,
    required this.text,
    this.onPressed,
    this.size = ModernButtonSize.medium,
    this.variant = ModernButtonVariant.filled,
    this.icon,
    this.iconAfter = false,
    this.isLoading = false,
    this.isFullWidth = false,
    this.backgroundColor,
    this.foregroundColor,
    this.borderColor,
  }) : super(key: key);

  @override
  State<ModernButton> createState() => _ModernButtonState();
}

class _ModernButtonState extends State<ModernButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: ModernTheme.fastAnimation,
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  EdgeInsets _getPadding() {
    switch (widget.size) {
      case ModernButtonSize.small:
        return const EdgeInsets.symmetric(
          horizontal: ModernTheme.spacingMd,
          vertical: ModernTheme.spacingSm,
        );
      case ModernButtonSize.medium:
        return const EdgeInsets.symmetric(
          horizontal: ModernTheme.spacingLg,
          vertical: ModernTheme.spacingMd,
        );
      case ModernButtonSize.large:
        return const EdgeInsets.symmetric(
          horizontal: ModernTheme.spacingXl,
          vertical: ModernTheme.spacingLg,
        );
    }
  }

  double _getFontSize() {
    switch (widget.size) {
      case ModernButtonSize.small:
        return 12;
      case ModernButtonSize.medium:
        return 14;
      case ModernButtonSize.large:
        return 16;
    }
  }

  double _getIconSize() {
    switch (widget.size) {
      case ModernButtonSize.small:
        return 16;
      case ModernButtonSize.medium:
        return 18;
      case ModernButtonSize.large:
        return 20;
    }
  }

  void _onTapDown(TapDownDetails details) {
    if (widget.onPressed != null && !widget.isLoading) {
      setState(() => _isPressed = true);
      _animationController.forward();
    }
  }

  void _onTapUp(TapUpDetails details) {
    _resetPress();
  }

  void _onTapCancel() {
    _resetPress();
  }

  void _resetPress() {
    if (_isPressed) {
      setState(() => _isPressed = false);
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isEnabled = widget.onPressed != null && !widget.isLoading;

    Widget buttonChild = Row(
      mainAxisSize: widget.isFullWidth ? MainAxisSize.max : MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (widget.icon != null && !widget.iconAfter) ...[
          Icon(
            widget.icon,
            size: _getIconSize(),
          ),
          const SizedBox(width: ModernTheme.spacingSm),
        ],
        if (widget.isLoading) ...[
          SizedBox(
            width: _getIconSize(),
            height: _getIconSize(),
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                widget.foregroundColor ?? theme.colorScheme.onPrimary,
              ),
            ),
          ),
          const SizedBox(width: ModernTheme.spacingSm),
        ],
        Text(
          widget.text,
          style: TextStyle(
            fontSize: _getFontSize(),
            fontWeight: FontWeight.w600,
            letterSpacing: 0.5,
          ),
        ),
        if (widget.icon != null && widget.iconAfter) ...[
          const SizedBox(width: ModernTheme.spacingSm),
          Icon(
            widget.icon,
            size: _getIconSize(),
          ),
        ],
      ],
    );

    Widget button;

    switch (widget.variant) {
      case ModernButtonVariant.filled:
        button = ElevatedButton(
          onPressed: isEnabled ? widget.onPressed : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: widget.backgroundColor,
            foregroundColor: widget.foregroundColor,
            padding: _getPadding(),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(ModernTheme.radiusMd),
            ),
            elevation: ModernTheme.elevationMd,
            shadowColor: theme.shadowColor.withOpacity(0.3),
          ),
          child: buttonChild,
        );
        break;

      case ModernButtonVariant.outlined:
        button = OutlinedButton(
          onPressed: isEnabled ? widget.onPressed : null,
          style: OutlinedButton.styleFrom(
            backgroundColor: widget.backgroundColor,
            foregroundColor: widget.foregroundColor ?? theme.colorScheme.primary,
            padding: _getPadding(),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(ModernTheme.radiusMd),
            ),
            side: BorderSide(
              color: widget.borderColor ?? theme.colorScheme.outline,
              width: 1.5,
            ),
          ),
          child: buttonChild,
        );
        break;

      case ModernButtonVariant.text:
        button = TextButton(
          onPressed: isEnabled ? widget.onPressed : null,
          style: TextButton.styleFrom(
            backgroundColor: widget.backgroundColor,
            foregroundColor: widget.foregroundColor ?? theme.colorScheme.primary,
            padding: _getPadding(),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(ModernTheme.radiusMd),
            ),
          ),
          child: buttonChild,
        );
        break;

      case ModernButtonVariant.ghost:
        button = Material(
          color: widget.backgroundColor ?? Colors.transparent,
          borderRadius: BorderRadius.circular(ModernTheme.radiusMd),
          child: InkWell(
            onTap: isEnabled ? widget.onPressed : null,
            borderRadius: BorderRadius.circular(ModernTheme.radiusMd),
            child: Container(
              padding: _getPadding(),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(ModernTheme.radiusMd),
                border: widget.borderColor != null
                    ? Border.all(color: widget.borderColor!)
                    : null,
              ),
              child: DefaultTextStyle(
                style: TextStyle(
                  color: widget.foregroundColor ?? theme.colorScheme.primary,
                  fontSize: _getFontSize(),
                  fontWeight: FontWeight.w600,
                  letterSpacing: 0.5,
                ),
                child: IconTheme(
                  data: IconThemeData(
                    color: widget.foregroundColor ?? theme.colorScheme.primary,
                    size: _getIconSize(),
                  ),
                  child: buttonChild,
                ),
              ),
            ),
          ),
        );
        break;
    }

    if (widget.isFullWidth) {
      button = SizedBox(
        width: double.infinity,
        child: button,
      );
    }

    return GestureDetector(
      onTapDown: _onTapDown,
      onTapUp: _onTapUp,
      onTapCancel: _onTapCancel,
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: button,
          );
        },
      ),
    );
  }
}

class ModernIconButton extends StatefulWidget {
  final IconData icon;
  final VoidCallback? onPressed;
  final String? tooltip;
  final ModernButtonSize size;
  final Color? backgroundColor;
  final Color? iconColor;
  final bool isSelected;

  const ModernIconButton({
    Key? key,
    required this.icon,
    this.onPressed,
    this.tooltip,
    this.size = ModernButtonSize.medium,
    this.backgroundColor,
    this.iconColor,
    this.isSelected = false,
  }) : super(key: key);

  @override
  State<ModernIconButton> createState() => _ModernIconButtonState();
}

class _ModernIconButtonState extends State<ModernIconButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: ModernTheme.fastAnimation,
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.9,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  double _getSize() {
    switch (widget.size) {
      case ModernButtonSize.small:
        return 32;
      case ModernButtonSize.medium:
        return 40;
      case ModernButtonSize.large:
        return 48;
    }
  }

  double _getIconSize() {
    switch (widget.size) {
      case ModernButtonSize.small:
        return 16;
      case ModernButtonSize.medium:
        return 20;
      case ModernButtonSize.large:
        return 24;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final size = _getSize();

    Widget button = Material(
      color: widget.backgroundColor ??
          (widget.isSelected
              ? theme.colorScheme.primaryContainer
              : theme.colorScheme.surfaceVariant.withOpacity(0.3)),
      borderRadius: BorderRadius.circular(ModernTheme.radiusMd),
      child: InkWell(
        onTap: widget.onPressed,
        onTapDown: (_) => _animationController.forward(),
        onTapUp: (_) => _animationController.reverse(),
        onTapCancel: () => _animationController.reverse(),
        borderRadius: BorderRadius.circular(ModernTheme.radiusMd),
        child: Container(
          width: size,
          height: size,
          alignment: Alignment.center,
          child: Icon(
            widget.icon,
            size: _getIconSize(),
            color: widget.iconColor ??
                (widget.isSelected
                    ? theme.colorScheme.onPrimaryContainer
                    : theme.colorScheme.onSurfaceVariant),
          ),
        ),
      ),
    );

    if (widget.tooltip != null) {
      button = Tooltip(
        message: widget.tooltip!,
        child: button,
      );
    }

    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: button,
        );
      },
    );
  }
}
