import 'package:flutter/material.dart';
import '../../theme/modern_theme.dart';

class ModernDrawer extends StatelessWidget {
  final String? userEmail;
  final String? userName;
  final String? userAvatar;
  final List<ModernDrawerItem> items;
  final VoidCallback? onThemeToggle;
  final bool isDarkMode;

  const ModernDrawer({
    Key? key,
    this.userEmail,
    this.userName,
    this.userAvatar,
    required this.items,
    this.onThemeToggle,
    this.isDarkMode = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Drawer(
      child: Column(
        children: [
          // Header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.fromLTRB(
              ModernTheme.spacingMd,
              ModernTheme.spacingXl + 24, // Account for status bar
              ModernTheme.spacingMd,
              ModernTheme.spacingMd,
            ),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  theme.colorScheme.primary,
                  theme.colorScheme.primary.withOpacity(0.8),
                ],
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Logo/Avatar
                Container(
                  width: 64,
                  height: 64,
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(ModernTheme.radiusXl),
                    border: Border.all(
                      color: Colors.white.withOpacity(0.3),
                      width: 2,
                    ),
                  ),
                  child: userAvatar != null
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(ModernTheme.radiusXl),
                          child: Image.network(
                            userAvatar!,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) =>
                                const Icon(
                              Icons.person,
                              color: Colors.white,
                              size: 32,
                            ),
                          ),
                        )
                      : const Icon(
                          Icons.sports_soccer,
                          color: Colors.white,
                          size: 32,
                        ),
                ),
                const SizedBox(height: ModernTheme.spacingMd),
                
                // User Info
                Text(
                  userName ?? 'Hemma Sports Academy',
                  style: theme.textTheme.titleLarge?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                if (userEmail != null) ...[
                  const SizedBox(height: ModernTheme.spacingXs),
                  Text(
                    userEmail!,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: Colors.white.withOpacity(0.8),
                    ),
                  ),
                ],
              ],
            ),
          ),

          // Menu Items
          Expanded(
            child: ListView(
              padding: const EdgeInsets.symmetric(vertical: ModernTheme.spacingMd),
              children: [
                ...items.map((item) => _buildDrawerItem(context, item)),
                
                const Divider(
                  height: ModernTheme.spacingXl,
                  indent: ModernTheme.spacingMd,
                  endIndent: ModernTheme.spacingMd,
                ),

                // Theme Toggle
                if (onThemeToggle != null)
                  _buildDrawerItem(
                    context,
                    ModernDrawerItem(
                      icon: isDarkMode ? Icons.light_mode : Icons.dark_mode,
                      title: isDarkMode ? 'Light Mode' : 'Dark Mode',
                      onTap: onThemeToggle!,
                    ),
                  ),
              ],
            ),
          ),

          // Footer
          Container(
            padding: const EdgeInsets.all(ModernTheme.spacingMd),
            decoration: BoxDecoration(
              border: Border(
                top: BorderSide(
                  color: theme.colorScheme.outline.withOpacity(0.2),
                ),
              ),
            ),
            child: Column(
              children: [
                Text(
                  'Hemma ERP v1.0.0',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
                const SizedBox(height: ModernTheme.spacingXs),
                Text(
                  '© 2024 Hemma Sports Academy',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDrawerItem(BuildContext context, ModernDrawerItem item) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: ModernTheme.spacingMd,
        vertical: ModernTheme.spacingXs,
      ),
      child: Material(
        color: item.isSelected
            ? theme.colorScheme.primaryContainer.withOpacity(0.3)
            : Colors.transparent,
        borderRadius: BorderRadius.circular(ModernTheme.radiusMd),
        child: InkWell(
          onTap: item.onTap,
          borderRadius: BorderRadius.circular(ModernTheme.radiusMd),
          child: Container(
            padding: const EdgeInsets.symmetric(
              horizontal: ModernTheme.spacingMd,
              vertical: ModernTheme.spacingMd,
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(ModernTheme.spacingSm),
                  decoration: BoxDecoration(
                    color: item.isSelected
                        ? theme.colorScheme.primary.withOpacity(0.1)
                        : theme.colorScheme.surfaceVariant.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(ModernTheme.radiusMd),
                  ),
                  child: Icon(
                    item.icon,
                    size: 20,
                    color: item.isSelected
                        ? theme.colorScheme.primary
                        : theme.colorScheme.onSurfaceVariant,
                  ),
                ),
                const SizedBox(width: ModernTheme.spacingMd),
                Expanded(
                  child: Text(
                    item.title,
                    style: theme.textTheme.bodyLarge?.copyWith(
                      fontWeight: item.isSelected ? FontWeight.w600 : FontWeight.w400,
                      color: item.isSelected
                          ? theme.colorScheme.onSurface
                          : theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ),
                if (item.badge != null) ...[
                  const SizedBox(width: ModernTheme.spacingSm),
                  item.badge!,
                ],
                if (item.trailing != null) ...[
                  const SizedBox(width: ModernTheme.spacingSm),
                  item.trailing!,
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class ModernDrawerItem {
  final IconData icon;
  final String title;
  final VoidCallback onTap;
  final bool isSelected;
  final Widget? badge;
  final Widget? trailing;

  const ModernDrawerItem({
    required this.icon,
    required this.title,
    required this.onTap,
    this.isSelected = false,
    this.badge,
    this.trailing,
  });
}

class ModernDrawerSection extends StatelessWidget {
  final String title;
  final List<ModernDrawerItem> items;

  const ModernDrawerSection({
    Key? key,
    required this.title,
    required this.items,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(
            ModernTheme.spacingLg,
            ModernTheme.spacingMd,
            ModernTheme.spacingMd,
            ModernTheme.spacingSm,
          ),
          child: Text(
            title.toUpperCase(),
            style: theme.textTheme.labelSmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
              fontWeight: FontWeight.w600,
              letterSpacing: 1.2,
            ),
          ),
        ),
        ...items.map((item) => _buildDrawerItem(context, item)),
      ],
    );
  }

  Widget _buildDrawerItem(BuildContext context, ModernDrawerItem item) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: ModernTheme.spacingMd,
        vertical: ModernTheme.spacingXs,
      ),
      child: Material(
        color: item.isSelected
            ? theme.colorScheme.primaryContainer.withOpacity(0.3)
            : Colors.transparent,
        borderRadius: BorderRadius.circular(ModernTheme.radiusMd),
        child: InkWell(
          onTap: item.onTap,
          borderRadius: BorderRadius.circular(ModernTheme.radiusMd),
          child: Container(
            padding: const EdgeInsets.symmetric(
              horizontal: ModernTheme.spacingMd,
              vertical: ModernTheme.spacingMd,
            ),
            child: Row(
              children: [
                Icon(
                  item.icon,
                  size: 20,
                  color: item.isSelected
                      ? theme.colorScheme.primary
                      : theme.colorScheme.onSurfaceVariant,
                ),
                const SizedBox(width: ModernTheme.spacingMd),
                Expanded(
                  child: Text(
                    item.title,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: item.isSelected ? FontWeight.w600 : FontWeight.w400,
                      color: item.isSelected
                          ? theme.colorScheme.onSurface
                          : theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ),
                if (item.badge != null) ...[
                  const SizedBox(width: ModernTheme.spacingSm),
                  item.badge!,
                ],
                if (item.trailing != null) ...[
                  const SizedBox(width: ModernTheme.spacingSm),
                  item.trailing!,
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}
