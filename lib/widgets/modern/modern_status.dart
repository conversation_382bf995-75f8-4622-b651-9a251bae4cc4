import 'package:flutter/material.dart';
import '../../theme/modern_theme.dart';

enum StatusType { success, warning, error, info, neutral }

class ModernStatusBadge extends StatelessWidget {
  final String text;
  final StatusType type;
  final bool isOutlined;
  final IconData? icon;

  const ModernStatusBadge({
    Key? key,
    required this.text,
    required this.type,
    this.isOutlined = false,
    this.icon,
  }) : super(key: key);

  Color _getBackgroundColor(BuildContext context) {
    final theme = Theme.of(context);
    if (isOutlined) return Colors.transparent;

    switch (type) {
      case StatusType.success:
        return ModernTheme.successGreen.withOpacity(0.1);
      case StatusType.warning:
        return ModernTheme.warningAmber.withOpacity(0.1);
      case StatusType.error:
        return ModernTheme.errorRed.withOpacity(0.1);
      case StatusType.info:
        return ModernTheme.infoBlue.withOpacity(0.1);
      case StatusType.neutral:
        return theme.colorScheme.surfaceVariant;
    }
  }

  Color _getTextColor(BuildContext context) {
    final theme = Theme.of(context);
    switch (type) {
      case StatusType.success:
        return ModernTheme.successGreen;
      case StatusType.warning:
        return ModernTheme.warningAmber;
      case StatusType.error:
        return ModernTheme.errorRed;
      case StatusType.info:
        return ModernTheme.infoBlue;
      case StatusType.neutral:
        return theme.colorScheme.onSurfaceVariant;
    }
  }

  Color _getBorderColor(BuildContext context) {
    if (!isOutlined) return Colors.transparent;
    return _getTextColor(context).withOpacity(0.3);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: ModernTheme.spacingSm,
        vertical: ModernTheme.spacingXs,
      ),
      decoration: BoxDecoration(
        color: _getBackgroundColor(context),
        borderRadius: BorderRadius.circular(ModernTheme.radiusFull),
        border: Border.all(
          color: _getBorderColor(context),
          width: isOutlined ? 1 : 0,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (icon != null) ...[
            Icon(
              icon,
              size: 12,
              color: _getTextColor(context),
            ),
            const SizedBox(width: ModernTheme.spacingXs),
          ],
          Text(
            text,
            style: theme.textTheme.labelSmall?.copyWith(
              color: _getTextColor(context),
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}

class ModernProgressIndicator extends StatelessWidget {
  final double value;
  final String? label;
  final Color? color;
  final double height;
  final bool showPercentage;

  const ModernProgressIndicator({
    Key? key,
    required this.value,
    this.label,
    this.color,
    this.height = 8,
    this.showPercentage = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final progressColor = color ?? theme.colorScheme.primary;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (label != null || showPercentage) ...[
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              if (label != null)
                Text(
                  label!,
                  style: theme.textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              if (showPercentage)
                Text(
                  '${(value * 100).round()}%',
                  style: theme.textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: progressColor,
                  ),
                ),
            ],
          ),
          const SizedBox(height: ModernTheme.spacingXs),
        ],
        Container(
          height: height,
          decoration: BoxDecoration(
            color: theme.colorScheme.surfaceVariant,
            borderRadius: BorderRadius.circular(height / 2),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(height / 2),
            child: LinearProgressIndicator(
              value: value,
              backgroundColor: Colors.transparent,
              valueColor: AlwaysStoppedAnimation<Color>(progressColor),
            ),
          ),
        ),
      ],
    );
  }
}

class ModernLoadingIndicator extends StatelessWidget {
  final String? message;
  final double size;
  final Color? color;

  const ModernLoadingIndicator({
    Key? key,
    this.message,
    this.size = 24,
    this.color,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: size,
          height: size,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(
              color ?? theme.colorScheme.primary,
            ),
          ),
        ),
        if (message != null) ...[
          const SizedBox(height: ModernTheme.spacingMd),
          Text(
            message!,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }
}

class ModernEmptyState extends StatelessWidget {
  final IconData icon;
  final String title;
  final String? description;
  final Widget? action;
  final Color? iconColor;

  const ModernEmptyState({
    Key? key,
    required this.icon,
    required this.title,
    this.description,
    this.action,
    this.iconColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(ModernTheme.spacingXl),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(ModernTheme.spacingLg),
              decoration: BoxDecoration(
                color: (iconColor ?? theme.colorScheme.primary).withOpacity(0.1),
                borderRadius: BorderRadius.circular(ModernTheme.radius2xl),
              ),
              child: Icon(
                icon,
                size: 48,
                color: iconColor ?? theme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: ModernTheme.spacingLg),
            Text(
              title,
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            if (description != null) ...[
              const SizedBox(height: ModernTheme.spacingSm),
              Text(
                description!,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
              ),
            ],
            if (action != null) ...[
              const SizedBox(height: ModernTheme.spacingLg),
              action!,
            ],
          ],
        ),
      ),
    );
  }
}

class ModernErrorState extends StatelessWidget {
  final String title;
  final String? description;
  final VoidCallback? onRetry;
  final String retryText;

  const ModernErrorState({
    Key? key,
    required this.title,
    this.description,
    this.onRetry,
    this.retryText = 'Try Again',
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(ModernTheme.spacingXl),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(ModernTheme.spacingLg),
              decoration: BoxDecoration(
                color: ModernTheme.errorRed.withOpacity(0.1),
                borderRadius: BorderRadius.circular(ModernTheme.radius2xl),
              ),
              child: Icon(
                Icons.error_outline,
                size: 48,
                color: ModernTheme.errorRed,
              ),
            ),
            const SizedBox(height: ModernTheme.spacingLg),
            Text(
              title,
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: ModernTheme.errorRed,
              ),
              textAlign: TextAlign.center,
            ),
            if (description != null) ...[
              const SizedBox(height: ModernTheme.spacingSm),
              Text(
                description!,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
              ),
            ],
            if (onRetry != null) ...[
              const SizedBox(height: ModernTheme.spacingLg),
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh),
                label: Text(retryText),
                style: ElevatedButton.styleFrom(
                  backgroundColor: ModernTheme.errorRed,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

class ModernToast {
  static void show(
    BuildContext context, {
    required String message,
    StatusType type = StatusType.info,
    Duration duration = const Duration(seconds: 3),
    IconData? icon,
  }) {
    Color backgroundColor;
    Color textColor;
    IconData defaultIcon;

    switch (type) {
      case StatusType.success:
        backgroundColor = ModernTheme.successGreen;
        textColor = Colors.white;
        defaultIcon = Icons.check_circle;
        break;
      case StatusType.warning:
        backgroundColor = ModernTheme.warningAmber;
        textColor = Colors.white;
        defaultIcon = Icons.warning;
        break;
      case StatusType.error:
        backgroundColor = ModernTheme.errorRed;
        textColor = Colors.white;
        defaultIcon = Icons.error;
        break;
      case StatusType.info:
        backgroundColor = ModernTheme.infoBlue;
        textColor = Colors.white;
        defaultIcon = Icons.info;
        break;
      case StatusType.neutral:
        backgroundColor = Theme.of(context).colorScheme.inverseSurface;
        textColor = Theme.of(context).colorScheme.onInverseSurface;
        defaultIcon = Icons.notifications;
        break;
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              icon ?? defaultIcon,
              color: textColor,
              size: 20,
            ),
            const SizedBox(width: ModernTheme.spacingSm),
            Expanded(
              child: Text(
                message,
                style: TextStyle(
                  color: textColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: backgroundColor,
        duration: duration,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(ModernTheme.radiusMd),
        ),
        margin: const EdgeInsets.all(ModernTheme.spacingMd),
      ),
    );
  }
}
