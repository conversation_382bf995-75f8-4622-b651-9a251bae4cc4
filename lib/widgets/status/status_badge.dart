// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import '../../constants/text_styles.dart';
import '../../constants/colors.dart'; // Import ModernAppColors
class StatusBadge extends StatelessWidget {
  final String text;
  final Color color;
  final Color? textColor;
  final double fontSize;
  final EdgeInsetsGeometry padding;
  final BorderRadius borderRadius;

  const StatusBadge({
    super.key,
    required this.text,
    required this.color,
    this.textColor,
    this.fontSize = 12,
    this.padding = const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
    this.borderRadius = const BorderRadius.all(Radius.circular(12)),
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding,
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: borderRadius,
        border: Border.all(color: color),
      ),
      child: Text(
        text,
        style: TextStyles.bodySmall.copyWith(
          color: textColor ?? color,
          fontSize: fontSize,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  factory StatusBadge.success({
    required String text,
    Color color = ModernAppColors.success, // Replaced Colors.green
    Color? textColor,
    double fontSize = 12,
    EdgeInsetsGeometry padding = const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
    BorderRadius borderRadius = const BorderRadius.all(Radius.circular(12)),
  }) {
    return StatusBadge(
      text: text,
      color: color,
      textColor: textColor,
      fontSize: fontSize,
      padding: padding,
      borderRadius: borderRadius,
    );
  }

  factory StatusBadge.warning({
    required String text,
    Color color = ModernAppColors.warning, // Replaced Colors.orange
    Color? textColor,
    double fontSize = 12,
    EdgeInsetsGeometry padding = const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
    BorderRadius borderRadius = const BorderRadius.all(Radius.circular(12)),
  }) {
    return StatusBadge(
      text: text,
      color: color,
      textColor: textColor,
      fontSize: fontSize,
      padding: padding,
      borderRadius: borderRadius,
    );
  }

  factory StatusBadge.error({
    required String text,
    Color color = ModernAppColors.error, // Replaced Colors.red
    Color? textColor,
    double fontSize = 12,
    EdgeInsetsGeometry padding = const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
    BorderRadius borderRadius = const BorderRadius.all(Radius.circular(12)),
  }) {
    return StatusBadge(
      text: text,
      color: color,
      textColor: textColor,
      fontSize: fontSize,
      padding: padding,
      borderRadius: borderRadius,
    );
  }
  
  factory StatusBadge.info({
    required String text,
    Color color = ModernAppColors.info, // Replaced Colors.blue
    Color? textColor,
    double fontSize = 12,
    EdgeInsetsGeometry padding = const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
    BorderRadius borderRadius = const BorderRadius.all(Radius.circular(12)),
  }) {
    return StatusBadge(
      text: text,
      color: color,
      textColor: textColor,
      fontSize: fontSize,
      padding: padding,
      borderRadius: borderRadius,
    );
  }
}