// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import '../../constants/text_styles.dart';

class DataTableWidget<T> extends StatelessWidget {
  final List<DataColumn> columns;
  final List<T> data;
  final DataRow Function(T item, int index) buildRow;
  final bool isLoading;
  final String emptyMessage;
  final double? horizontalScrollOffset;
  final Function(double)? onHorizontalScroll;
  final bool showBorder;
  final double rowHeight;
  final Color? headerColor;
  final bool showCheckboxColumn;
  final List<int>? selectedRows;
  final Function(bool?, int)? onSelectRow;
  final Function(bool?)? onSelectAll;

  const DataTableWidget({
    super.key,
    required this.columns,
    required this.data,
    required this.buildRow,
    this.isLoading = false,
    this.emptyMessage = 'No data available',
    this.horizontalScrollOffset,
    this.onHorizontalScroll,
    this.showBorder = true,
    this.rowHeight = 56.0,
    this.headerColor,
    this.showCheckboxColumn = false,
    this.selectedRows,
    this.onSelectRow,
    this.onSelectAll,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    if (isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (data.isEmpty) {
      return Center(
        child: Text(
          emptyMessage,
          style: TextStyles.bodyMedium.copyWith(
            color: theme.hintColor,
          ),
        ),
      );
    }

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      controller: horizontalScrollOffset != null
          ? ScrollController(initialScrollOffset: horizontalScrollOffset!)
          : null,
      child: Container(
        decoration: showBorder
            ? BoxDecoration(
                border: Border.all(color: theme.dividerColor),
                borderRadius: BorderRadius.circular(8),
              )
            : null,
        child: DataTable(
          columns: columns,
          rows: List.generate(
            data.length,
            (index) => buildRow(data[index], index),
          ),
          headingRowColor: MaterialStateProperty.all(
            headerColor ?? theme.colorScheme.surface,
          ),
          dataRowMinHeight: rowHeight,
          dataRowMaxHeight: rowHeight,
          dividerThickness: 1,
          showCheckboxColumn: showCheckboxColumn,
          onSelectAll: onSelectAll,
        ),
      ),
    );
  }
}