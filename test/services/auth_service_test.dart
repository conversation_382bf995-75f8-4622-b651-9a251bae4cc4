// ignore_for_file: avoid_relative_lib_imports

import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../lib/services/auth_service.dart';
import '../../lib/services/secure_api_service.dart';
import '../../lib/utils/validators.dart';
import '../../lib/utils/security_utils.dart';

void main() {
  group('Validator Tests', () {
    group('Email Validation', () {
      test('should return null for valid email', () {
        expect(CustomerValidators.validateEmail('<EMAIL>'), null);
        expect(
          CustomerValidators.validateEmail('<EMAIL>'),
          null,
        );
      });

      test('should return error for invalid email', () {
        expect(CustomerValidators.validateEmail('invalid-email'), isNotNull);
        expect(CustomerValidators.validateEmail(''), isNotNull);
        expect(CustomerValidators.validateEmail('@domain.com'), isNotNull);
      });
    });

    group('Password Validation', () {
      test('should return null for strong password', () {
        expect(CustomerValidators.validatePassword('StrongPass123!'), null);
      });

      test('should return error for weak password', () {
        expect(CustomerValidators.validatePassword('weak'), isNotNull);
        expect(CustomerValidators.validatePassword(''), isNotNull);
        expect(
          CustomerValidators.validatePassword('12345678'),
          isNotNull,
        ); // No uppercase
        expect(
          CustomerValidators.validatePassword('PASSWORD123!'),
          isNotNull,
        ); // No lowercase
      });
    });

    group('Input Sanitization', () {
      test('should sanitize dangerous characters', () {
        final input = '<script>alert("xss")</script>';
        final sanitized = CustomerValidators.sanitizeInput(input);
        expect(
          sanitized,
          contains('&amp;lt;'),
        ); // Double encoding due to & being escaped first
        expect(sanitized, contains('&amp;gt;'));
      });

      test('should handle empty input', () {
        expect(CustomerValidators.sanitizeInput(''), '');
      });
    });
  });

  group('Security Utils Tests', () {
    group('JWT Token Validation', () {
      test('should validate proper JWT token format', () {
        // This is a valid JWT token with proper header and payload structure
        // Header: {"alg":"HS256","typ":"JWT"}
        // Payload: {"sub":"1234567890","name":"John Doe","iat":1516239022,"exp":9999999999}
        final validToken =
            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjk5OTk5OTk5OTl9.Lm_8cLKaLzMhKdJhQoJoJoJoJoJoJoJoJoJoJoJoJoJ';
        expect(SecurityUtils.isValidSessionToken(validToken), true);
      });

      test('should reject invalid token format', () {
        expect(SecurityUtils.isValidSessionToken('invalid-token'), false);
        expect(SecurityUtils.isValidSessionToken(''), false);
        expect(SecurityUtils.isValidSessionToken('a.b'), false); // Only 2 parts
      });
    });

    group('Data Masking', () {
      test('should mask email addresses', () {
        final masked = SecurityUtils.maskSensitiveData('<EMAIL>');
        expect(masked, contains('us***@example.com'));
      });

      test('should mask phone numbers', () {
        final masked = SecurityUtils.maskSensitiveData('1234567890');
        expect(masked, contains('***7890'));
      });

      test('should mask general data', () {
        final masked = SecurityUtils.maskSensitiveData('sensitive-data');
        expect(masked, contains('se***ta'));
      });
    });

    group('Security Threat Detection', () {
      test('should detect SQL injection patterns', () {
        expect(
          SecurityUtils.containsSecurityThreats('SELECT * FROM users'),
          true,
        );
        expect(SecurityUtils.containsSecurityThreats('DROP TABLE users'), true);
        expect(SecurityUtils.containsSecurityThreats('normal text'), false);
      });

      test('should detect XSS patterns', () {
        expect(
          SecurityUtils.containsSecurityThreats(
            '<script>alert("xss")</script>',
          ),
          true,
        );
        expect(
          SecurityUtils.containsSecurityThreats('javascript:alert("xss")'),
          true,
        );
        expect(SecurityUtils.containsSecurityThreats('normal text'), false);
      });
    });
  });

  group('UserData Tests', () {
    test('should create UserData from valid JSON', () {
      final json = {
        'id': '123',
        'email': '<EMAIL>',
        'name': 'Test User',
        'role': 'admin',
        'permissions': ['read', 'write'],
      };

      final userData = UserData.fromJson(json);

      expect(userData.id, '123');
      expect(userData.email, '<EMAIL>');
      expect(userData.name, 'Test User');
      expect(userData.role, 'admin');
      expect(userData.permissions, ['read', 'write']);
    });

    test('should throw error for missing required fields', () {
      final json = {
        'email': '<EMAIL>',
        'name': 'Test User',
        // Missing 'id'
      };

      expect(() => UserData.fromJson(json), throwsArgumentError);
    });

    test('should check permissions correctly', () {
      final userData = UserData(
        id: '123',
        email: '<EMAIL>',
        name: 'Test User',
        role: 'user',
        permissions: ['read'],
      );

      expect(userData.hasPermission('read'), true);
      expect(userData.hasPermission('write'), false);
    });

    test('should grant all permissions to admin', () {
      final userData = UserData(
        id: '123',
        email: '<EMAIL>',
        name: 'Admin User',
        role: 'admin',
        permissions: [],
      );

      expect(userData.hasPermission('read'), true);
      expect(userData.hasPermission('write'), true);
      expect(userData.hasPermission('delete'), true);
    });
  });

  group('AuthResult Tests', () {
    test('should create success result', () {
      final userData = UserData(
        id: '123',
        email: '<EMAIL>',
        name: 'Test User',
        role: 'user',
        permissions: [],
      );

      final result = AuthResult.success(userData);

      expect(result.success, true);
      expect(result.error, null);
      expect(result.user, userData);
    });

    test('should create failure result', () {
      final result = AuthResult.failure('Login failed');

      expect(result.success, false);
      expect(result.error, 'Login failed');
      expect(result.user, null);
    });
  });

  group('Basic AuthService Tests', () {
    late AuthService authService;
    late SecureApiService apiService;

    setUp(() {
      apiService = SecureApiService(baseUrl: 'https://test.example.com');
      authService = AuthService(apiService);
      SharedPreferences.setMockInitialValues({});
    });

    test('should return false for isAuthenticated when no user', () {
      expect(authService.isAuthenticated, false);
    });

    test('should return null for currentUser when no user', () {
      expect(authService.currentUser, null);
    });

    test('should return failure for invalid email in login', () async {
      final result = await authService.login('invalid-email', 'password123');

      expect(result.success, false);
      expect(result.error, contains('valid email'));
    });

    test('should return failure for empty password in login', () async {
      final result = await authService.login('<EMAIL>', '');

      expect(result.success, false);
      expect(result.error, 'Password is required');
    });

    test('should return failure for short password in login', () async {
      final result = await authService.login('<EMAIL>', '123');

      expect(result.success, false);
      expect(result.error, contains('at least 6 characters'));
    });

    test(
      'should return failure when changing password without authentication',
      () async {
        final result = await authService.changePassword(
          'old123',
          'NewPassword123!',
        );

        expect(result.success, false);
        expect(result.error, 'User not authenticated');
      },
    );

    test('should fail to refresh token when no refresh token stored', () async {
      final result = await authService.refreshAuthToken();

      expect(result, false);
    });

    test('should fail to initialize auth with no stored data', () async {
      final result = await authService.initializeAuth();

      expect(result, false);
    });
  });
}
