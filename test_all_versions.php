<?php
/**
 * Test script to verify all API versions work correctly
 */

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

header("Content-Type: application/json; charset=UTF-8");

echo "=== API Version Testing ===\n\n";

// Test database connection first
echo "1. Testing database connection...\n";
try {
    $conn = new mysqli('localhost', 'u709340751_erp', '1234567890CodePoint', 'u709340751_erp_final');
    
    if ($conn->connect_error) {
        throw new Exception("Connection failed: " . $conn->connect_error);
    }
    
    echo json_encode(['status' => 'success', 'message' => 'Database connection successful']) . "\n";
    
    // Test if tables exist
    $tables = ['customers', 'customer_sports'];
    foreach ($tables as $table) {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        if ($result && $result->num_rows > 0) {
            echo json_encode(['status' => 'success', 'message' => "Table '$table' exists"]) . "\n";
        } else {
            echo json_encode(['status' => 'warning', 'message' => "Table '$table' not found"]) . "\n";
        }
    }
    
    // Test a simple query
    $result = $conn->query("SELECT COUNT(*) as count FROM customer_sports");
    if ($result) {
        $row = $result->fetch_assoc();
        echo json_encode(['status' => 'success', 'message' => "Found {$row['count']} records in customer_sports"]) . "\n";
    }
    
    $conn->close();
    
} catch (Exception $e) {
    echo json_encode(['status' => 'error', 'message' => 'Database error: ' . $e->getMessage()]) . "\n";
    exit;
}

echo "\n2. Testing simple version...\n";
// Test the simple version by including it
ob_start();
try {
    // Simulate GET request
    $_SERVER['REQUEST_METHOD'] = 'GET';
    $_GET = []; // Empty GET for basic test
    
    include 'trainer_trainees_simple.php';
    $output = ob_get_contents();
    ob_end_clean();
    
    $decoded = json_decode($output, true);
    if ($decoded && isset($decoded['status'])) {
        echo json_encode(['status' => 'success', 'message' => 'Simple version works', 'api_status' => $decoded['status']]) . "\n";
    } else {
        echo json_encode(['status' => 'error', 'message' => 'Simple version failed', 'output' => substr($output, 0, 200)]) . "\n";
    }
} catch (Exception $e) {
    ob_end_clean();
    echo json_encode(['status' => 'error', 'message' => 'Simple version error: ' . $e->getMessage()]) . "\n";
}

echo "\n3. Testing fixed version...\n";
// Test the fixed version
ob_start();
try {
    // Reset superglobals
    $_SERVER['REQUEST_METHOD'] = 'GET';
    $_GET = [];
    
    include 'trainer_trainees_fixed.php';
    $output = ob_get_contents();
    ob_end_clean();
    
    $decoded = json_decode($output, true);
    if ($decoded && isset($decoded['status'])) {
        echo json_encode(['status' => 'success', 'message' => 'Fixed version works', 'api_status' => $decoded['status']]) . "\n";
    } else {
        echo json_encode(['status' => 'error', 'message' => 'Fixed version failed', 'output' => substr($output, 0, 200)]) . "\n";
    }
} catch (Exception $e) {
    ob_end_clean();
    echo json_encode(['status' => 'error', 'message' => 'Fixed version error: ' . $e->getMessage()]) . "\n";
}

echo "\n4. Testing final version with config...\n";
// Test the final version with config
ob_start();
try {
    // Reset superglobals
    $_SERVER['REQUEST_METHOD'] = 'GET';
    $_GET = [];
    
    include 'trainer_trainees_final.php';
    $output = ob_get_contents();
    ob_end_clean();
    
    $decoded = json_decode($output, true);
    if ($decoded && isset($decoded['status'])) {
        echo json_encode(['status' => 'success', 'message' => 'Final version works', 'api_status' => $decoded['status']]) . "\n";
    } else {
        echo json_encode(['status' => 'error', 'message' => 'Final version failed', 'output' => substr($output, 0, 200)]) . "\n";
    }
} catch (Exception $e) {
    ob_end_clean();
    echo json_encode(['status' => 'error', 'message' => 'Final version error: ' . $e->getMessage()]) . "\n";
}

echo "\n=== Testing Complete ===\n";
echo "Recommendation: Use the version that shows 'success' status.\n";
echo "If all fail, check database credentials and table structure.\n";
?>
