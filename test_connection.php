<?php
/**
 * Simple test script to diagnose the connection issue
 */

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

header("Content-Type: application/json; charset=UTF-8");

echo json_encode([
    'status' => 'info',
    'message' => 'Starting diagnostic test',
    'timestamp' => date('Y-m-d H:i:s')
]) . "\n\n";

// Test 1: Check if config file exists
echo "Test 1: Checking config file...\n";
if (file_exists('config.php')) {
    echo json_encode(['status' => 'success', 'message' => 'config.php exists']) . "\n";
} else {
    echo json_encode(['status' => 'error', 'message' => 'config.php not found']) . "\n";
    exit;
}

// Test 2: Try to include config
echo "\nTest 2: Loading config file...\n";
define('API_ACCESS', true);
try {
    require_once 'config.php';
    echo json_encode(['status' => 'success', 'message' => 'config.php loaded successfully']) . "\n";
} catch (Exception $e) {
    echo json_encode(['status' => 'error', 'message' => 'Error loading config: ' . $e->getMessage()]) . "\n";
    exit;
}

// Test 3: Check if constants are defined
echo "\nTest 3: Checking database constants...\n";
$constants = ['DB_HOST', 'DB_USER', 'DB_PASS', 'DB_NAME', 'DB_CHARSET'];
foreach ($constants as $const) {
    if (defined($const)) {
        echo json_encode(['status' => 'success', 'message' => "$const is defined"]) . "\n";
    } else {
        echo json_encode(['status' => 'error', 'message' => "$const is not defined"]) . "\n";
    }
}

// Test 4: Test database connection
echo "\nTest 4: Testing database connection...\n";
try {
    $conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
    
    if ($conn->connect_error) {
        throw new Exception("Connection failed: " . $conn->connect_error);
    }
    
    echo json_encode(['status' => 'success', 'message' => 'Database connection successful']) . "\n";
    
    // Test 5: Test a simple query
    echo "\nTest 5: Testing simple query...\n";
    $result = $conn->query("SELECT 1 as test");
    if ($result) {
        echo json_encode(['status' => 'success', 'message' => 'Simple query successful']) . "\n";
    } else {
        echo json_encode(['status' => 'error', 'message' => 'Simple query failed: ' . $conn->error]) . "\n";
    }
    
    // Test 6: Check if tables exist
    echo "\nTest 6: Checking if required tables exist...\n";
    $tables = ['customers', 'customer_sports'];
    foreach ($tables as $table) {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        if ($result && $result->num_rows > 0) {
            echo json_encode(['status' => 'success', 'message' => "Table '$table' exists"]) . "\n";
        } else {
            echo json_encode(['status' => 'warning', 'message' => "Table '$table' not found"]) . "\n";
        }
    }
    
    $conn->close();
    
} catch (Exception $e) {
    echo json_encode(['status' => 'error', 'message' => 'Database connection error: ' . $e->getMessage()]) . "\n";
}

echo "\nDiagnostic test completed.\n";
?>
