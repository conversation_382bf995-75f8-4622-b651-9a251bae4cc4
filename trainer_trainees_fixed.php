<?php
/**
 * Trainer Trainees API - Fixed Version
 * 
 * This version addresses all potential issues and provides comprehensive error handling
 * 
 * <AUTHOR> Name
 * @version 1.3
 */

// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Set headers early
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With");
header("Content-Type: application/json; charset=UTF-8");

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

/**
 * Configuration Class
 */
class Config {
    // Database Configuration
    const DB_HOST = 'localhost';
    const DB_USER = 'u709340751_erp';
    const DB_PASS = '1234567890CodePoint';
    const DB_NAME = 'u709340751_erp_final';
    const DB_CHARSET = 'utf8mb4';
    
    // Application Configuration
    const APP_DEBUG = true;
    const MAX_QUERY_RESULTS = 1000;
    const LOG_ERRORS = true;
    
    public static function isDebug() {
        return self::APP_DEBUG;
    }
}

/**
 * Database Connection Manager
 */
class DatabaseManager {
    private static $instance = null;
    private $connection;
    
    private function __construct() {
        try {
            $this->connection = new mysqli(
                Config::DB_HOST, 
                Config::DB_USER, 
                Config::DB_PASS, 
                Config::DB_NAME
            );
            
            if ($this->connection->connect_error) {
                throw new Exception("Database connection failed: " . $this->connection->connect_error);
            }
            
            $this->connection->set_charset(Config::DB_CHARSET);
            
        } catch (Exception $e) {
            error_log("Database connection error: " . $e->getMessage());
            throw $e;
        }
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function getConnection() {
        return $this->connection;
    }
    
    public function close() {
        if ($this->connection) {
            $this->connection->close();
        }
    }
    
    // Prevent cloning and serialization
    private function __clone() {}
    public function __wakeup() {
        throw new Exception("Cannot unserialize singleton");
    }
}

/**
 * API Response Handler
 */
class ApiResponse {
    public static function success($data, $message = "Success", $meta = []) {
        $response = [
            "status" => "success",
            "message" => $message,
            "data" => $data,
            "timestamp" => date('Y-m-d H:i:s'),
            "count" => is_array($data) ? count($data) : 1
        ];
        
        if (!empty($meta)) {
            $response["meta"] = $meta;
        }
        
        return $response;
    }
    
    public static function error($message, $code = 500, $details = null) {
        http_response_code($code);
        $response = [
            "status" => "error",
            "message" => $message,
            "timestamp" => date('Y-m-d H:i:s'),
            "error_code" => $code
        ];
        
        if ($details && Config::isDebug()) {
            $response["details"] = $details;
        }
        
        return $response;
    }
}

/**
 * Input Validator
 */
class InputValidator {
    public static function validateAndSanitize($input) {
        $allowedFilters = ['trainer_name', 'sport_name', 'customer_id', 'customer_name', 'subscription_type'];
        $validatedFilters = [];
        
        foreach ($allowedFilters as $filter) {
            if (isset($input[$filter]) && !empty(trim($input[$filter]))) {
                $value = trim($input[$filter]);
                
                if ($filter === 'customer_id') {
                    if (!is_numeric($value) || $value <= 0) {
                        throw new InvalidArgumentException("Invalid customer_id: must be a positive integer");
                    }
                    $validatedFilters[$filter] = (int)$value;
                } else {
                    $validatedFilters[$filter] = htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
                }
            }
        }
        
        // Validate pagination parameters
        $limit = isset($input['limit']) ? (int)$input['limit'] : null;
        $offset = isset($input['offset']) ? (int)$input['offset'] : 0;
        
        if ($limit !== null && ($limit <= 0 || $limit > Config::MAX_QUERY_RESULTS)) {
            throw new InvalidArgumentException("Invalid limit: must be between 1 and " . Config::MAX_QUERY_RESULTS);
        }
        
        if ($offset < 0) {
            throw new InvalidArgumentException("Invalid offset: must be non-negative");
        }
        
        return [
            'filters' => $validatedFilters,
            'limit' => $limit,
            'offset' => $offset
        ];
    }
}

/**
 * Trainer Trainees Service
 */
class TrainerTraineesService {
    private $conn;
    
    public function __construct($connection) {
        $this->conn = $connection;
    }
    
    /**
     * Get trainer-trainee relationships with filtering
     */
    public function getTrainerTrainees($filters = [], $limit = null, $offset = 0) {
        try {
            $sql = "SELECT 
                        cs.trainer AS trainer_name,
                        c.id AS customer_id,
                        c.name AS customer_name,
                        c.email AS customer_email,
                        c.phone AS customer_phone,
                        cs.sport_name,
                        cs.training_days,
                        cs.subscription_type,
                        cs.subscription_duration,
                        cs.number_of_sessions,
                        cs.created_at,
                        cs.updated_at
                    FROM customer_sports cs
                    JOIN customers c ON cs.customer_id = c.id";
            
            $whereConditions = [];
            $params = [];
            $types = "";
            
            // Build WHERE conditions
            $this->buildWhereConditions($filters, $whereConditions, $params, $types);
            
            if (!empty($whereConditions)) {
                $sql .= " WHERE " . implode(" AND ", $whereConditions);
            }
            
            $sql .= " ORDER BY cs.trainer, cs.sport_name, c.name";
            
            // Add LIMIT and OFFSET
            if ($limit !== null) {
                $sql .= " LIMIT ?";
                $params[] = $limit;
                $types .= "i";
                
                if ($offset > 0) {
                    $sql .= " OFFSET ?";
                    $params[] = $offset;
                    $types .= "i";
                }
            }
            
            $stmt = $this->conn->prepare($sql);
            
            if (!$stmt) {
                throw new Exception("Failed to prepare statement: " . $this->conn->error);
            }
            
            if (!empty($params)) {
                $stmt->bind_param($types, ...$params);
            }
            
            if (!$stmt->execute()) {
                throw new Exception("Failed to execute query: " . $stmt->error);
            }
            
            $result = $stmt->get_result();
            $trainees = [];
            
            while ($row = $result->fetch_assoc()) {
                $trainees[] = $this->formatTraineeData($row);
            }
            
            $stmt->close();
            return $trainees;
            
        } catch (Exception $e) {
            error_log("Error in getTrainerTrainees: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Get statistics about trainers and trainees
     */
    public function getStatistics() {
        try {
            $stats = [];
            
            // Total trainers
            $sql = "SELECT COUNT(DISTINCT trainer) as total_trainers FROM customer_sports";
            $result = $this->conn->query($sql);
            if ($result) {
                $row = $result->fetch_assoc();
                $stats['total_trainers'] = (int)$row['total_trainers'];
            }
            
            // Total trainees
            $sql = "SELECT COUNT(DISTINCT customer_id) as total_trainees FROM customer_sports";
            $result = $this->conn->query($sql);
            if ($result) {
                $row = $result->fetch_assoc();
                $stats['total_trainees'] = (int)$row['total_trainees'];
            }
            
            // Total sports
            $sql = "SELECT COUNT(DISTINCT sport_name) as total_sports FROM customer_sports";
            $result = $this->conn->query($sql);
            if ($result) {
                $row = $result->fetch_assoc();
                $stats['total_sports'] = (int)$row['total_sports'];
            }
            
            return $stats;
            
        } catch (Exception $e) {
            error_log("Error in getStatistics: " . $e->getMessage());
            throw $e;
        }
    }
    
    private function buildWhereConditions($filters, &$whereConditions, &$params, &$types) {
        $filterMap = [
            'trainer_name' => ['column' => 'cs.trainer', 'type' => 's', 'operator' => 'LIKE'],
            'sport_name' => ['column' => 'cs.sport_name', 'type' => 's', 'operator' => 'LIKE'],
            'customer_id' => ['column' => 'c.id', 'type' => 'i', 'operator' => '='],
            'customer_name' => ['column' => 'c.name', 'type' => 's', 'operator' => 'LIKE'],
            'subscription_type' => ['column' => 'cs.subscription_type', 'type' => 's', 'operator' => '=']
        ];
        
        foreach ($filters as $key => $value) {
            if (isset($filterMap[$key]) && !empty($value)) {
                $config = $filterMap[$key];
                $whereConditions[] = $config['column'] . " " . $config['operator'] . " ?";
                
                if ($config['operator'] === 'LIKE') {
                    $params[] = "%" . $value . "%";
                } else {
                    $params[] = $value;
                }
                
                $types .= $config['type'];
            }
        }
    }
    
    private function formatTraineeData($row) {
        return [
            "trainer_name" => $row['trainer_name'],
            "customer_id" => (int)$row['customer_id'],
            "customer_name" => $row['customer_name'],
            "customer_email" => $row['customer_email'] ?? null,
            "customer_phone" => $row['customer_phone'] ?? null,
            "sport_name" => $row['sport_name'],
            "training_days" => $row['training_days'],
            "subscription_type" => $row['subscription_type'],
            "subscription_duration" => $row['subscription_duration'],
            "number_of_sessions" => (int)$row['number_of_sessions'],
            "created_at" => $row['created_at'] ?? null,
            "updated_at" => $row['updated_at'] ?? null
        ];
    }
}

// Main execution
try {
    // Only allow GET requests
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        throw new Exception("Method not allowed", 405);
    }

    // Get database connection
    $dbManager = DatabaseManager::getInstance();
    $conn = $dbManager->getConnection();

    // Initialize service
    $service = new TrainerTraineesService($conn);

    // Validate input
    $validated = InputValidator::validateAndSanitize($_GET);

    // Check for special endpoints
    if (isset($_GET['action'])) {
        switch ($_GET['action']) {
            case 'stats':
                $data = $service->getStatistics();
                $message = "Statistics retrieved successfully";
                break;
            default:
                throw new InvalidArgumentException("Invalid action parameter");
        }
    } else {
        // Get regular trainer-trainee data
        $data = $service->getTrainerTrainees(
            $validated['filters'],
            $validated['limit'],
            $validated['offset']
        );
        $message = "Trainer-trainee data retrieved successfully";
    }

    // Prepare metadata
    $meta = [
        'filters_applied' => $validated['filters'],
        'total_results' => count($data)
    ];

    if ($validated['limit'] !== null) {
        $meta['pagination'] = [
            'limit' => $validated['limit'],
            'offset' => $validated['offset']
        ];
    }

    // Return success response
    echo json_encode(
        ApiResponse::success($data, $message, $meta),
        JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE
    );

} catch (InvalidArgumentException $e) {
    echo json_encode(ApiResponse::error($e->getMessage(), 400), JSON_PRETTY_PRINT);
} catch (Exception $e) {
    $message = "An error occurred while processing your request";
    $details = Config::isDebug() ? $e->getMessage() : null;

    error_log("API Error: " . $e->getMessage());

    echo json_encode(ApiResponse::error($message, 500, $details), JSON_PRETTY_PRINT);
} finally {
    // Close database connection
    if (isset($dbManager)) {
        $dbManager->close();
    }
}
?>
