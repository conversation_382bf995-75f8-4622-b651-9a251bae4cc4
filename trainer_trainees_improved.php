<?php
/**
 * Trainer Trainees API Endpoint
 * 
 * Retrieves trainer-trainee relationships with customer and sport details
 * 
 * <AUTHOR> Name
 * @version 1.1
 */

// Error reporting configuration
ini_set('display_errors', 0); // Disable in production
ini_set('display_startup_errors', 0);
error_reporting(E_ALL);

// CORS and Content-Type headers
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With");
header("Content-Type: application/json; charset=UTF-8");

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

/**
 * Database Configuration Class
 */
class DatabaseConfig {
    private const HOST = "localhost";
    private const USER = "u709340751_erp";
    private const PASS = "1234567890CodePoint";
    private const DB = "u709340751_erp_final";
    
    public static function getConnection() {
        try {
            $conn = new mysqli(self::HOST, self::USER, self::PASS, self::DB);
            
            if ($conn->connect_error) {
                throw new Exception("Database connection failed: " . $conn->connect_error);
            }
            
            // Set charset to prevent character encoding issues
            $conn->set_charset("utf8mb4");
            
            return $conn;
        } catch (Exception $e) {
            error_log("Database connection error: " . $e->getMessage());
            throw $e;
        }
    }
}

/**
 * API Response Handler Class
 */
class ApiResponse {
    public static function success($data, $message = "Success") {
        return [
            "status" => "success",
            "message" => $message,
            "data" => $data,
            "timestamp" => date('Y-m-d H:i:s'),
            "count" => is_array($data) ? count($data) : 1
        ];
    }
    
    public static function error($message, $code = 500, $details = null) {
        http_response_code($code);
        $response = [
            "status" => "error",
            "message" => $message,
            "timestamp" => date('Y-m-d H:i:s'),
            "error_code" => $code
        ];
        
        if ($details && ini_get('display_errors')) {
            $response["details"] = $details;
        }
        
        return $response;
    }
}

/**
 * Trainer Trainees Service Class
 */
class TrainerTraineesService {
    private $conn;
    
    public function __construct($connection) {
        $this->conn = $connection;
    }
    
    /**
     * Get all trainer-trainee relationships
     * 
     * @param array $filters Optional filters (trainer_name, sport_name, etc.)
     * @return array
     */
    public function getTrainerTrainees($filters = []) {
        try {
            $sql = "SELECT 
                        cs.trainer AS trainer_name,
                        c.id AS customer_id,
                        c.name AS customer_name,
                        cs.sport_name,
                        cs.training_days,
                        cs.subscription_type,
                        cs.subscription_duration,
                        cs.number_of_sessions,
                        cs.created_at,
                        cs.updated_at
                    FROM customer_sports cs
                    JOIN customers c ON cs.customer_id = c.id";
            
            $whereConditions = [];
            $params = [];
            $types = "";
            
            // Add filters if provided
            if (!empty($filters['trainer_name'])) {
                $whereConditions[] = "cs.trainer LIKE ?";
                $params[] = "%" . $filters['trainer_name'] . "%";
                $types .= "s";
            }
            
            if (!empty($filters['sport_name'])) {
                $whereConditions[] = "cs.sport_name LIKE ?";
                $params[] = "%" . $filters['sport_name'] . "%";
                $types .= "s";
            }
            
            if (!empty($filters['customer_id'])) {
                $whereConditions[] = "c.id = ?";
                $params[] = $filters['customer_id'];
                $types .= "i";
            }
            
            // Add WHERE clause if filters exist
            if (!empty($whereConditions)) {
                $sql .= " WHERE " . implode(" AND ", $whereConditions);
            }
            
            $sql .= " ORDER BY cs.trainer, cs.sport_name, c.name";
            
            $stmt = $this->conn->prepare($sql);
            
            if (!$stmt) {
                throw new Exception("Failed to prepare statement: " . $this->conn->error);
            }
            
            // Bind parameters if any
            if (!empty($params)) {
                $stmt->bind_param($types, ...$params);
            }
            
            if (!$stmt->execute()) {
                throw new Exception("Failed to execute query: " . $stmt->error);
            }
            
            $result = $stmt->get_result();
            $trainees = [];
            
            while ($row = $result->fetch_assoc()) {
                $trainees[] = [
                    "trainer_name" => $row['trainer_name'],
                    "customer_id" => (int)$row['customer_id'],
                    "customer_name" => $row['customer_name'],
                    "sport_name" => $row['sport_name'],
                    "training_days" => $row['training_days'],
                    "subscription_type" => $row['subscription_type'],
                    "subscription_duration" => $row['subscription_duration'],
                    "number_of_sessions" => (int)$row['number_of_sessions'],
                    "created_at" => $row['created_at'] ?? null,
                    "updated_at" => $row['updated_at'] ?? null
                ];
            }
            
            $stmt->close();
            return $trainees;
            
        } catch (Exception $e) {
            error_log("Error in getTrainerTrainees: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Get grouped trainer-trainee data
     * 
     * @return array
     */
    public function getGroupedByTrainer() {
        try {
            $trainees = $this->getTrainerTrainees();
            $grouped = [];
            
            foreach ($trainees as $trainee) {
                $trainerName = $trainee['trainer_name'];
                
                if (!isset($grouped[$trainerName])) {
                    $grouped[$trainerName] = [
                        "trainer_name" => $trainerName,
                        "total_trainees" => 0,
                        "sports" => [],
                        "trainees" => []
                    ];
                }
                
                $grouped[$trainerName]['total_trainees']++;
                $grouped[$trainerName]['trainees'][] = $trainee;
                
                // Track unique sports
                if (!in_array($trainee['sport_name'], $grouped[$trainerName]['sports'])) {
                    $grouped[$trainerName]['sports'][] = $trainee['sport_name'];
                }
            }
            
            return array_values($grouped);
            
        } catch (Exception $e) {
            error_log("Error in getGroupedByTrainer: " . $e->getMessage());
            throw $e;
        }
    }
}

/**
 * Input Validation Class
 */
class InputValidator {
    public static function validateFilters($input) {
        $allowedFilters = ['trainer_name', 'sport_name', 'customer_id'];
        $validatedFilters = [];
        
        foreach ($allowedFilters as $filter) {
            if (isset($input[$filter]) && !empty(trim($input[$filter]))) {
                $value = trim($input[$filter]);
                
                // Specific validation for customer_id
                if ($filter === 'customer_id') {
                    if (!is_numeric($value) || $value <= 0) {
                        throw new InvalidArgumentException("Invalid customer_id: must be a positive integer");
                    }
                    $validatedFilters[$filter] = (int)$value;
                } else {
                    // Basic sanitization for string filters
                    $validatedFilters[$filter] = htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
                }
            }
        }
        
        return $validatedFilters;
    }
}

// Main execution
try {
    // Only allow GET requests for this endpoint
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        throw new Exception("Method not allowed", 405);
    }
    
    // Get database connection
    $conn = DatabaseConfig::getConnection();
    
    // Initialize service
    $service = new TrainerTraineesService($conn);
    
    // Validate and get filters from query parameters
    $filters = InputValidator::validateFilters($_GET);
    
    // Check if grouped view is requested
    $grouped = isset($_GET['grouped']) && $_GET['grouped'] === 'true';
    
    // Get data
    if ($grouped) {
        $data = $service->getGroupedByTrainer();
        $message = "Trainer-trainee data retrieved successfully (grouped by trainer)";
    } else {
        $data = $service->getTrainerTrainees($filters);
        $message = "Trainer-trainee data retrieved successfully";
    }
    
    // Return success response
    echo json_encode(ApiResponse::success($data, $message), JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    
} catch (InvalidArgumentException $e) {
    echo json_encode(ApiResponse::error($e->getMessage(), 400), JSON_PRETTY_PRINT);
} catch (Exception $e) {
    $message = "An error occurred while processing your request";
    $details = ini_get('display_errors') ? $e->getMessage() : null;
    
    // Log the actual error
    error_log("API Error: " . $e->getMessage());
    
    echo json_encode(ApiResponse::error($message, 500, $details), JSON_PRETTY_PRINT);
} finally {
    // Close database connection
    if (isset($conn) && $conn instanceof mysqli) {
        $conn->close();
    }
}
?>
