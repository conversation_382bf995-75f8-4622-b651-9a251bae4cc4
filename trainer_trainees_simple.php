<?php
/**
 * Simple Trainer Trainees API - Debugging Version
 * Based on your original code with improved error handling
 */

// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Headers: *");
header("Content-Type: application/json; charset=UTF-8");

// Database configuration
$host = "localhost";
$user = "u709340751_erp";
$pass = "1234567890CodePoint";
$db = "u709340751_erp_final";

try {
    // Test database connection
    $conn = new mysqli($host, $user, $pass, $db);
    
    if ($conn->connect_error) {
        throw new Exception("Connection failed: " . $conn->connect_error);
    }
    
    // Set charset
    $conn->set_charset("utf8mb4");
    
    // Build the SQL query
    $sql = "SELECT 
                cs.trainer AS trainer_name,
                c.id AS customer_id,
                c.name AS customer_name,
                cs.sport_name,
                cs.training_days,
                cs.subscription_type,
                cs.subscription_duration,
                cs.number_of_sessions
            FROM customer_sports cs
            JOIN customers c ON cs.customer_id = c.id
            ORDER BY cs.trainer, cs.sport_name";
    
    $result = $conn->query($sql);
    
    if (!$result) {
        throw new Exception("Query failed: " . $conn->error);
    }
    
    $trainees = [];
    
    while ($row = $result->fetch_assoc()) {
        $trainees[] = [
            "trainer_name" => $row['trainer_name'],
            "customer_id" => (int)$row['customer_id'],
            "customer_name" => $row['customer_name'],
            "sport_name" => $row['sport_name'],
            "training_days" => $row['training_days'],
            "subscription_type" => $row['subscription_type'],
            "subscription_duration" => $row['subscription_duration'],
            "number_of_sessions" => (int)$row['number_of_sessions']
        ];
    }
    
    // Return success response
    $response = [
        "status" => "success",
        "message" => "Data retrieved successfully",
        "data" => $trainees,
        "count" => count($trainees),
        "timestamp" => date('Y-m-d H:i:s')
    ];
    
    echo json_encode($response, JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    // Return error response
    $response = [
        "status" => "error",
        "message" => $e->getMessage(),
        "timestamp" => date('Y-m-d H:i:s'),
        "error_code" => 500
    ];
    
    http_response_code(500);
    echo json_encode($response, JSON_PRETTY_PRINT);
    
} finally {
    // Close connection if it exists
    if (isset($conn) && $conn instanceof mysqli) {
        $conn->close();
    }
}
?>
